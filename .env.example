# MoviePilot 环境变量配置示例
# 复制此文件为 .env 并填入实际值

# MoviePilot 配置
MOVIEPILOT_SUPERUSER=admin
MOVIEPILOT_GITHUB_TOKEN=****************************************
MOVIEPILOT_API_TOKEN=AU2pOSFIAdUiLKFM9TU88w

# Transmission 配置
TRANSMISSION_USER=admin
TRANSMISSION_PASS=your_secure_password_here

# 时区设置
TZ=Asia/Shanghai

# 用户权限设置
PUID=1000
PGID=1000
UMASK=022

# 代理设置（可选）
GITHUB_PROXY=https://ghfast.top/
PIP_PROXY=https://pypi.mirrors.ustc.edu.cn/simple

# TMDB 配置
TMDB_API_DOMAIN=tmdb.movie-pilot.org
TMDB_IMAGE_DOMAIN=static-mdb.v.geilijiasu.com
