# MoviePilot 媒体管理系统

> 基于 Docker Compose 的全自动媒体下载、管理和播放解决方案
> 
> 维护者: **alxxxxla**

## 📋 目录

- [系统概述](#系统概述)
- [功能特性](#功能特性)
- [系统架构](#系统架构)
- [快速开始](#快速开始)
- [配置说明](#配置说明)
- [使用指南](#使用指南)
- [维护管理](#维护管理)
- [故障排除](#故障排除)
- [更新日志](#更新日志)

## 🎯 系统概述

MoviePilot 是一个集成化的媒体管理系统，包含以下核心组件：

- **MoviePilot v2**: 智能媒体搜索和自动下载管理
- **Emby**: 强大的媒体服务器，支持多设备流媒体播放
- **qBittorrent**: 高效的 BT 下载客户端
- **Transmission**: 备用 BT 下载客户端
- **ChineseSubFinder**: 自动中文字幕下载和匹配
- **Watchtower**: 容器自动更新服务

## ✨ 功能特性

### 🎬 媒体管理
- 自动搜索和下载电影、电视剧
- 智能分类整理（华语/外语电影、国产/欧美/日韩剧等）
- 自动重命名和目录结构优化
- 支持多种视频格式和质量选择

### 🔍 智能搜索
- 集成多个资源站点
- 支持 RSS 订阅和自动更新
- 智能去重和质量筛选
- 自定义搜索规则和过滤条件

### 📱 多设备支持
- Web 管理界面
- 移动端适配
- 多用户权限管理
- 远程访问支持

### 🌐 本地化优化
- 中文界面和配置
- 国内镜像源加速
- 中文字幕自动下载
- 符合国内使用习惯的分类规则

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MoviePilot    │    │      Emby       │    │  ChineseSub     │
│   (管理核心)     │    │   (媒体服务器)   │    │   (字幕下载)     │
│   Port: 3000    │    │   Port: 8096    │    │  Port: 19035    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
         ┌───────────────────────┼───────────────────────┐
         │                       │                       │
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│  qBittorrent    │    │  Transmission   │    │   Watchtower    │
│  (主下载器)      │    │   (备用下载器)   │    │   (自动更新)     │
│   Port: 8099    │    │   Port: 9091    │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 快速开始

### 环境要求

- Docker 20.10+
- Docker Compose 2.0+
- 可用磁盘空间 50GB+
- 内存 4GB+

### 安装步骤

1. **克隆项目**
   ```bash
   git clone <repository-url>
   cd moviepilot
   ```

2. **配置环境变量**
   ```bash
   cp .env.example .env
   # 编辑 .env 文件，填入必要的配置信息
   nano .env
   ```

3. **启动服务**
   ```bash
   # 使用优化后的配置
   docker-compose -f docker-compose.optimized.yml up -d
   
   # 或使用原始配置（不推荐）
   docker-compose up -d
   ```

4. **验证安装**
   ```bash
   # 检查服务状态
   docker-compose ps
   
   # 运行系统监控
   bash scripts/monitor.sh
   ```

### 首次配置

1. **访问 MoviePilot**: http://localhost:3000
   - 默认用户名: admin
   - 密码: 在首次启动时设置

2. **访问 Emby**: http://localhost:8096
   - 按照向导完成初始设置
   - 添加媒体库路径: `/media`

3. **配置下载器**
   - qBittorrent: http://localhost:8099
   - Transmission: http://localhost:9091

## ⚙️ 配置说明

### 环境变量配置

| 变量名 | 说明 | 默认值 |
|--------|------|--------|
| `MOVIEPILOT_SUPERUSER` | MoviePilot 管理员用户名 | admin |
| `MOVIEPILOT_GITHUB_TOKEN` | GitHub Token（用于插件更新） | - |
| `MOVIEPILOT_API_TOKEN` | API 访问令牌 | - |
| `TRANSMISSION_USER` | Transmission 用户名 | admin |
| `TRANSMISSION_PASS` | Transmission 密码 | - |
| `TZ` | 时区设置 | Asia/Shanghai |
| `PUID/PGID` | 用户/组 ID | 1000 |

### 目录结构

```
moviepilot/
├── docker-compose.yml          # Docker 编排文件
├── docker-compose.optimized.yml # 优化后的编排文件
├── .env                        # 环境变量配置
├── .env.example               # 环境变量模板
├── README.md                  # 项目说明文档
├── scripts/                   # 管理脚本
│   ├── optimize.sh           # 系统优化脚本
│   └── monitor.sh            # 监控脚本
├── media/                     # 媒体文件目录
│   ├── downloads/            # 下载目录
│   └── links/               # 媒体库目录
│       ├── 华语电影/
│       ├── 外语电影/
│       ├── 国产剧/
│       ├── 欧美剧/
│       ├── 动画电影/
│       └── 国漫/
├── moviepilot/config/         # MoviePilot 配置
├── emby/config/              # Emby 配置
├── qbittorrent/config/       # qBittorrent 配置
├── transmission/config/      # Transmission 配置
└── chinesesubfinder/config/  # 字幕下载器配置
```

## 📖 使用指南

### 添加媒体内容

1. **自动搜索**
   - 在 MoviePilot 中搜索想要的电影或电视剧
   - 系统会自动匹配最佳资源并开始下载
   - 下载完成后自动整理到对应分类目录

2. **RSS 订阅**
   - 配置 RSS 源实现自动追剧
   - 支持自定义过滤规则
   - 新剧集自动下载和更新

3. **手动管理**
   - 通过下载器直接添加种子
   - 手动整理和重命名文件
   - 自定义分类规则

### 媒体播放

1. **通过 Emby 播放**
   - 支持多种设备和客户端
   - 自动转码和字幕匹配
   - 观看进度同步

2. **直接文件访问**
   - 媒体文件存储在 `media/links/` 目录
   - 支持网络共享访问
   - 兼容各种播放器

## 🔧 维护管理

### 系统优化

```bash
# 执行完整优化
bash scripts/optimize.sh

# 单独执行特定优化
bash scripts/optimize.sh docker    # 清理 Docker 缓存
bash scripts/optimize.sh logs      # 清理日志文件
bash scripts/optimize.sh db        # 优化数据库
bash scripts/optimize.sh update    # 更新镜像
```

### 系统监控

```bash
# 执行一次监控检查
bash scripts/monitor.sh

# 生成健康报告
bash scripts/monitor.sh report

# 自动修复问题
bash scripts/monitor.sh fix

# 连续监控模式
bash scripts/monitor.sh continuous
```

### 备份和恢复

```bash
# 备份配置文件
tar -czf backup_$(date +%Y%m%d).tar.gz */config/

# 恢复配置文件
tar -xzf backup_YYYYMMDD.tar.gz
```

## 🔍 故障排除

### 常见问题

1. **服务无法启动**
   - 检查端口占用: `netstat -tlnp | grep :3000`
   - 查看容器日志: `docker-compose logs moviepilot`
   - 验证配置文件: `docker-compose config`

2. **下载速度慢**
   - 检查网络连接
   - 优化下载器设置
   - 更换资源站点

3. **字幕匹配失败**
   - 检查 ChineseSubFinder 配置
   - 验证媒体文件命名规范
   - 手动下载字幕文件

4. **磁盘空间不足**
   - 运行清理脚本: `bash scripts/optimize.sh`
   - 删除旧的下载文件
   - 调整媒体质量设置

### 日志查看

```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs moviepilot
docker-compose logs emby

# 实时跟踪日志
docker-compose logs -f moviepilot
```

## 🔐 安全建议

### 重要安全提醒

1. **修改默认密码**: 首次部署后立即修改所有默认密码
2. **环境变量保护**: 确保 `.env` 文件不被提交到版本控制
3. **网络访问控制**: 建议使用防火墙限制外部访问
4. **定期更新**: 保持所有组件为最新版本

### 网络安全配置

```bash
# 仅允许本地访问（推荐）
# 在 docker-compose.yml 中修改端口映射
ports:
  - "127.0.0.1:3000:3000"  # 仅本地访问
  - "127.0.0.1:8096:8096"  # 仅本地访问
```

## 🎛️ 高级配置

### 自定义分类规则

编辑 `moviepilot/config/category.yaml` 文件来自定义媒体分类：

```yaml
movie:
  4K电影:
    # 匹配4K分辨率
    resolution: '2160p'
  经典电影:
    # 匹配发行年份
    release_year: '1990-2010'
```

### 性能调优

1. **内存优化**
   ```bash
   # 在 .env 文件中设置
   BIG_MEMORY_MODE=True
   DB_WAL_ENABLE=True
   ```

2. **下载优化**
   - 调整 qBittorrent 连接数限制
   - 配置合适的上传/下载速度限制
   - 启用 DHT 和 PEX

3. **存储优化**
   - 使用 SSD 存储配置文件
   - 定期清理下载缓存
   - 配置自动删除已完成的种子

## 📊 监控和告警

### Prometheus 监控集成

```yaml
# 添加到 docker-compose.yml
prometheus:
  image: prom/prometheus
  ports:
    - "9090:9090"
  volumes:
    - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml
```

### 日志管理

```bash
# 配置日志轮转
# 在 docker-compose.yml 中添加
logging:
  driver: "json-file"
  options:
    max-size: "10m"
    max-file: "3"
```

## 🔄 自动化脚本

### 定时任务设置

```bash
# 添加到 crontab
# 每天凌晨2点执行系统优化
0 2 * * * /path/to/moviepilot/scripts/optimize.sh all

# 每小时执行一次监控检查
0 * * * * /path/to/moviepilot/scripts/monitor.sh

# 每周日凌晨3点生成健康报告
0 3 * * 0 /path/to/moviepilot/scripts/monitor.sh report
```

### 自动备份脚本

```bash
#!/bin/bash
# backup.sh - 自动备份脚本

BACKUP_DIR="/backup/moviepilot"
DATE=$(date +%Y%m%d_%H%M%S)

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 备份配置文件
tar -czf "$BACKUP_DIR/config_$DATE.tar.gz" */config/

# 备份数据库
cp moviepilot/config/user.db "$BACKUP_DIR/user_db_$DATE.db"

# 清理30天前的备份
find "$BACKUP_DIR" -name "*.tar.gz" -mtime +30 -delete
find "$BACKUP_DIR" -name "*.db" -mtime +30 -delete
```

## 📝 更新日志

### v2.0.0 (2025-07-30)
- ✨ 优化 Docker Compose 配置，移除不必要的特权模式
- 🔒 增强安全性配置，使用环境变量管理敏感信息
- 📊 添加系统监控和优化脚本，支持自动化运维
- 📚 完善项目文档，添加详细的使用指南
- 🐛 修复已知安全隐患，包括敏感信息泄露问题
- 🚀 添加性能优化建议和最佳实践
- 🔧 提供自动化维护脚本和监控工具

### 贡献指南

欢迎提交 Issue 和 Pull Request！请遵循以下规范：

1. **代码规范**: 遵循项目现有的代码风格
2. **提交信息**: 使用规范的 commit message 格式
3. **测试**: 确保更改不会破坏现有功能
4. **文档**: 更新相关文档说明

#### 提交流程

1. Fork 本项目
2. 创建功能分支: `git checkout -b feature/new-feature`
3. 提交更改: `git commit -am 'feat: Add new feature'`
4. 推送分支: `git push origin feature/new-feature`
5. 提交 Pull Request，指定 @alxxxxla 为审核者

#### Commit Message 规范

```
<type>(<scope>): <subject>

<body>

<footer>
```

类型说明：
- `feat`: 新功能
- `fix`: 修复问题
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

## 📞 支持和反馈

### 获取帮助

1. **查看文档**: 首先查阅本 README 和相关配置文件
2. **搜索 Issues**: 在项目 Issues 中搜索类似问题
3. **提交 Issue**: 详细描述问题和环境信息
4. **社区讨论**: 参与项目讨论区

### 问题报告模板

```markdown
**问题描述**
简要描述遇到的问题

**环境信息**
- 操作系统:
- Docker 版本:
- Docker Compose 版本:
- 项目版本:

**重现步骤**
1.
2.
3.

**期望行为**
描述期望的正确行为

**实际行为**
描述实际发生的情况

**日志信息**
```
相关的错误日志
```

**额外信息**
其他可能有用的信息
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢以下开源项目和贡献者：

- [MoviePilot](https://github.com/jxxghp/MoviePilot) - 核心媒体管理系统
- [Emby](https://emby.media/) - 强大的媒体服务器
- [qBittorrent](https://www.qbittorrent.org/) - 优秀的 BT 下载客户端
- [Transmission](https://transmissionbt.com/) - 轻量级 BT 客户端
- [ChineseSubFinder](https://github.com/allanpk716/ChineseSubFinder) - 中文字幕自动下载器
- [Watchtower](https://github.com/containrrr/watchtower) - 容器自动更新工具

特别感谢所有为项目贡献代码、文档和反馈的社区成员！

---

**项目维护者**: alxxxxla
**最后更新**: 2025-07-30
**文档版本**: v2.0.0
