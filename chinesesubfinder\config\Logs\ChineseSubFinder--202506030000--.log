[INFO]: 2025-06-03 20:10:17 - LiteMode is true
[INFO]: 2025-06-03 20:10:17 - ChineseSubFinder Version: v0.55.3 Lite
[INFO]: 2025-06-03 20:10:17 - Reload Log Settings, level = Info
[INFO]: 2025-06-03 20:10:17 - Speed Dev Mode is Off
[INFO]: 2025-06-03 20:10:17 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-03 20:10:17 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-03 20:10:17 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-03 20:10:17 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-03 20:10:17 - PreDownloadProcess.Init() End
[INFO]: 2025-06-03 20:10:17 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-03 20:10:17 - UseHttpProxy = false
[ERROR]: 2025-06-03 20:10:22 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-03 20:10:22 - Check Sub Supplier Start...
[INFO]: 2025-06-03 20:10:22 - xunlei Check Alive = true, Speed = 109 ms
[ERROR]: 2025-06-03 20:10:22 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on ************:53: no such host
[WARNING]: 2025-06-03 20:10:22 - a4k Check Alive = false
[INFO]: 2025-06-03 20:10:24 - shooter Check Alive = true, Speed = 1412 ms
[INFO]: 2025-06-03 20:10:24 - Alive Supplier: xunlei
[INFO]: 2025-06-03 20:10:24 - Alive Supplier: shooter
[INFO]: 2025-06-03 20:10:24 - Check Sub Supplier End
[INFO]: 2025-06-03 20:10:24 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-03 20:10:24 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-03 20:10:24 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-03 20:10:24 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-03 20:10:24 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-03 20:10:24 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-03 20:10:24 - PreDownloadProcess.Check() End
[INFO]: 2025-06-03 20:10:24 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-03 20:10:24 - Download.SupplierCheck() End
[INFO]: 2025-06-03 20:10:25 - Tmdb Api is Alive 382
[INFO]: 2025-06-03 20:10:25 - Try Start Http Server At Port 19035
[INFO]: 2025-06-03 20:10:25 - Setup is Done
[INFO]: 2025-06-03 20:10:25 - PreJob Will Start...
[INFO]: 2025-06-03 20:10:25 - PreJob.HotFix() Start...
[INFO]: 2025-06-03 20:10:25 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-03 20:10:25 - PreJob.HotFix() End
[INFO]: 2025-06-03 20:10:25 - PreJob.ChangeSubNameFormat() Start...
[INFO]: 2025-06-03 20:10:25 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-03 20:10:25 - DesSubFormatter == LateTimeSubFormatter then skip process
[INFO]: 2025-06-03 20:10:25 - PreJob.ChangeSubNameFormat() End
[INFO]: 2025-06-03 20:10:25 - PreJob.Wait() Done.
[INFO]: 2025-06-03 20:10:25 - Setup is Done
[INFO]: 2025-06-03 20:10:25 - RunAtStartup: false, so will not Run At Startup
[INFO]: 2025-06-03 20:10:25 - CronHelper Start...
[INFO]: 2025-06-03 20:10:25 - Next Sub Scan Will Process At: 2025-06-04 20:08:00
[INFO]: 2025-06-03 22:23:21 - LiteMode is true
[INFO]: 2025-06-03 22:23:21 - ChineseSubFinder Version: v0.55.3 Lite
[INFO]: 2025-06-03 22:23:21 - Reload Log Settings, level = Info
[INFO]: 2025-06-03 22:23:21 - Speed Dev Mode is Off
[INFO]: 2025-06-03 22:23:21 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-03 22:23:21 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-03 22:23:21 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-03 22:23:21 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-03 22:23:21 - PreDownloadProcess.Init() End
[INFO]: 2025-06-03 22:23:21 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-03 22:23:21 - UseHttpProxy = false
[INFO]: 2025-06-03 22:23:22 - UrlConnectednessTest Target Site https://baidu.com Speed: 1275 ms, Status: true
[INFO]: 2025-06-03 22:23:22 - Check Sub Supplier Start...
[INFO]: 2025-06-03 22:23:22 - xunlei Check Alive = true, Speed = 84 ms
[ERROR]: 2025-06-03 22:23:22 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-03 22:23:22 - a4k Check Alive = false
[INFO]: 2025-06-03 22:23:25 - shooter Check Alive = true, Speed = 2975 ms
[INFO]: 2025-06-03 22:23:25 - Alive Supplier: xunlei
[INFO]: 2025-06-03 22:23:25 - Alive Supplier: shooter
[INFO]: 2025-06-03 22:23:25 - Check Sub Supplier End
[INFO]: 2025-06-03 22:23:25 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-03 22:23:25 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-03 22:23:25 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-03 22:23:25 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-03 22:23:25 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-03 22:23:25 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-03 22:23:25 - PreDownloadProcess.Check() End
[INFO]: 2025-06-03 22:23:25 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-03 22:23:25 - Download.SupplierCheck() End
[INFO]: 2025-06-03 22:23:26 - Tmdb Api is Alive 382
[INFO]: 2025-06-03 22:23:26 - Try Start Http Server At Port 19035
[INFO]: 2025-06-03 22:23:26 - Setup is Done
[INFO]: 2025-06-03 22:23:26 - PreJob Will Start...
[INFO]: 2025-06-03 22:23:26 - PreJob.HotFix() Start...
[INFO]: 2025-06-03 22:23:26 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-03 22:23:26 - PreJob.HotFix() End
[INFO]: 2025-06-03 22:23:26 - PreJob.ChangeSubNameFormat() Start...
[INFO]: 2025-06-03 22:23:26 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-03 22:23:26 - DesSubFormatter == LateTimeSubFormatter then skip process
[INFO]: 2025-06-03 22:23:26 - PreJob.ChangeSubNameFormat() End
[INFO]: 2025-06-03 22:23:26 - PreJob.Wait() Done.
[INFO]: 2025-06-03 22:23:26 - Setup is Done
[INFO]: 2025-06-03 22:23:26 - RunAtStartup: false, so will not Run At Startup
[INFO]: 2025-06-03 22:23:26 - CronHelper Start...
[INFO]: 2025-06-03 22:23:26 - Next Sub Scan Will Process At: 2025-06-04 20:08:00
[INFO]: 2025-06-03 23:23:26 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-03 23:23:26 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-03 23:23:26 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-03 23:23:26 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-03 23:23:26 - PreDownloadProcess.Init() End
[INFO]: 2025-06-03 23:23:26 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-03 23:23:26 - UseHttpProxy = false
[ERROR]: 2025-06-03 23:23:31 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-03 23:23:31 - Check Sub Supplier Start...
[INFO]: 2025-06-03 23:23:31 - xunlei Check Alive = true, Speed = 64 ms
[ERROR]: 2025-06-03 23:23:31 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-03 23:23:31 - a4k Check Alive = false
[INFO]: 2025-06-03 23:23:34 - shooter Check Alive = true, Speed = 3985 ms
[INFO]: 2025-06-03 23:23:34 - Alive Supplier: xunlei
[INFO]: 2025-06-03 23:23:34 - Alive Supplier: shooter
[INFO]: 2025-06-03 23:23:34 - Check Sub Supplier End
[INFO]: 2025-06-03 23:23:34 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-03 23:23:34 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-03 23:23:34 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-03 23:23:34 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-03 23:23:35 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-03 23:23:35 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-03 23:23:35 - PreDownloadProcess.Check() End
[INFO]: 2025-06-03 23:23:35 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-03 23:23:35 - Download.SupplierCheck() End
