[INFO]: 2025-06-04 00:23:26 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 00:23:26 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 00:23:26 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 00:23:26 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 00:23:26 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 00:23:26 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 00:23:26 - UseHttpProxy = false
[ERROR]: 2025-06-04 00:23:31 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-04 00:23:31 - Check Sub Supplier Start...
[INFO]: 2025-06-04 00:23:31 - xunlei Check Alive = true, Speed = 69 ms
[ERROR]: 2025-06-04 00:23:31 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 00:23:31 - a4k Check Alive = false
[INFO]: 2025-06-04 00:23:35 - shooter Check Alive = true, Speed = 4100 ms
[INFO]: 2025-06-04 00:23:35 - Alive Supplier: xunlei
[INFO]: 2025-06-04 00:23:35 - Alive Supplier: shooter
[INFO]: 2025-06-04 00:23:35 - Check Sub Supplier End
[INFO]: 2025-06-04 00:23:35 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 00:23:35 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 00:23:35 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 00:23:35 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 00:23:35 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 00:23:35 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 00:23:35 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 00:23:35 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 00:23:35 - Download.SupplierCheck() End
[INFO]: 2025-06-04 01:23:26 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 01:23:26 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 01:23:26 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 01:23:26 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 01:23:26 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 01:23:26 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 01:23:26 - UseHttpProxy = false
[INFO]: 2025-06-04 01:23:26 - UrlConnectednessTest Target Site https://baidu.com Speed: 176 ms, Status: true
[INFO]: 2025-06-04 01:23:26 - Check Sub Supplier Start...
[ERROR]: 2025-06-04 01:23:26 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 01:23:26 - a4k Check Alive = false
[INFO]: 2025-06-04 01:23:26 - xunlei Check Alive = true, Speed = 127 ms
[INFO]: 2025-06-04 01:23:26 - shooter Check Alive = true, Speed = 734 ms
[INFO]: 2025-06-04 01:23:26 - Alive Supplier: xunlei
[INFO]: 2025-06-04 01:23:26 - Alive Supplier: shooter
[INFO]: 2025-06-04 01:23:26 - Check Sub Supplier End
[INFO]: 2025-06-04 01:23:26 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 01:23:26 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 01:23:26 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 01:23:26 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 01:23:26 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 01:23:26 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 01:23:26 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 01:23:26 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 01:23:26 - Download.SupplierCheck() End
[INFO]: 2025-06-04 02:23:26 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 02:23:26 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 02:23:26 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 02:23:26 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 02:23:26 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 02:23:26 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 02:23:26 - UseHttpProxy = false
[ERROR]: 2025-06-04 02:23:31 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-04 02:23:31 - Check Sub Supplier Start...
[INFO]: 2025-06-04 02:23:31 - xunlei Check Alive = true, Speed = 81 ms
[ERROR]: 2025-06-04 02:23:31 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 02:23:31 - a4k Check Alive = false
[INFO]: 2025-06-04 02:23:31 - shooter Check Alive = true, Speed = 688 ms
[INFO]: 2025-06-04 02:23:31 - Alive Supplier: xunlei
[INFO]: 2025-06-04 02:23:31 - Alive Supplier: shooter
[INFO]: 2025-06-04 02:23:31 - Check Sub Supplier End
[INFO]: 2025-06-04 02:23:31 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 02:23:31 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 02:23:31 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 02:23:31 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 02:23:31 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 02:23:31 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 02:23:31 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 02:23:31 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 02:23:31 - Download.SupplierCheck() End
[INFO]: 2025-06-04 03:23:26 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 03:23:26 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 03:23:26 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 03:23:26 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 03:23:26 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 03:23:26 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 03:23:26 - UseHttpProxy = false
[ERROR]: 2025-06-04 03:23:31 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-04 03:23:31 - Check Sub Supplier Start...
[ERROR]: 2025-06-04 03:23:31 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 03:23:31 - a4k Check Alive = false
[INFO]: 2025-06-04 03:23:31 - xunlei Check Alive = true, Speed = 375 ms
[INFO]: 2025-06-04 03:23:31 - shooter Check Alive = true, Speed = 629 ms
[INFO]: 2025-06-04 03:23:31 - Alive Supplier: xunlei
[INFO]: 2025-06-04 03:23:31 - Alive Supplier: shooter
[INFO]: 2025-06-04 03:23:31 - Check Sub Supplier End
[INFO]: 2025-06-04 03:23:31 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 03:23:31 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 03:23:31 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 03:23:31 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 03:23:31 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 03:23:31 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 03:23:31 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 03:23:31 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 03:23:31 - Download.SupplierCheck() End
[INFO]: 2025-06-04 04:23:26 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 04:23:26 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 04:23:26 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 04:23:26 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 04:23:26 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 04:23:26 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 04:23:26 - UseHttpProxy = false
[INFO]: 2025-06-04 04:23:26 - UrlConnectednessTest Target Site https://baidu.com Speed: 184 ms, Status: true
[INFO]: 2025-06-04 04:23:26 - Check Sub Supplier Start...
[INFO]: 2025-06-04 04:23:26 - xunlei Check Alive = true, Speed = 66 ms
[ERROR]: 2025-06-04 04:23:26 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 04:23:26 - a4k Check Alive = false
[INFO]: 2025-06-04 04:23:26 - shooter Check Alive = true, Speed = 637 ms
[INFO]: 2025-06-04 04:23:26 - Alive Supplier: xunlei
[INFO]: 2025-06-04 04:23:26 - Alive Supplier: shooter
[INFO]: 2025-06-04 04:23:26 - Check Sub Supplier End
[INFO]: 2025-06-04 04:23:26 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 04:23:26 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 04:23:26 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 04:23:26 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 04:23:26 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 04:23:26 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 04:23:26 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 04:23:26 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 04:23:26 - Download.SupplierCheck() End
[INFO]: 2025-06-04 05:23:26 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 05:23:26 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 05:23:26 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 05:23:26 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 05:23:26 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 05:23:26 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 05:23:26 - UseHttpProxy = false
[INFO]: 2025-06-04 05:23:26 - UrlConnectednessTest Target Site https://baidu.com Speed: 205 ms, Status: true
[INFO]: 2025-06-04 05:23:26 - Check Sub Supplier Start...
[ERROR]: 2025-06-04 05:23:26 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 05:23:26 - a4k Check Alive = false
[INFO]: 2025-06-04 05:23:26 - xunlei Check Alive = true, Speed = 151 ms
[INFO]: 2025-06-04 05:23:26 - shooter Check Alive = true, Speed = 666 ms
[INFO]: 2025-06-04 05:23:26 - Alive Supplier: xunlei
[INFO]: 2025-06-04 05:23:26 - Alive Supplier: shooter
[INFO]: 2025-06-04 05:23:26 - Check Sub Supplier End
[INFO]: 2025-06-04 05:23:26 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 05:23:26 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 05:23:26 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 05:23:26 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 05:23:26 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 05:23:26 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 05:23:26 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 05:23:26 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 05:23:26 - Download.SupplierCheck() End
[INFO]: 2025-06-04 06:23:26 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 06:23:26 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 06:23:26 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 06:23:26 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 06:23:26 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 06:23:26 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 06:23:26 - UseHttpProxy = false
[ERROR]: 2025-06-04 06:23:31 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-04 06:23:31 - Check Sub Supplier Start...
[ERROR]: 2025-06-04 06:23:31 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 06:23:31 - a4k Check Alive = false
[INFO]: 2025-06-04 06:23:31 - xunlei Check Alive = true, Speed = 266 ms
[INFO]: 2025-06-04 06:23:31 - shooter Check Alive = true, Speed = 633 ms
[INFO]: 2025-06-04 06:23:31 - Alive Supplier: xunlei
[INFO]: 2025-06-04 06:23:31 - Alive Supplier: shooter
[INFO]: 2025-06-04 06:23:31 - Check Sub Supplier End
[INFO]: 2025-06-04 06:23:31 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 06:23:31 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 06:23:31 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 06:23:31 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 06:23:31 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 06:23:31 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 06:23:31 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 06:23:31 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 06:23:31 - Download.SupplierCheck() End
[INFO]: 2025-06-04 07:23:26 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 07:23:26 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 07:23:26 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 07:23:26 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 07:23:26 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 07:23:26 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 07:23:26 - UseHttpProxy = false
[ERROR]: 2025-06-04 07:23:31 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-04 07:23:31 - Check Sub Supplier Start...
[ERROR]: 2025-06-04 07:23:31 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 07:23:31 - a4k Check Alive = false
[INFO]: 2025-06-04 07:23:31 - xunlei Check Alive = true, Speed = 145 ms
[INFO]: 2025-06-04 07:23:31 - shooter Check Alive = true, Speed = 645 ms
[INFO]: 2025-06-04 07:23:31 - Alive Supplier: xunlei
[INFO]: 2025-06-04 07:23:31 - Alive Supplier: shooter
[INFO]: 2025-06-04 07:23:31 - Check Sub Supplier End
[INFO]: 2025-06-04 07:23:31 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 07:23:31 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 07:23:31 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 07:23:31 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 07:23:31 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 07:23:31 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 07:23:31 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 07:23:31 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 07:23:31 - Download.SupplierCheck() End
[INFO]: 2025-06-04 08:23:26 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 08:23:26 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 08:23:26 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 08:23:26 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 08:23:26 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 08:23:26 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 08:23:26 - UseHttpProxy = false
[ERROR]: 2025-06-04 08:23:31 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-04 08:23:31 - Check Sub Supplier Start...
[INFO]: 2025-06-04 08:23:31 - xunlei Check Alive = true, Speed = 69 ms
[ERROR]: 2025-06-04 08:23:31 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 08:23:31 - a4k Check Alive = false
[INFO]: 2025-06-04 08:23:31 - shooter Check Alive = true, Speed = 640 ms
[INFO]: 2025-06-04 08:23:31 - Alive Supplier: xunlei
[INFO]: 2025-06-04 08:23:31 - Alive Supplier: shooter
[INFO]: 2025-06-04 08:23:31 - Check Sub Supplier End
[INFO]: 2025-06-04 08:23:31 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 08:23:31 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 08:23:31 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 08:23:31 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 08:23:31 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 08:23:31 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 08:23:31 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 08:23:31 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 08:23:31 - Download.SupplierCheck() End
[INFO]: 2025-06-04 09:23:26 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 09:23:26 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 09:23:26 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 09:23:26 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 09:23:26 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 09:23:26 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 09:23:26 - UseHttpProxy = false
[ERROR]: 2025-06-04 09:23:31 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-04 09:23:31 - Check Sub Supplier Start...
[INFO]: 2025-06-04 09:23:31 - xunlei Check Alive = true, Speed = 68 ms
[ERROR]: 2025-06-04 09:23:31 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 09:23:31 - a4k Check Alive = false
[INFO]: 2025-06-04 09:23:31 - shooter Check Alive = true, Speed = 662 ms
[INFO]: 2025-06-04 09:23:31 - Alive Supplier: xunlei
[INFO]: 2025-06-04 09:23:31 - Alive Supplier: shooter
[INFO]: 2025-06-04 09:23:31 - Check Sub Supplier End
[INFO]: 2025-06-04 09:23:31 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 09:23:31 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 09:23:31 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 09:23:31 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 09:23:31 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 09:23:31 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 09:23:31 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 09:23:31 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 09:23:31 - Download.SupplierCheck() End
[INFO]: 2025-06-04 10:22:32 - LiteMode is true
[INFO]: 2025-06-04 10:22:32 - ChineseSubFinder Version: v0.55.3 Lite
[INFO]: 2025-06-04 10:22:33 - Reload Log Settings, level = Info
[INFO]: 2025-06-04 10:22:33 - Speed Dev Mode is Off
[INFO]: 2025-06-04 10:22:33 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 10:22:33 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 10:22:33 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 10:22:33 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 10:22:33 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 10:22:33 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 10:22:33 - UseHttpProxy = false
[INFO]: 2025-06-04 10:22:33 - UrlConnectednessTest Target Site https://baidu.com Speed: 263 ms, Status: true
[INFO]: 2025-06-04 10:22:33 - Check Sub Supplier Start...
[ERROR]: 2025-06-04 10:22:33 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 10:22:33 - a4k Check Alive = false
[INFO]: 2025-06-04 10:22:33 - xunlei Check Alive = true, Speed = 301 ms
[INFO]: 2025-06-04 10:22:34 - shooter Check Alive = true, Speed = 623 ms
[INFO]: 2025-06-04 10:22:34 - Alive Supplier: xunlei
[INFO]: 2025-06-04 10:22:34 - Alive Supplier: shooter
[INFO]: 2025-06-04 10:22:34 - Check Sub Supplier End
[INFO]: 2025-06-04 10:22:34 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 10:22:34 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 10:22:34 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 10:22:34 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 10:22:34 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 10:22:34 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 10:22:34 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 10:22:34 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 10:22:34 - Download.SupplierCheck() End
[INFO]: 2025-06-04 10:22:34 - Tmdb Api is Alive 382
[INFO]: 2025-06-04 10:22:34 - Try Start Http Server At Port 19035
[INFO]: 2025-06-04 10:22:34 - Setup is Done
[INFO]: 2025-06-04 10:22:34 - PreJob Will Start...
[INFO]: 2025-06-04 10:22:34 - PreJob.HotFix() Start...
[INFO]: 2025-06-04 10:22:34 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-04 10:22:34 - PreJob.HotFix() End
[INFO]: 2025-06-04 10:22:34 - PreJob.ChangeSubNameFormat() Start...
[INFO]: 2025-06-04 10:22:34 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-04 10:22:34 - DesSubFormatter == LateTimeSubFormatter then skip process
[INFO]: 2025-06-04 10:22:34 - PreJob.ChangeSubNameFormat() End
[INFO]: 2025-06-04 10:22:34 - PreJob.Wait() Done.
[INFO]: 2025-06-04 10:22:34 - Setup is Done
[INFO]: 2025-06-04 10:22:34 - RunAtStartup: false, so will not Run At Startup
[INFO]: 2025-06-04 10:22:34 - CronHelper Start...
[INFO]: 2025-06-04 10:22:34 - Next Sub Scan Will Process At: 2025-06-04 20:08:00
[INFO]: 2025-06-04 11:22:34 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 11:22:34 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 11:22:34 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 11:22:34 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 11:22:34 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 11:22:34 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 11:22:34 - UseHttpProxy = false
[INFO]: 2025-06-04 11:22:34 - UrlConnectednessTest Target Site https://baidu.com Speed: 200 ms, Status: true
[INFO]: 2025-06-04 11:22:34 - Check Sub Supplier Start...
[INFO]: 2025-06-04 11:22:34 - xunlei Check Alive = true, Speed = 116 ms
[ERROR]: 2025-06-04 11:22:34 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 11:22:34 - a4k Check Alive = false
[INFO]: 2025-06-04 11:22:34 - shooter Check Alive = true, Speed = 626 ms
[INFO]: 2025-06-04 11:22:34 - Alive Supplier: xunlei
[INFO]: 2025-06-04 11:22:34 - Alive Supplier: shooter
[INFO]: 2025-06-04 11:22:34 - Check Sub Supplier End
[INFO]: 2025-06-04 11:22:34 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 11:22:34 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 11:22:34 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 11:22:34 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 11:22:34 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 11:22:34 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 11:22:34 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 11:22:34 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 11:22:34 - Download.SupplierCheck() End
[INFO]: 2025-06-04 12:22:34 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 12:22:34 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 12:22:34 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 12:22:34 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 12:22:34 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 12:22:34 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 12:22:34 - UseHttpProxy = false
[INFO]: 2025-06-04 12:22:34 - UrlConnectednessTest Target Site https://baidu.com Speed: 218 ms, Status: true
[INFO]: 2025-06-04 12:22:34 - Check Sub Supplier Start...
[ERROR]: 2025-06-04 12:22:34 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 12:22:34 - a4k Check Alive = false
[INFO]: 2025-06-04 12:22:34 - xunlei Check Alive = true, Speed = 280 ms
[INFO]: 2025-06-04 12:22:34 - shooter Check Alive = true, Speed = 692 ms
[INFO]: 2025-06-04 12:22:34 - Alive Supplier: xunlei
[INFO]: 2025-06-04 12:22:34 - Alive Supplier: shooter
[INFO]: 2025-06-04 12:22:34 - Check Sub Supplier End
[INFO]: 2025-06-04 12:22:34 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 12:22:34 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 12:22:34 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 12:22:34 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 12:22:34 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 12:22:34 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 12:22:34 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 12:22:34 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 12:22:34 - Download.SupplierCheck() End
[INFO]: 2025-06-04 13:22:34 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 13:22:34 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 13:22:34 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 13:22:34 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 13:22:34 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 13:22:34 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 13:22:34 - UseHttpProxy = false
[ERROR]: 2025-06-04 13:22:39 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-04 13:22:39 - Check Sub Supplier Start...
[INFO]: 2025-06-04 13:22:39 - xunlei Check Alive = true, Speed = 105 ms
[ERROR]: 2025-06-04 13:22:39 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 13:22:39 - a4k Check Alive = false
[INFO]: 2025-06-04 13:22:39 - shooter Check Alive = true, Speed = 745 ms
[INFO]: 2025-06-04 13:22:39 - Alive Supplier: xunlei
[INFO]: 2025-06-04 13:22:39 - Alive Supplier: shooter
[INFO]: 2025-06-04 13:22:39 - Check Sub Supplier End
[INFO]: 2025-06-04 13:22:39 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 13:22:39 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 13:22:39 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 13:22:39 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 13:22:39 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 13:22:39 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 13:22:39 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 13:22:39 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 13:22:39 - Download.SupplierCheck() End
[INFO]: 2025-06-04 14:22:34 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 14:22:34 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 14:22:34 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 14:22:34 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 14:22:34 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 14:22:34 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 14:22:34 - UseHttpProxy = false
[ERROR]: 2025-06-04 14:22:39 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-04 14:22:39 - Check Sub Supplier Start...
[INFO]: 2025-06-04 14:22:39 - xunlei Check Alive = true, Speed = 114 ms
[ERROR]: 2025-06-04 14:22:39 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 14:22:39 - a4k Check Alive = false
[INFO]: 2025-06-04 14:22:39 - shooter Check Alive = true, Speed = 687 ms
[INFO]: 2025-06-04 14:22:39 - Alive Supplier: xunlei
[INFO]: 2025-06-04 14:22:39 - Alive Supplier: shooter
[INFO]: 2025-06-04 14:22:39 - Check Sub Supplier End
[INFO]: 2025-06-04 14:22:39 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 14:22:39 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 14:22:39 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 14:22:39 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 14:22:39 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 14:22:39 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 14:22:39 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 14:22:39 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 14:22:39 - Download.SupplierCheck() End
[INFO]: 2025-06-04 15:22:34 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 15:22:34 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 15:22:34 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 15:22:34 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 15:22:34 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 15:22:34 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 15:22:34 - UseHttpProxy = false
[ERROR]: 2025-06-04 15:22:39 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-04 15:22:39 - Check Sub Supplier Start...
[ERROR]: 2025-06-04 15:22:39 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 15:22:39 - a4k Check Alive = false
[INFO]: 2025-06-04 15:22:39 - xunlei Check Alive = true, Speed = 199 ms
[INFO]: 2025-06-04 15:22:39 - shooter Check Alive = true, Speed = 977 ms
[INFO]: 2025-06-04 15:22:39 - Alive Supplier: xunlei
[INFO]: 2025-06-04 15:22:39 - Alive Supplier: shooter
[INFO]: 2025-06-04 15:22:39 - Check Sub Supplier End
[INFO]: 2025-06-04 15:22:39 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 15:22:39 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 15:22:39 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 15:22:39 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 15:22:39 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 15:22:39 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 15:22:39 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 15:22:39 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 15:22:39 - Download.SupplierCheck() End
[INFO]: 2025-06-04 16:22:34 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 16:22:34 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 16:22:34 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 16:22:34 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 16:22:34 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 16:22:34 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 16:22:34 - UseHttpProxy = false
[ERROR]: 2025-06-04 16:22:39 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-04 16:22:39 - Check Sub Supplier Start...
[ERROR]: 2025-06-04 16:22:39 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 16:22:39 - a4k Check Alive = false
[INFO]: 2025-06-04 16:22:39 - xunlei Check Alive = true, Speed = 127 ms
[INFO]: 2025-06-04 16:22:39 - shooter Check Alive = true, Speed = 748 ms
[INFO]: 2025-06-04 16:22:39 - Alive Supplier: xunlei
[INFO]: 2025-06-04 16:22:39 - Alive Supplier: shooter
[INFO]: 2025-06-04 16:22:39 - Check Sub Supplier End
[INFO]: 2025-06-04 16:22:39 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 16:22:39 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 16:22:39 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 16:22:39 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 16:22:39 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 16:22:39 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 16:22:39 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 16:22:39 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 16:22:39 - Download.SupplierCheck() End
[INFO]: 2025-06-04 16:49:43 - LiteMode is true
[INFO]: 2025-06-04 16:49:43 - ChineseSubFinder Version: v0.55.3 Lite
[INFO]: 2025-06-04 16:49:43 - Reload Log Settings, level = Info
[INFO]: 2025-06-04 16:49:43 - Speed Dev Mode is Off
[INFO]: 2025-06-04 16:49:43 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 16:49:43 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 16:49:43 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 16:49:43 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 16:49:43 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 16:49:43 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 16:49:43 - UseHttpProxy = false
[INFO]: 2025-06-04 16:49:43 - UrlConnectednessTest Target Site https://baidu.com Speed: 427 ms, Status: true
[INFO]: 2025-06-04 16:49:43 - Check Sub Supplier Start...
[ERROR]: 2025-06-04 16:49:43 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 16:49:43 - a4k Check Alive = false
[INFO]: 2025-06-04 16:49:43 - xunlei Check Alive = true, Speed = 251 ms
[INFO]: 2025-06-04 16:49:44 - shooter Check Alive = true, Speed = 834 ms
[INFO]: 2025-06-04 16:49:44 - Alive Supplier: xunlei
[INFO]: 2025-06-04 16:49:44 - Alive Supplier: shooter
[INFO]: 2025-06-04 16:49:44 - Check Sub Supplier End
[INFO]: 2025-06-04 16:49:44 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 16:49:44 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 16:49:44 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 16:49:44 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 16:49:44 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 16:49:44 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 16:49:44 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 16:49:44 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 16:49:44 - Download.SupplierCheck() End
[INFO]: 2025-06-04 16:49:45 - Tmdb Api is Alive 382
[INFO]: 2025-06-04 16:49:45 - Try Start Http Server At Port 19035
[INFO]: 2025-06-04 16:49:45 - Setup is Done
[INFO]: 2025-06-04 16:49:45 - PreJob Will Start...
[INFO]: 2025-06-04 16:49:45 - PreJob.HotFix() Start...
[INFO]: 2025-06-04 16:49:45 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-04 16:49:45 - PreJob.HotFix() End
[INFO]: 2025-06-04 16:49:45 - PreJob.ChangeSubNameFormat() Start...
[INFO]: 2025-06-04 16:49:45 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-04 16:49:45 - DesSubFormatter == LateTimeSubFormatter then skip process
[INFO]: 2025-06-04 16:49:45 - PreJob.ChangeSubNameFormat() End
[INFO]: 2025-06-04 16:49:45 - PreJob.Wait() Done.
[INFO]: 2025-06-04 16:49:45 - Setup is Done
[INFO]: 2025-06-04 16:49:45 - RunAtStartup: false, so will not Run At Startup
[INFO]: 2025-06-04 16:49:45 - CronHelper Start...
[INFO]: 2025-06-04 16:49:45 - Next Sub Scan Will Process At: 2025-06-04 20:08:00
[INFO]: 2025-06-04 17:49:45 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 17:49:45 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 17:49:45 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 17:49:45 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 17:49:45 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 17:49:45 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 17:49:45 - UseHttpProxy = false
[ERROR]: 2025-06-04 17:49:50 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-04 17:49:50 - Check Sub Supplier Start...
[INFO]: 2025-06-04 17:49:50 - xunlei Check Alive = true, Speed = 66 ms
[ERROR]: 2025-06-04 17:49:50 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 17:49:50 - a4k Check Alive = false
[INFO]: 2025-06-04 17:49:50 - shooter Check Alive = true, Speed = 761 ms
[INFO]: 2025-06-04 17:49:50 - Alive Supplier: xunlei
[INFO]: 2025-06-04 17:49:50 - Alive Supplier: shooter
[INFO]: 2025-06-04 17:49:50 - Check Sub Supplier End
[INFO]: 2025-06-04 17:49:50 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 17:49:50 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 17:49:50 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 17:49:50 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 17:49:50 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 17:49:50 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 17:49:50 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 17:49:50 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 17:49:50 - Download.SupplierCheck() End
[INFO]: 2025-06-04 18:49:45 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 18:49:45 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 18:49:45 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 18:49:45 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 18:49:45 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 18:49:45 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 18:49:45 - UseHttpProxy = false
[ERROR]: 2025-06-04 18:49:50 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-04 18:49:50 - Check Sub Supplier Start...
[ERROR]: 2025-06-04 18:49:50 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 18:49:50 - a4k Check Alive = false
[INFO]: 2025-06-04 18:49:50 - xunlei Check Alive = true, Speed = 208 ms
[INFO]: 2025-06-04 18:49:50 - shooter Check Alive = true, Speed = 820 ms
[INFO]: 2025-06-04 18:49:50 - Alive Supplier: xunlei
[INFO]: 2025-06-04 18:49:50 - Alive Supplier: shooter
[INFO]: 2025-06-04 18:49:50 - Check Sub Supplier End
[INFO]: 2025-06-04 18:49:50 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 18:49:50 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 18:49:50 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 18:49:50 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 18:49:50 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 18:49:50 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 18:49:50 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 18:49:50 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 18:49:50 - Download.SupplierCheck() End
[INFO]: 2025-06-04 19:49:45 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 19:49:45 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 19:49:45 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 19:49:45 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 19:49:45 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 19:49:45 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 19:49:45 - UseHttpProxy = false
[ERROR]: 2025-06-04 19:49:50 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-04 19:49:50 - Check Sub Supplier Start...
[ERROR]: 2025-06-04 19:49:50 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 19:49:50 - a4k Check Alive = false
[INFO]: 2025-06-04 19:49:50 - xunlei Check Alive = true, Speed = 272 ms
[INFO]: 2025-06-04 19:49:50 - shooter Check Alive = true, Speed = 789 ms
[INFO]: 2025-06-04 19:49:50 - Alive Supplier: xunlei
[INFO]: 2025-06-04 19:49:50 - Alive Supplier: shooter
[INFO]: 2025-06-04 19:49:50 - Check Sub Supplier End
[INFO]: 2025-06-04 19:49:50 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 19:49:50 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 19:49:50 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 19:49:50 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 19:49:50 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 19:49:50 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 19:49:50 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 19:49:50 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 19:49:50 - Download.SupplierCheck() End
[INFO]: 2025-06-04 20:08:00 - scanVideoProcessAdd2DownloadQueue Start: 2025-06-04 20:08:00
[INFO]: 2025-06-04 20:08:00 - ------------------------------------
[INFO]: 2025-06-04 20:08:00 - Video Scan Started...
[INFO]: 2025-06-04 20:08:00 - ScanNormalMovieAndSeries Start...
[INFO]: 2025-06-04 20:08:00 -  --------------------------------------------------
[INFO]: 2025-06-04 20:08:00 - MatchedVideoFileFromDirs Start...
[INFO]: 2025-06-04 20:08:00 - ------------------------------------------
[INFO]: 2025-06-04 20:08:00 - GetSeriesListFromDirs Start...
[INFO]: 2025-06-04 20:08:00 - MatchedVideoFileFromDirs End
[INFO]: 2025-06-04 20:08:00 -  --------------------------------------------------
[INFO]: 2025-06-04 20:08:00 - GetSeriesListFromDirs End
[INFO]: 2025-06-04 20:08:00 - ------------------------------------------
[INFO]: 2025-06-04 20:08:00 - ScanNormalMovieAndSeries End
[INFO]: 2025-06-04 20:08:00 - ScanEmbyMovieAndSeries Start...
[INFO]: 2025-06-04 20:08:00 - Not Forced Scan And DownSub
[INFO]: 2025-06-04 20:08:00 - Movie Sub Dl From Emby API...
[INFO]: 2025-06-04 20:08:00 - Refresh Emby Sub List Start...
[ERROR]: 2025-06-04 20:08:21 - Refresh Emby Sub List Error
[ERROR]: 2025-06-04 20:08:21 - refreshEmbySubList Get "http://10.0.0.236:8096/emby/Items?Filters=IsNotFolder&IncludeItemTypes=Episode%2CMovie&IsUnaired=false&Limit=1000000&Recursive=true&SortBy=DateCreated&SortOrder=Descending&api_key=626d06abd06545529572a37c9a6b1392": dial tcp 10.0.0.236:8096: connect: connection refused
[INFO]: 2025-06-04 20:08:21 - ScanEmbyMovieAndSeries End
[ERROR]: 2025-06-04 20:08:21 - ScanEmbyMovieAndSeries Get "http://10.0.0.236:8096/emby/Items?Filters=IsNotFolder&IncludeItemTypes=Episode%2CMovie&IsUnaired=false&Limit=1000000&Recursive=true&SortBy=DateCreated&SortOrder=Descending&api_key=626d06abd06545529572a37c9a6b1392": dial tcp 10.0.0.236:8096: connect: connection refused
[INFO]: 2025-06-04 20:08:21 - VideoScanAndRefreshHelper finished, cost: 0.35078376418333335 min
[INFO]: 2025-06-04 20:08:21 - Video Scan End
[INFO]: 2025-06-04 20:08:21 - ------------------------------------
[ERROR]: 2025-06-04 20:08:21 - Get "http://10.0.0.236:8096/emby/Items?Filters=IsNotFolder&IncludeItemTypes=Episode%2CMovie&IsUnaired=false&Limit=1000000&Recursive=true&SortBy=DateCreated&SortOrder=Descending&api_key=626d06abd06545529572a37c9a6b1392": dial tcp 10.0.0.236:8096: connect: connection refused
[INFO]: 2025-06-04 20:49:45 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 20:49:45 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 20:49:45 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 20:49:45 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 20:49:45 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 20:49:45 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 20:49:45 - UseHttpProxy = false
[INFO]: 2025-06-04 20:49:45 - UrlConnectednessTest Target Site https://baidu.com Speed: 649 ms, Status: true
[INFO]: 2025-06-04 20:49:45 - Check Sub Supplier Start...
[INFO]: 2025-06-04 20:49:45 - xunlei Check Alive = true, Speed = 80 ms
[ERROR]: 2025-06-04 20:49:45 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 20:49:45 - a4k Check Alive = false
[INFO]: 2025-06-04 20:49:46 - shooter Check Alive = true, Speed = 850 ms
[INFO]: 2025-06-04 20:49:46 - Alive Supplier: xunlei
[INFO]: 2025-06-04 20:49:46 - Alive Supplier: shooter
[INFO]: 2025-06-04 20:49:46 - Check Sub Supplier End
[INFO]: 2025-06-04 20:49:46 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 20:49:46 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 20:49:46 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 20:49:46 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 20:49:46 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 20:49:46 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 20:49:46 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 20:49:46 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 20:49:46 - Download.SupplierCheck() End
[INFO]: 2025-06-04 21:49:45 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 21:49:45 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 21:49:45 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 21:49:45 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 21:49:45 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 21:49:45 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 21:49:45 - UseHttpProxy = false
[ERROR]: 2025-06-04 21:49:50 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-04 21:49:50 - Check Sub Supplier Start...
[ERROR]: 2025-06-04 21:49:50 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 21:49:50 - a4k Check Alive = false
[INFO]: 2025-06-04 21:49:50 - xunlei Check Alive = true, Speed = 216 ms
[INFO]: 2025-06-04 21:49:50 - shooter Check Alive = true, Speed = 640 ms
[INFO]: 2025-06-04 21:49:50 - Alive Supplier: xunlei
[INFO]: 2025-06-04 21:49:50 - Alive Supplier: shooter
[INFO]: 2025-06-04 21:49:50 - Check Sub Supplier End
[INFO]: 2025-06-04 21:49:50 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 21:49:50 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 21:49:50 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 21:49:50 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 21:49:50 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 21:49:50 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 21:49:50 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 21:49:50 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 21:49:50 - Download.SupplierCheck() End
[INFO]: 2025-06-04 23:12:58 - LiteMode is true
[INFO]: 2025-06-04 23:12:58 - ChineseSubFinder Version: v0.55.3 Lite
[INFO]: 2025-06-04 23:12:59 - Reload Log Settings, level = Info
[INFO]: 2025-06-04 23:12:59 - Speed Dev Mode is Off
[INFO]: 2025-06-04 23:12:59 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 23:12:59 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 23:12:59 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 23:12:59 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 23:12:59 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 23:12:59 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 23:12:59 - UseHttpProxy = false
[INFO]: 2025-06-04 23:12:59 - UrlConnectednessTest Target Site https://baidu.com Speed: 380 ms, Status: true
[INFO]: 2025-06-04 23:12:59 - Check Sub Supplier Start...
[INFO]: 2025-06-04 23:12:59 - xunlei Check Alive = true, Speed = 81 ms
[ERROR]: 2025-06-04 23:12:59 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 23:12:59 - a4k Check Alive = false
[INFO]: 2025-06-04 23:13:35 - shooter Check Alive = true, Speed = 36106 ms
[INFO]: 2025-06-04 23:13:35 - Alive Supplier: xunlei
[INFO]: 2025-06-04 23:13:35 - Alive Supplier: shooter
[INFO]: 2025-06-04 23:13:35 - Check Sub Supplier End
[INFO]: 2025-06-04 23:13:35 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 23:13:35 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 23:13:35 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 23:13:35 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 23:13:35 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 23:13:35 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 23:13:35 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 23:13:35 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 23:13:35 - Download.SupplierCheck() End
[INFO]: 2025-06-04 23:13:35 - Tmdb Api is Alive 382
[INFO]: 2025-06-04 23:13:35 - Try Start Http Server At Port 19035
[INFO]: 2025-06-04 23:13:35 - Setup is Done
[INFO]: 2025-06-04 23:13:35 - PreJob Will Start...
[INFO]: 2025-06-04 23:13:35 - PreJob.HotFix() Start...
[INFO]: 2025-06-04 23:13:35 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-04 23:13:35 - PreJob.HotFix() End
[INFO]: 2025-06-04 23:13:35 - PreJob.ChangeSubNameFormat() Start...
[INFO]: 2025-06-04 23:13:35 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-04 23:13:35 - DesSubFormatter == LateTimeSubFormatter then skip process
[INFO]: 2025-06-04 23:13:35 - PreJob.ChangeSubNameFormat() End
[INFO]: 2025-06-04 23:13:35 - PreJob.Wait() Done.
[INFO]: 2025-06-04 23:13:35 - Setup is Done
[INFO]: 2025-06-04 23:13:35 - RunAtStartup: false, so will not Run At Startup
[INFO]: 2025-06-04 23:13:35 - CronHelper Start...
[INFO]: 2025-06-04 23:13:35 - Next Sub Scan Will Process At: 2025-06-05 20:08:00
[INFO]: 2025-06-04 23:14:39 - LiteMode is true
[INFO]: 2025-06-04 23:14:39 - ChineseSubFinder Version: v0.55.3 Lite
[INFO]: 2025-06-04 23:14:39 - Reload Log Settings, level = Info
[INFO]: 2025-06-04 23:14:39 - Speed Dev Mode is Off
[INFO]: 2025-06-04 23:14:39 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 23:14:39 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 23:14:39 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 23:14:39 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 23:14:39 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 23:14:39 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 23:14:39 - UseHttpProxy = false
[INFO]: 2025-06-04 23:14:39 - UrlConnectednessTest Target Site https://baidu.com Speed: 327 ms, Status: true
[INFO]: 2025-06-04 23:14:39 - Check Sub Supplier Start...
[INFO]: 2025-06-04 23:14:40 - xunlei Check Alive = true, Speed = 71 ms
[ERROR]: 2025-06-04 23:14:40 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 23:14:40 - a4k Check Alive = false
[INFO]: 2025-06-04 23:14:47 - shooter Check Alive = true, Speed = 7998 ms
[INFO]: 2025-06-04 23:14:47 - Alive Supplier: xunlei
[INFO]: 2025-06-04 23:14:47 - Alive Supplier: shooter
[INFO]: 2025-06-04 23:14:47 - Check Sub Supplier End
[INFO]: 2025-06-04 23:14:47 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 23:14:47 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 23:14:47 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 23:14:47 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 23:14:47 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 23:14:47 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 23:14:47 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 23:14:47 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 23:14:47 - Download.SupplierCheck() End
[INFO]: 2025-06-04 23:14:48 - Tmdb Api is Alive 382
[INFO]: 2025-06-04 23:14:48 - Try Start Http Server At Port 19035
[INFO]: 2025-06-04 23:14:48 - Setup is Done
[INFO]: 2025-06-04 23:14:48 - PreJob Will Start...
[INFO]: 2025-06-04 23:14:48 - PreJob.HotFix() Start...
[INFO]: 2025-06-04 23:14:48 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-04 23:14:48 - PreJob.HotFix() End
[INFO]: 2025-06-04 23:14:48 - PreJob.ChangeSubNameFormat() Start...
[INFO]: 2025-06-04 23:14:48 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-04 23:14:48 - DesSubFormatter == LateTimeSubFormatter then skip process
[INFO]: 2025-06-04 23:14:48 - PreJob.ChangeSubNameFormat() End
[INFO]: 2025-06-04 23:14:48 - PreJob.Wait() Done.
[INFO]: 2025-06-04 23:14:48 - Setup is Done
[INFO]: 2025-06-04 23:14:48 - RunAtStartup: false, so will not Run At Startup
[INFO]: 2025-06-04 23:14:48 - CronHelper Start...
[INFO]: 2025-06-04 23:14:48 - Next Sub Scan Will Process At: 2025-06-05 20:08:00
[INFO]: 2025-06-04 23:16:00 - LiteMode is true
[INFO]: 2025-06-04 23:16:00 - ChineseSubFinder Version: v0.55.3 Lite
[INFO]: 2025-06-04 23:16:00 - Reload Log Settings, level = Info
[INFO]: 2025-06-04 23:16:00 - Speed Dev Mode is Off
[INFO]: 2025-06-04 23:16:00 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 23:16:00 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 23:16:00 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 23:16:00 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 23:16:00 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 23:16:00 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 23:16:00 - UseHttpProxy = false
[INFO]: 2025-06-04 23:16:00 - UrlConnectednessTest Target Site https://baidu.com Speed: 324 ms, Status: true
[INFO]: 2025-06-04 23:16:00 - Check Sub Supplier Start...
[ERROR]: 2025-06-04 23:16:01 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 23:16:01 - a4k Check Alive = false
[INFO]: 2025-06-04 23:16:01 - xunlei Check Alive = true, Speed = 165 ms
[INFO]: 2025-06-04 23:16:41 - shooter Check Alive = true, Speed = 40654 ms
[INFO]: 2025-06-04 23:16:41 - Alive Supplier: xunlei
[INFO]: 2025-06-04 23:16:41 - Alive Supplier: shooter
[INFO]: 2025-06-04 23:16:41 - Check Sub Supplier End
[INFO]: 2025-06-04 23:16:41 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 23:16:41 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 23:16:41 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 23:16:41 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 23:16:41 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 23:16:41 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 23:16:41 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 23:16:41 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 23:16:41 - Download.SupplierCheck() End
[INFO]: 2025-06-04 23:16:42 - Tmdb Api is Alive 382
[INFO]: 2025-06-04 23:16:42 - Try Start Http Server At Port 19035
[INFO]: 2025-06-04 23:16:42 - Setup is Done
[INFO]: 2025-06-04 23:16:42 - PreJob Will Start...
[INFO]: 2025-06-04 23:16:42 - PreJob.HotFix() Start...
[INFO]: 2025-06-04 23:16:42 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-04 23:16:42 - PreJob.HotFix() End
[INFO]: 2025-06-04 23:16:42 - PreJob.ChangeSubNameFormat() Start...
[INFO]: 2025-06-04 23:16:42 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-04 23:16:42 - DesSubFormatter == LateTimeSubFormatter then skip process
[INFO]: 2025-06-04 23:16:42 - PreJob.ChangeSubNameFormat() End
[INFO]: 2025-06-04 23:16:42 - PreJob.Wait() Done.
[INFO]: 2025-06-04 23:16:42 - Setup is Done
[INFO]: 2025-06-04 23:16:42 - RunAtStartup: false, so will not Run At Startup
[INFO]: 2025-06-04 23:16:42 - CronHelper Start...
[INFO]: 2025-06-04 23:16:42 - Next Sub Scan Will Process At: 2025-06-05 20:08:00
[INFO]: 2025-06-04 23:23:33 - LiteMode is true
[INFO]: 2025-06-04 23:23:34 - ChineseSubFinder Version: v0.55.3 Lite
[INFO]: 2025-06-04 23:23:34 - Reload Log Settings, level = Info
[INFO]: 2025-06-04 23:23:34 - Speed Dev Mode is Off
[INFO]: 2025-06-04 23:23:34 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-04 23:23:34 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-04 23:23:34 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-04 23:23:34 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-04 23:23:34 - PreDownloadProcess.Init() End
[INFO]: 2025-06-04 23:23:34 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-04 23:23:34 - UseHttpProxy = false
[ERROR]: 2025-06-04 23:23:39 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-04 23:23:39 - Check Sub Supplier Start...
[ERROR]: 2025-06-04 23:23:39 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-04 23:23:39 - a4k Check Alive = false
[INFO]: 2025-06-04 23:23:39 - xunlei Check Alive = true, Speed = 228 ms
[INFO]: 2025-06-04 23:23:49 - shooter Check Alive = true, Speed = 10128 ms
[INFO]: 2025-06-04 23:23:49 - Alive Supplier: xunlei
[INFO]: 2025-06-04 23:23:49 - Alive Supplier: shooter
[INFO]: 2025-06-04 23:23:49 - Check Sub Supplier End
[INFO]: 2025-06-04 23:23:49 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-04 23:23:49 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-04 23:23:49 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-04 23:23:49 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-04 23:23:49 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-04 23:23:49 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-04 23:23:49 - PreDownloadProcess.Check() End
[INFO]: 2025-06-04 23:23:49 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-04 23:23:49 - Download.SupplierCheck() End
[INFO]: 2025-06-04 23:23:49 - Tmdb Api is Alive 382
[INFO]: 2025-06-04 23:23:49 - Try Start Http Server At Port 19035
[INFO]: 2025-06-04 23:23:49 - Setup is Done
[INFO]: 2025-06-04 23:23:49 - PreJob Will Start...
[INFO]: 2025-06-04 23:23:49 - PreJob.HotFix() Start...
[INFO]: 2025-06-04 23:23:49 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-04 23:23:49 - PreJob.HotFix() End
[INFO]: 2025-06-04 23:23:49 - PreJob.ChangeSubNameFormat() Start...
[INFO]: 2025-06-04 23:23:49 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-04 23:23:49 - DesSubFormatter == LateTimeSubFormatter then skip process
[INFO]: 2025-06-04 23:23:49 - PreJob.ChangeSubNameFormat() End
[INFO]: 2025-06-04 23:23:49 - PreJob.Wait() Done.
[INFO]: 2025-06-04 23:23:49 - Setup is Done
[INFO]: 2025-06-04 23:23:49 - RunAtStartup: false, so will not Run At Startup
[INFO]: 2025-06-04 23:23:49 - CronHelper Start...
[INFO]: 2025-06-04 23:23:49 - Next Sub Scan Will Process At: 2025-06-05 20:08:00
