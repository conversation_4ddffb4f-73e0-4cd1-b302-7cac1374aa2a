[INFO]: 2025-06-05 00:23:49 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 00:23:49 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 00:23:49 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 00:23:49 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 00:23:49 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 00:23:49 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 00:23:49 - UseHttpProxy = false
[INFO]: 2025-06-05 00:23:49 - UrlConnectednessTest Target Site https://baidu.com Speed: 241 ms, Status: true
[INFO]: 2025-06-05 00:23:49 - Check Sub Supplier Start...
[ERROR]: 2025-06-05 00:23:49 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 00:23:49 - a4k Check Alive = false
[INFO]: 2025-06-05 00:23:49 - xunlei Check Alive = true, Speed = 183 ms
[INFO]: 2025-06-05 00:23:51 - shooter Check Alive = true, Speed = 2432 ms
[INFO]: 2025-06-05 00:23:51 - Alive Supplier: xunlei
[INFO]: 2025-06-05 00:23:51 - Alive Supplier: shooter
[INFO]: 2025-06-05 00:23:51 - Check Sub Supplier End
[INFO]: 2025-06-05 00:23:51 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 00:23:51 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 00:23:51 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 00:23:51 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 00:23:51 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 00:23:51 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 00:23:51 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 00:23:51 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 00:23:51 - Download.SupplierCheck() End
[INFO]: 2025-06-05 00:34:44 - LiteMode is true
[INFO]: 2025-06-05 00:34:44 - ChineseSubFinder Version: v0.55.3 Lite
[INFO]: 2025-06-05 00:34:45 - Reload Log Settings, level = Info
[INFO]: 2025-06-05 00:34:45 - Speed Dev Mode is Off
[INFO]: 2025-06-05 00:34:45 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 00:34:45 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 00:34:45 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 00:34:45 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 00:34:45 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 00:34:45 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 00:34:45 - UseHttpProxy = false
[ERROR]: 2025-06-05 00:34:50 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-05 00:34:50 - Check Sub Supplier Start...
[INFO]: 2025-06-05 00:34:50 - xunlei Check Alive = true, Speed = 90 ms
[ERROR]: 2025-06-05 00:34:50 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 00:34:50 - a4k Check Alive = false
[INFO]: 2025-06-05 00:34:52 - shooter Check Alive = true, Speed = 2130 ms
[INFO]: 2025-06-05 00:34:52 - Alive Supplier: xunlei
[INFO]: 2025-06-05 00:34:52 - Alive Supplier: shooter
[INFO]: 2025-06-05 00:34:52 - Check Sub Supplier End
[INFO]: 2025-06-05 00:34:52 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 00:34:52 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 00:34:52 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 00:34:52 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 00:34:52 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 00:34:52 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 00:34:52 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 00:34:52 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 00:34:52 - Download.SupplierCheck() End
[INFO]: 2025-06-05 00:34:52 - Tmdb Api is Alive 382
[INFO]: 2025-06-05 00:34:52 - Try Start Http Server At Port 19035
[INFO]: 2025-06-05 00:34:52 - Setup is Done
[INFO]: 2025-06-05 00:34:52 - PreJob Will Start...
[INFO]: 2025-06-05 00:34:52 - PreJob.HotFix() Start...
[INFO]: 2025-06-05 00:34:52 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-05 00:34:52 - PreJob.HotFix() End
[INFO]: 2025-06-05 00:34:52 - PreJob.ChangeSubNameFormat() Start...
[INFO]: 2025-06-05 00:34:52 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-05 00:34:52 - DesSubFormatter == LateTimeSubFormatter then skip process
[INFO]: 2025-06-05 00:34:52 - PreJob.ChangeSubNameFormat() End
[INFO]: 2025-06-05 00:34:52 - PreJob.Wait() Done.
[INFO]: 2025-06-05 00:34:52 - Setup is Done
[INFO]: 2025-06-05 00:34:52 - RunAtStartup: false, so will not Run At Startup
[INFO]: 2025-06-05 00:34:52 - CronHelper Start...
[INFO]: 2025-06-05 00:34:52 - Next Sub Scan Will Process At: 2025-06-05 20:08:00
[INFO]: 2025-06-05 00:36:26 - LiteMode is true
[INFO]: 2025-06-05 00:36:26 - ChineseSubFinder Version: v0.55.3 Lite
[INFO]: 2025-06-05 00:36:26 - Reload Log Settings, level = Info
[INFO]: 2025-06-05 00:36:26 - Speed Dev Mode is Off
[INFO]: 2025-06-05 00:36:26 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 00:36:26 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 00:36:26 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 00:36:26 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 00:36:26 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 00:36:26 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 00:36:26 - UseHttpProxy = false
[ERROR]: 2025-06-05 00:36:31 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-05 00:36:31 - Check Sub Supplier Start...
[INFO]: 2025-06-05 00:36:31 - xunlei Check Alive = true, Speed = 73 ms
[ERROR]: 2025-06-05 00:36:31 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 00:36:31 - a4k Check Alive = false
[INFO]: 2025-06-05 00:36:55 - shooter Check Alive = true, Speed = 23618 ms
[INFO]: 2025-06-05 00:36:55 - Alive Supplier: xunlei
[INFO]: 2025-06-05 00:36:55 - Alive Supplier: shooter
[INFO]: 2025-06-05 00:36:55 - Check Sub Supplier End
[INFO]: 2025-06-05 00:36:55 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 00:36:55 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 00:36:55 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 00:36:55 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 00:36:55 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 00:36:55 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 00:36:55 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 00:36:55 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 00:36:55 - Download.SupplierCheck() End
[INFO]: 2025-06-05 00:36:56 - Tmdb Api is Alive 382
[INFO]: 2025-06-05 00:36:56 - Try Start Http Server At Port 19035
[INFO]: 2025-06-05 00:36:56 - Setup is Done
[INFO]: 2025-06-05 00:36:56 - PreJob Will Start...
[INFO]: 2025-06-05 00:36:56 - PreJob.HotFix() Start...
[INFO]: 2025-06-05 00:36:56 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-05 00:36:56 - PreJob.HotFix() End
[INFO]: 2025-06-05 00:36:56 - PreJob.ChangeSubNameFormat() Start...
[INFO]: 2025-06-05 00:36:56 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-05 00:36:56 - DesSubFormatter == LateTimeSubFormatter then skip process
[INFO]: 2025-06-05 00:36:56 - PreJob.ChangeSubNameFormat() End
[INFO]: 2025-06-05 00:36:56 - PreJob.Wait() Done.
[INFO]: 2025-06-05 00:36:56 - Setup is Done
[INFO]: 2025-06-05 00:36:56 - RunAtStartup: false, so will not Run At Startup
[INFO]: 2025-06-05 00:36:56 - CronHelper Start...
[INFO]: 2025-06-05 00:36:56 - Next Sub Scan Will Process At: 2025-06-05 20:08:00
[INFO]: 2025-06-05 01:36:56 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 01:36:56 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 01:36:56 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 01:36:56 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 01:36:56 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 01:36:56 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 01:36:56 - UseHttpProxy = false
[ERROR]: 2025-06-05 01:37:01 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-05 01:37:01 - Check Sub Supplier Start...
[ERROR]: 2025-06-05 01:37:01 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 01:37:01 - a4k Check Alive = false
[INFO]: 2025-06-05 01:37:01 - xunlei Check Alive = true, Speed = 170 ms
[INFO]: 2025-06-05 01:37:01 - shooter Check Alive = true, Speed = 713 ms
[INFO]: 2025-06-05 01:37:01 - Alive Supplier: xunlei
[INFO]: 2025-06-05 01:37:01 - Alive Supplier: shooter
[INFO]: 2025-06-05 01:37:01 - Check Sub Supplier End
[INFO]: 2025-06-05 01:37:01 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 01:37:01 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 01:37:01 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 01:37:01 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 01:37:01 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 01:37:01 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 01:37:01 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 01:37:01 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 01:37:01 - Download.SupplierCheck() End
[INFO]: 2025-06-05 02:36:56 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 02:36:56 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 02:36:56 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 02:36:56 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 02:36:56 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 02:36:56 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 02:36:56 - UseHttpProxy = false
[ERROR]: 2025-06-05 02:37:00 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": EOF
[INFO]: 2025-06-05 02:37:00 - Check Sub Supplier Start...
[INFO]: 2025-06-05 02:37:00 - xunlei Check Alive = true, Speed = 75 ms
[ERROR]: 2025-06-05 02:37:00 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 02:37:00 - a4k Check Alive = false
[INFO]: 2025-06-05 02:37:01 - shooter Check Alive = true, Speed = 638 ms
[INFO]: 2025-06-05 02:37:01 - Alive Supplier: xunlei
[INFO]: 2025-06-05 02:37:01 - Alive Supplier: shooter
[INFO]: 2025-06-05 02:37:01 - Check Sub Supplier End
[INFO]: 2025-06-05 02:37:01 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 02:37:01 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 02:37:01 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 02:37:01 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 02:37:01 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 02:37:01 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 02:37:01 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 02:37:01 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 02:37:01 - Download.SupplierCheck() End
[INFO]: 2025-06-05 03:36:56 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 03:36:56 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 03:36:56 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 03:36:56 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 03:36:56 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 03:36:56 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 03:36:56 - UseHttpProxy = false
[INFO]: 2025-06-05 03:36:56 - UrlConnectednessTest Target Site https://baidu.com Speed: 444 ms, Status: true
[INFO]: 2025-06-05 03:36:56 - Check Sub Supplier Start...
[ERROR]: 2025-06-05 03:36:56 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 03:36:56 - a4k Check Alive = false
[INFO]: 2025-06-05 03:36:56 - xunlei Check Alive = true, Speed = 180 ms
[INFO]: 2025-06-05 03:36:57 - shooter Check Alive = true, Speed = 629 ms
[INFO]: 2025-06-05 03:36:57 - Alive Supplier: xunlei
[INFO]: 2025-06-05 03:36:57 - Alive Supplier: shooter
[INFO]: 2025-06-05 03:36:57 - Check Sub Supplier End
[INFO]: 2025-06-05 03:36:57 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 03:36:57 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 03:36:57 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 03:36:57 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 03:36:57 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 03:36:57 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 03:36:57 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 03:36:57 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 03:36:57 - Download.SupplierCheck() End
[INFO]: 2025-06-05 04:36:56 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 04:36:56 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 04:36:56 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 04:36:56 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 04:36:56 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 04:36:56 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 04:36:56 - UseHttpProxy = false
[ERROR]: 2025-06-05 04:37:01 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-05 04:37:01 - Check Sub Supplier Start...
[INFO]: 2025-06-05 04:37:01 - xunlei Check Alive = true, Speed = 59 ms
[ERROR]: 2025-06-05 04:37:01 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 04:37:01 - a4k Check Alive = false
[INFO]: 2025-06-05 04:37:01 - shooter Check Alive = true, Speed = 643 ms
[INFO]: 2025-06-05 04:37:01 - Alive Supplier: xunlei
[INFO]: 2025-06-05 04:37:01 - Alive Supplier: shooter
[INFO]: 2025-06-05 04:37:01 - Check Sub Supplier End
[INFO]: 2025-06-05 04:37:01 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 04:37:01 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 04:37:01 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 04:37:01 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 04:37:01 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 04:37:01 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 04:37:01 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 04:37:01 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 04:37:01 - Download.SupplierCheck() End
[INFO]: 2025-06-05 05:36:56 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 05:36:56 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 05:36:56 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 05:36:56 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 05:36:56 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 05:36:56 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 05:36:56 - UseHttpProxy = false
[INFO]: 2025-06-05 05:36:56 - UrlConnectednessTest Target Site https://baidu.com Speed: 190 ms, Status: true
[INFO]: 2025-06-05 05:36:56 - Check Sub Supplier Start...
[ERROR]: 2025-06-05 05:36:56 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 05:36:56 - a4k Check Alive = false
[INFO]: 2025-06-05 05:36:56 - xunlei Check Alive = true, Speed = 163 ms
[INFO]: 2025-06-05 05:36:56 - shooter Check Alive = true, Speed = 621 ms
[INFO]: 2025-06-05 05:36:56 - Alive Supplier: xunlei
[INFO]: 2025-06-05 05:36:56 - Alive Supplier: shooter
[INFO]: 2025-06-05 05:36:56 - Check Sub Supplier End
[INFO]: 2025-06-05 05:36:56 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 05:36:56 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 05:36:56 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 05:36:56 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 05:36:56 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 05:36:56 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 05:36:56 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 05:36:56 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 05:36:56 - Download.SupplierCheck() End
[INFO]: 2025-06-05 06:36:56 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 06:36:56 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 06:36:56 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 06:36:56 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 06:36:56 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 06:36:56 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 06:36:56 - UseHttpProxy = false
[ERROR]: 2025-06-05 06:37:01 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-05 06:37:01 - Check Sub Supplier Start...
[INFO]: 2025-06-05 06:37:01 - xunlei Check Alive = true, Speed = 62 ms
[ERROR]: 2025-06-05 06:37:01 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 06:37:01 - a4k Check Alive = false
[INFO]: 2025-06-05 06:37:01 - shooter Check Alive = true, Speed = 643 ms
[INFO]: 2025-06-05 06:37:01 - Alive Supplier: xunlei
[INFO]: 2025-06-05 06:37:01 - Alive Supplier: shooter
[INFO]: 2025-06-05 06:37:01 - Check Sub Supplier End
[INFO]: 2025-06-05 06:37:01 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 06:37:01 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 06:37:01 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 06:37:01 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 06:37:01 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 06:37:01 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 06:37:01 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 06:37:01 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 06:37:01 - Download.SupplierCheck() End
[INFO]: 2025-06-05 07:36:56 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 07:36:56 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 07:36:56 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 07:36:56 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 07:36:56 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 07:36:56 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 07:36:56 - UseHttpProxy = false
[ERROR]: 2025-06-05 07:37:01 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-05 07:37:01 - Check Sub Supplier Start...
[ERROR]: 2025-06-05 07:37:01 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 07:37:01 - a4k Check Alive = false
[INFO]: 2025-06-05 07:37:01 - xunlei Check Alive = true, Speed = 178 ms
[INFO]: 2025-06-05 07:37:01 - shooter Check Alive = true, Speed = 608 ms
[INFO]: 2025-06-05 07:37:01 - Alive Supplier: xunlei
[INFO]: 2025-06-05 07:37:01 - Alive Supplier: shooter
[INFO]: 2025-06-05 07:37:01 - Check Sub Supplier End
[INFO]: 2025-06-05 07:37:01 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 07:37:01 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 07:37:01 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 07:37:01 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 07:37:01 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 07:37:01 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 07:37:01 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 07:37:01 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 07:37:01 - Download.SupplierCheck() End
[INFO]: 2025-06-05 08:36:56 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 08:36:56 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 08:36:56 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 08:36:56 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 08:36:56 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 08:36:56 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 08:36:56 - UseHttpProxy = false
[INFO]: 2025-06-05 08:36:56 - UrlConnectednessTest Target Site https://baidu.com Speed: 157 ms, Status: true
[INFO]: 2025-06-05 08:36:56 - Check Sub Supplier Start...
[ERROR]: 2025-06-05 08:36:56 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 08:36:56 - a4k Check Alive = false
[INFO]: 2025-06-05 08:36:56 - xunlei Check Alive = true, Speed = 143 ms
[INFO]: 2025-06-05 08:36:56 - shooter Check Alive = true, Speed = 615 ms
[INFO]: 2025-06-05 08:36:56 - Alive Supplier: xunlei
[INFO]: 2025-06-05 08:36:56 - Alive Supplier: shooter
[INFO]: 2025-06-05 08:36:56 - Check Sub Supplier End
[INFO]: 2025-06-05 08:36:56 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 08:36:56 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 08:36:56 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 08:36:56 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 08:36:56 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 08:36:56 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 08:36:56 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 08:36:56 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 08:36:56 - Download.SupplierCheck() End
[INFO]: 2025-06-05 09:36:56 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 09:36:56 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 09:36:56 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 09:36:56 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 09:36:56 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 09:36:56 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 09:36:56 - UseHttpProxy = false
[ERROR]: 2025-06-05 09:36:59 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": EOF
[INFO]: 2025-06-05 09:36:59 - Check Sub Supplier Start...
[ERROR]: 2025-06-05 09:36:59 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 09:36:59 - a4k Check Alive = false
[INFO]: 2025-06-05 09:36:59 - xunlei Check Alive = true, Speed = 268 ms
[INFO]: 2025-06-05 09:36:59 - shooter Check Alive = true, Speed = 602 ms
[INFO]: 2025-06-05 09:36:59 - Alive Supplier: xunlei
[INFO]: 2025-06-05 09:36:59 - Alive Supplier: shooter
[INFO]: 2025-06-05 09:36:59 - Check Sub Supplier End
[INFO]: 2025-06-05 09:36:59 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 09:36:59 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 09:36:59 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 09:36:59 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 09:36:59 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 09:36:59 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 09:36:59 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 09:36:59 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 09:36:59 - Download.SupplierCheck() End
[INFO]: 2025-06-05 10:36:56 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 10:36:56 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 10:36:56 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 10:36:56 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 10:36:56 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 10:36:56 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 10:36:56 - UseHttpProxy = false
[ERROR]: 2025-06-05 10:37:01 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-05 10:37:01 - Check Sub Supplier Start...
[ERROR]: 2025-06-05 10:37:01 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 10:37:01 - a4k Check Alive = false
[INFO]: 2025-06-05 10:37:01 - xunlei Check Alive = true, Speed = 268 ms
[INFO]: 2025-06-05 10:37:01 - shooter Check Alive = true, Speed = 595 ms
[INFO]: 2025-06-05 10:37:01 - Alive Supplier: xunlei
[INFO]: 2025-06-05 10:37:01 - Alive Supplier: shooter
[INFO]: 2025-06-05 10:37:01 - Check Sub Supplier End
[INFO]: 2025-06-05 10:37:01 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 10:37:01 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 10:37:01 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 10:37:01 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 10:37:01 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 10:37:01 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 10:37:01 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 10:37:01 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 10:37:01 - Download.SupplierCheck() End
[INFO]: 2025-06-05 11:36:56 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 11:36:56 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 11:36:56 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 11:36:56 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 11:36:56 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 11:36:56 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 11:36:56 - UseHttpProxy = false
[INFO]: 2025-06-05 11:36:56 - UrlConnectednessTest Target Site https://baidu.com Speed: 171 ms, Status: true
[INFO]: 2025-06-05 11:36:56 - Check Sub Supplier Start...
[INFO]: 2025-06-05 11:36:56 - xunlei Check Alive = true, Speed = 62 ms
[ERROR]: 2025-06-05 11:36:56 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 11:36:56 - a4k Check Alive = false
[INFO]: 2025-06-05 11:36:56 - shooter Check Alive = true, Speed = 609 ms
[INFO]: 2025-06-05 11:36:56 - Alive Supplier: xunlei
[INFO]: 2025-06-05 11:36:56 - Alive Supplier: shooter
[INFO]: 2025-06-05 11:36:56 - Check Sub Supplier End
[INFO]: 2025-06-05 11:36:56 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 11:36:56 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 11:36:56 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 11:36:56 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 11:36:56 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 11:36:56 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 11:36:56 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 11:36:56 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 11:36:56 - Download.SupplierCheck() End
[INFO]: 2025-06-05 12:36:56 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 12:36:56 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 12:36:56 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 12:36:56 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 12:36:56 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 12:36:56 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 12:36:56 - UseHttpProxy = false
[INFO]: 2025-06-05 12:36:56 - UrlConnectednessTest Target Site https://baidu.com Speed: 228 ms, Status: true
[INFO]: 2025-06-05 12:36:56 - Check Sub Supplier Start...
[ERROR]: 2025-06-05 12:36:56 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 12:36:56 - a4k Check Alive = false
[INFO]: 2025-06-05 12:36:56 - xunlei Check Alive = true, Speed = 117 ms
[INFO]: 2025-06-05 12:36:57 - shooter Check Alive = true, Speed = 1076 ms
[INFO]: 2025-06-05 12:36:57 - Alive Supplier: xunlei
[INFO]: 2025-06-05 12:36:57 - Alive Supplier: shooter
[INFO]: 2025-06-05 12:36:57 - Check Sub Supplier End
[INFO]: 2025-06-05 12:36:57 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 12:36:57 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 12:36:57 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 12:36:57 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 12:36:57 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 12:36:57 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 12:36:57 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 12:36:57 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 12:36:57 - Download.SupplierCheck() End
[INFO]: 2025-06-05 13:36:56 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 13:36:56 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 13:36:56 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 13:36:56 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 13:36:56 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 13:36:56 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 13:36:56 - UseHttpProxy = false
[INFO]: 2025-06-05 13:36:56 - UrlConnectednessTest Target Site https://baidu.com Speed: 161 ms, Status: true
[INFO]: 2025-06-05 13:36:56 - Check Sub Supplier Start...
[INFO]: 2025-06-05 13:36:56 - xunlei Check Alive = true, Speed = 97 ms
[ERROR]: 2025-06-05 13:36:56 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 13:36:56 - a4k Check Alive = false
[INFO]: 2025-06-05 13:36:57 - shooter Check Alive = true, Speed = 1147 ms
[INFO]: 2025-06-05 13:36:57 - Alive Supplier: xunlei
[INFO]: 2025-06-05 13:36:57 - Alive Supplier: shooter
[INFO]: 2025-06-05 13:36:57 - Check Sub Supplier End
[INFO]: 2025-06-05 13:36:57 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 13:36:57 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 13:36:57 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 13:36:57 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 13:36:57 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 13:36:57 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 13:36:57 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 13:36:57 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 13:36:57 - Download.SupplierCheck() End
[INFO]: 2025-06-05 14:36:56 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 14:36:56 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 14:36:56 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 14:36:56 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 14:36:56 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 14:36:56 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 14:36:56 - UseHttpProxy = false
[ERROR]: 2025-06-05 14:37:01 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-05 14:37:01 - Check Sub Supplier Start...
[INFO]: 2025-06-05 14:37:01 - xunlei Check Alive = true, Speed = 56 ms
[ERROR]: 2025-06-05 14:37:01 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 14:37:01 - a4k Check Alive = false
[INFO]: 2025-06-05 14:37:02 - shooter Check Alive = true, Speed = 1084 ms
[INFO]: 2025-06-05 14:37:02 - Alive Supplier: xunlei
[INFO]: 2025-06-05 14:37:02 - Alive Supplier: shooter
[INFO]: 2025-06-05 14:37:02 - Check Sub Supplier End
[INFO]: 2025-06-05 14:37:02 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 14:37:02 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 14:37:02 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 14:37:02 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 14:37:02 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 14:37:02 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 14:37:02 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 14:37:02 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 14:37:02 - Download.SupplierCheck() End
[INFO]: 2025-06-05 15:36:56 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 15:36:56 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 15:36:56 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 15:36:56 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 15:36:56 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 15:36:56 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 15:36:56 - UseHttpProxy = false
[INFO]: 2025-06-05 15:36:56 - UrlConnectednessTest Target Site https://baidu.com Speed: 215 ms, Status: true
[INFO]: 2025-06-05 15:36:56 - Check Sub Supplier Start...
[ERROR]: 2025-06-05 15:36:56 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 15:36:56 - a4k Check Alive = false
[INFO]: 2025-06-05 15:36:56 - xunlei Check Alive = true, Speed = 226 ms
[INFO]: 2025-06-05 15:36:56 - shooter Check Alive = true, Speed = 674 ms
[INFO]: 2025-06-05 15:36:56 - Alive Supplier: xunlei
[INFO]: 2025-06-05 15:36:56 - Alive Supplier: shooter
[INFO]: 2025-06-05 15:36:56 - Check Sub Supplier End
[INFO]: 2025-06-05 15:36:56 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 15:36:56 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 15:36:56 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 15:36:56 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 15:36:56 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 15:36:56 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 15:36:56 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 15:36:56 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 15:36:56 - Download.SupplierCheck() End
[INFO]: 2025-06-05 16:05:18 - LiteMode is true
[INFO]: 2025-06-05 16:05:18 - ChineseSubFinder Version: v0.55.3 Lite
[INFO]: 2025-06-05 16:05:18 - Reload Log Settings, level = Info
[INFO]: 2025-06-05 16:05:18 - Speed Dev Mode is Off
[INFO]: 2025-06-05 16:05:18 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 16:05:18 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 16:05:18 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 16:05:18 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 16:05:18 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 16:05:18 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 16:05:18 - UseHttpProxy = false
[ERROR]: 2025-06-05 16:05:23 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-05 16:05:23 - Check Sub Supplier Start...
[ERROR]: 2025-06-05 16:05:23 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 16:05:23 - a4k Check Alive = false
[INFO]: 2025-06-05 16:05:23 - xunlei Check Alive = true, Speed = 196 ms
[INFO]: 2025-06-05 16:05:24 - shooter Check Alive = true, Speed = 666 ms
[INFO]: 2025-06-05 16:05:24 - Alive Supplier: xunlei
[INFO]: 2025-06-05 16:05:24 - Alive Supplier: shooter
[INFO]: 2025-06-05 16:05:24 - Check Sub Supplier End
[INFO]: 2025-06-05 16:05:24 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 16:05:24 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 16:05:24 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 16:05:24 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 16:05:24 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 16:05:24 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 16:05:24 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 16:05:24 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 16:05:24 - Download.SupplierCheck() End
[INFO]: 2025-06-05 16:05:25 - Tmdb Api is Alive 382
[INFO]: 2025-06-05 16:05:25 - Try Start Http Server At Port 19035
[INFO]: 2025-06-05 16:05:25 - Setup is Done
[INFO]: 2025-06-05 16:05:25 - PreJob Will Start...
[INFO]: 2025-06-05 16:05:25 - PreJob.HotFix() Start...
[INFO]: 2025-06-05 16:05:25 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-05 16:05:25 - PreJob.HotFix() End
[INFO]: 2025-06-05 16:05:25 - PreJob.ChangeSubNameFormat() Start...
[INFO]: 2025-06-05 16:05:25 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-05 16:05:25 - DesSubFormatter == LateTimeSubFormatter then skip process
[INFO]: 2025-06-05 16:05:25 - PreJob.ChangeSubNameFormat() End
[INFO]: 2025-06-05 16:05:25 - PreJob.Wait() Done.
[INFO]: 2025-06-05 16:05:25 - Setup is Done
[INFO]: 2025-06-05 16:05:25 - RunAtStartup: false, so will not Run At Startup
[INFO]: 2025-06-05 16:05:25 - CronHelper Start...
[INFO]: 2025-06-05 16:05:25 - Next Sub Scan Will Process At: 2025-06-05 20:08:00
[INFO]: 2025-06-05 16:05:59 - LiteMode is true
[INFO]: 2025-06-05 16:05:59 - ChineseSubFinder Version: v0.55.3 Lite
[INFO]: 2025-06-05 16:05:59 - Reload Log Settings, level = Info
[INFO]: 2025-06-05 16:05:59 - Speed Dev Mode is Off
[INFO]: 2025-06-05 16:05:59 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 16:05:59 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 16:05:59 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 16:05:59 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 16:05:59 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 16:05:59 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 16:05:59 - UseHttpProxy = false
[INFO]: 2025-06-05 16:06:00 - UrlConnectednessTest Target Site https://baidu.com Speed: 500 ms, Status: true
[INFO]: 2025-06-05 16:06:00 - Check Sub Supplier Start...
[ERROR]: 2025-06-05 16:06:00 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 16:06:00 - a4k Check Alive = false
[INFO]: 2025-06-05 16:06:00 - xunlei Check Alive = true, Speed = 285 ms
[INFO]: 2025-06-05 16:06:00 - shooter Check Alive = true, Speed = 661 ms
[INFO]: 2025-06-05 16:06:00 - Alive Supplier: xunlei
[INFO]: 2025-06-05 16:06:00 - Alive Supplier: shooter
[INFO]: 2025-06-05 16:06:00 - Check Sub Supplier End
[INFO]: 2025-06-05 16:06:00 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 16:06:00 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 16:06:00 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 16:06:00 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 16:06:00 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 16:06:00 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 16:06:00 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 16:06:00 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 16:06:00 - Download.SupplierCheck() End
[INFO]: 2025-06-05 16:06:01 - Tmdb Api is Alive 382
[INFO]: 2025-06-05 16:06:01 - Try Start Http Server At Port 19035
[INFO]: 2025-06-05 16:06:01 - Setup is Done
[INFO]: 2025-06-05 16:06:01 - PreJob Will Start...
[INFO]: 2025-06-05 16:06:01 - PreJob.HotFix() Start...
[INFO]: 2025-06-05 16:06:01 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-05 16:06:01 - PreJob.HotFix() End
[INFO]: 2025-06-05 16:06:01 - PreJob.ChangeSubNameFormat() Start...
[INFO]: 2025-06-05 16:06:01 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-05 16:06:01 - DesSubFormatter == LateTimeSubFormatter then skip process
[INFO]: 2025-06-05 16:06:01 - PreJob.ChangeSubNameFormat() End
[INFO]: 2025-06-05 16:06:01 - PreJob.Wait() Done.
[INFO]: 2025-06-05 16:06:01 - Setup is Done
[INFO]: 2025-06-05 16:06:01 - RunAtStartup: false, so will not Run At Startup
[INFO]: 2025-06-05 16:06:01 - CronHelper Start...
[INFO]: 2025-06-05 16:06:01 - Next Sub Scan Will Process At: 2025-06-05 20:08:00
[INFO]: 2025-06-05 17:06:01 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 17:06:01 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 17:06:01 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 17:06:01 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 17:06:01 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 17:06:01 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 17:06:01 - UseHttpProxy = false
[ERROR]: 2025-06-05 17:06:06 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-05 17:06:06 - Check Sub Supplier Start...
[INFO]: 2025-06-05 17:06:06 - xunlei Check Alive = true, Speed = 62 ms
[ERROR]: 2025-06-05 17:06:06 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 17:06:06 - a4k Check Alive = false
[INFO]: 2025-06-05 17:06:06 - shooter Check Alive = true, Speed = 628 ms
[INFO]: 2025-06-05 17:06:06 - Alive Supplier: xunlei
[INFO]: 2025-06-05 17:06:06 - Alive Supplier: shooter
[INFO]: 2025-06-05 17:06:06 - Check Sub Supplier End
[INFO]: 2025-06-05 17:06:06 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 17:06:06 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 17:06:06 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 17:06:06 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 17:06:06 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 17:06:06 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 17:06:06 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 17:06:06 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 17:06:06 - Download.SupplierCheck() End
[INFO]: 2025-06-05 18:06:01 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 18:06:01 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 18:06:01 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 18:06:01 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 18:06:01 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 18:06:01 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 18:06:01 - UseHttpProxy = false
[ERROR]: 2025-06-05 18:06:06 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-05 18:06:06 - Check Sub Supplier Start...
[ERROR]: 2025-06-05 18:06:06 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 18:06:06 - a4k Check Alive = false
[INFO]: 2025-06-05 18:06:06 - xunlei Check Alive = true, Speed = 180 ms
[INFO]: 2025-06-05 18:06:06 - shooter Check Alive = true, Speed = 649 ms
[INFO]: 2025-06-05 18:06:06 - Alive Supplier: xunlei
[INFO]: 2025-06-05 18:06:06 - Alive Supplier: shooter
[INFO]: 2025-06-05 18:06:06 - Check Sub Supplier End
[INFO]: 2025-06-05 18:06:06 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 18:06:06 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 18:06:06 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 18:06:06 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 18:06:06 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 18:06:06 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 18:06:06 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 18:06:06 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 18:06:06 - Download.SupplierCheck() End
[INFO]: 2025-06-05 19:06:01 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 19:06:01 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 19:06:01 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 19:06:01 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 19:06:01 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 19:06:01 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 19:06:01 - UseHttpProxy = false
[ERROR]: 2025-06-05 19:06:06 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-05 19:06:06 - Check Sub Supplier Start...
[ERROR]: 2025-06-05 19:06:06 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 19:06:06 - a4k Check Alive = false
[INFO]: 2025-06-05 19:06:06 - xunlei Check Alive = true, Speed = 192 ms
[INFO]: 2025-06-05 19:06:06 - shooter Check Alive = true, Speed = 669 ms
[INFO]: 2025-06-05 19:06:06 - Alive Supplier: xunlei
[INFO]: 2025-06-05 19:06:06 - Alive Supplier: shooter
[INFO]: 2025-06-05 19:06:06 - Check Sub Supplier End
[INFO]: 2025-06-05 19:06:06 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 19:06:06 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 19:06:06 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 19:06:06 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 19:06:06 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 19:06:06 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 19:06:06 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 19:06:06 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 19:06:06 - Download.SupplierCheck() End
[INFO]: 2025-06-05 20:06:01 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 20:06:01 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 20:06:01 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 20:06:01 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 20:06:01 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 20:06:01 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 20:06:01 - UseHttpProxy = false
[ERROR]: 2025-06-05 20:06:06 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-05 20:06:06 - Check Sub Supplier Start...
[ERROR]: 2025-06-05 20:06:06 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 20:06:06 - a4k Check Alive = false
[INFO]: 2025-06-05 20:06:06 - xunlei Check Alive = true, Speed = 209 ms
[INFO]: 2025-06-05 20:06:06 - shooter Check Alive = true, Speed = 631 ms
[INFO]: 2025-06-05 20:06:06 - Alive Supplier: xunlei
[INFO]: 2025-06-05 20:06:06 - Alive Supplier: shooter
[INFO]: 2025-06-05 20:06:06 - Check Sub Supplier End
[INFO]: 2025-06-05 20:06:06 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 20:06:06 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 20:06:06 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 20:06:06 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 20:06:06 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 20:06:06 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 20:06:06 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 20:06:06 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 20:06:06 - Download.SupplierCheck() End
[INFO]: 2025-06-05 20:08:00 - scanVideoProcessAdd2DownloadQueue Start: 2025-06-05 20:08:00
[INFO]: 2025-06-05 20:08:00 - ------------------------------------
[INFO]: 2025-06-05 20:08:00 - Video Scan Started...
[INFO]: 2025-06-05 20:08:00 - ScanNormalMovieAndSeries Start...
[INFO]: 2025-06-05 20:08:00 -  --------------------------------------------------
[INFO]: 2025-06-05 20:08:00 - MatchedVideoFileFromDirs Start...
[INFO]: 2025-06-05 20:08:00 - ------------------------------------------
[INFO]: 2025-06-05 20:08:00 - GetSeriesListFromDirs Start...
[INFO]: 2025-06-05 20:08:00 - MatchedVideoFileFromDirs End
[INFO]: 2025-06-05 20:08:00 -  --------------------------------------------------
[INFO]: 2025-06-05 20:08:00 - GetSeriesListFromDirs End
[INFO]: 2025-06-05 20:08:00 - ------------------------------------------
[INFO]: 2025-06-05 20:08:00 - ScanNormalMovieAndSeries End
[INFO]: 2025-06-05 20:08:00 - ScanEmbyMovieAndSeries Start...
[INFO]: 2025-06-05 20:08:00 - Not Forced Scan And DownSub
[INFO]: 2025-06-05 20:08:00 - Movie Sub Dl From Emby API...
[INFO]: 2025-06-05 20:08:00 - Refresh Emby Sub List Start...
[ERROR]: 2025-06-05 20:08:21 - Refresh Emby Sub List Error
[ERROR]: 2025-06-05 20:08:21 - refreshEmbySubList Get "http://10.0.0.236:8096/emby/Items?Filters=IsNotFolder&IncludeItemTypes=Episode%2CMovie&IsUnaired=false&Limit=1000000&Recursive=true&SortBy=DateCreated&SortOrder=Descending&api_key=626d06abd06545529572a37c9a6b1392": dial tcp 10.0.0.236:8096: connect: connection refused
[INFO]: 2025-06-05 20:08:21 - ScanEmbyMovieAndSeries End
[ERROR]: 2025-06-05 20:08:21 - ScanEmbyMovieAndSeries Get "http://10.0.0.236:8096/emby/Items?Filters=IsNotFolder&IncludeItemTypes=Episode%2CMovie&IsUnaired=false&Limit=1000000&Recursive=true&SortBy=DateCreated&SortOrder=Descending&api_key=626d06abd06545529572a37c9a6b1392": dial tcp 10.0.0.236:8096: connect: connection refused
[INFO]: 2025-06-05 20:08:21 - VideoScanAndRefreshHelper finished, cost: 0.3507512734666667 min
[INFO]: 2025-06-05 20:08:21 - Video Scan End
[INFO]: 2025-06-05 20:08:21 - ------------------------------------
[ERROR]: 2025-06-05 20:08:21 - Get "http://10.0.0.236:8096/emby/Items?Filters=IsNotFolder&IncludeItemTypes=Episode%2CMovie&IsUnaired=false&Limit=1000000&Recursive=true&SortBy=DateCreated&SortOrder=Descending&api_key=626d06abd06545529572a37c9a6b1392": dial tcp 10.0.0.236:8096: connect: connection refused
[INFO]: 2025-06-05 21:21:55 - LiteMode is true
[INFO]: 2025-06-05 21:21:55 - ChineseSubFinder Version: v0.55.3 Lite
[INFO]: 2025-06-05 21:21:55 - Reload Log Settings, level = Info
[INFO]: 2025-06-05 21:21:55 - Speed Dev Mode is Off
[INFO]: 2025-06-05 21:21:55 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 21:21:55 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 21:21:55 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 21:21:55 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 21:21:55 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 21:21:55 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 21:21:55 - UseHttpProxy = false
[INFO]: 2025-06-05 21:21:59 - UrlConnectednessTest Target Site https://baidu.com Speed: 3978 ms, Status: true
[INFO]: 2025-06-05 21:21:59 - Check Sub Supplier Start...
[ERROR]: 2025-06-05 21:21:59 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 21:21:59 - a4k Check Alive = false
[INFO]: 2025-06-05 21:21:59 - xunlei Check Alive = true, Speed = 349 ms
[INFO]: 2025-06-05 21:22:00 - shooter Check Alive = true, Speed = 1487 ms
[INFO]: 2025-06-05 21:22:01 - Alive Supplier: xunlei
[INFO]: 2025-06-05 21:22:01 - Alive Supplier: shooter
[INFO]: 2025-06-05 21:22:01 - Check Sub Supplier End
[INFO]: 2025-06-05 21:22:01 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 21:22:01 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 21:22:01 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 21:22:01 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 21:22:01 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 21:22:01 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 21:22:01 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 21:22:01 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 21:22:01 - Download.SupplierCheck() End
[INFO]: 2025-06-05 21:22:02 - Tmdb Api is Alive 382
[INFO]: 2025-06-05 21:22:02 - Try Start Http Server At Port 19035
[INFO]: 2025-06-05 21:22:02 - Setup is Done
[INFO]: 2025-06-05 21:22:02 - PreJob Will Start...
[INFO]: 2025-06-05 21:22:02 - PreJob.HotFix() Start...
[INFO]: 2025-06-05 21:22:02 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-05 21:22:02 - PreJob.HotFix() End
[INFO]: 2025-06-05 21:22:02 - PreJob.ChangeSubNameFormat() Start...
[INFO]: 2025-06-05 21:22:02 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-05 21:22:02 - DesSubFormatter == LateTimeSubFormatter then skip process
[INFO]: 2025-06-05 21:22:02 - PreJob.ChangeSubNameFormat() End
[INFO]: 2025-06-05 21:22:02 - PreJob.Wait() Done.
[INFO]: 2025-06-05 21:22:02 - Setup is Done
[INFO]: 2025-06-05 21:22:02 - RunAtStartup: false, so will not Run At Startup
[INFO]: 2025-06-05 21:22:02 - CronHelper Start...
[INFO]: 2025-06-05 21:22:02 - Next Sub Scan Will Process At: 2025-06-06 20:08:00
[INFO]: 2025-06-05 21:35:46 - LiteMode is true
[INFO]: 2025-06-05 21:35:46 - ChineseSubFinder Version: v0.55.3 Lite
[INFO]: 2025-06-05 21:35:46 - Reload Log Settings, level = Info
[INFO]: 2025-06-05 21:35:46 - Speed Dev Mode is Off
[INFO]: 2025-06-05 21:35:46 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 21:35:46 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 21:35:46 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 21:35:46 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 21:35:46 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 21:35:46 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 21:35:46 - UseHttpProxy = false
[INFO]: 2025-06-05 21:35:47 - UrlConnectednessTest Target Site https://baidu.com Speed: 1079 ms, Status: true
[INFO]: 2025-06-05 21:35:47 - Check Sub Supplier Start...
[INFO]: 2025-06-05 21:35:47 - xunlei Check Alive = true, Speed = 98 ms
[ERROR]: 2025-06-05 21:35:47 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 21:35:47 - a4k Check Alive = false
[INFO]: 2025-06-05 21:35:50 - shooter Check Alive = true, Speed = 2517 ms
[INFO]: 2025-06-05 21:35:50 - Alive Supplier: xunlei
[INFO]: 2025-06-05 21:35:50 - Alive Supplier: shooter
[INFO]: 2025-06-05 21:35:50 - Check Sub Supplier End
[INFO]: 2025-06-05 21:35:50 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 21:35:50 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 21:35:50 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 21:35:50 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 21:35:50 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 21:35:50 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 21:35:50 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 21:35:50 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 21:35:50 - Download.SupplierCheck() End
[INFO]: 2025-06-05 21:35:51 - Tmdb Api is Alive 382
[INFO]: 2025-06-05 21:35:51 - Try Start Http Server At Port 19035
[INFO]: 2025-06-05 21:35:51 - Setup is Done
[INFO]: 2025-06-05 21:35:51 - PreJob Will Start...
[INFO]: 2025-06-05 21:35:51 - PreJob.HotFix() Start...
[INFO]: 2025-06-05 21:35:51 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-05 21:35:51 - PreJob.HotFix() End
[INFO]: 2025-06-05 21:35:51 - PreJob.ChangeSubNameFormat() Start...
[INFO]: 2025-06-05 21:35:51 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-05 21:35:51 - DesSubFormatter == LateTimeSubFormatter then skip process
[INFO]: 2025-06-05 21:35:51 - PreJob.ChangeSubNameFormat() End
[INFO]: 2025-06-05 21:35:51 - PreJob.Wait() Done.
[INFO]: 2025-06-05 21:35:51 - Setup is Done
[INFO]: 2025-06-05 21:35:51 - RunAtStartup: false, so will not Run At Startup
[INFO]: 2025-06-05 21:35:51 - CronHelper Start...
[INFO]: 2025-06-05 21:35:51 - Next Sub Scan Will Process At: 2025-06-06 20:08:00
[INFO]: 2025-06-05 22:35:51 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 22:35:51 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 22:35:51 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 22:35:51 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 22:35:51 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 22:35:51 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 22:35:51 - UseHttpProxy = false
[ERROR]: 2025-06-05 22:35:56 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-05 22:35:56 - Check Sub Supplier Start...
[ERROR]: 2025-06-05 22:35:56 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 22:35:56 - a4k Check Alive = false
[INFO]: 2025-06-05 22:35:56 - xunlei Check Alive = true, Speed = 192 ms
[INFO]: 2025-06-05 22:36:02 - shooter Check Alive = true, Speed = 6039 ms
[INFO]: 2025-06-05 22:36:02 - Alive Supplier: xunlei
[INFO]: 2025-06-05 22:36:02 - Alive Supplier: shooter
[INFO]: 2025-06-05 22:36:02 - Check Sub Supplier End
[INFO]: 2025-06-05 22:36:02 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 22:36:02 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 22:36:02 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 22:36:02 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 22:36:02 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 22:36:02 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 22:36:02 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 22:36:02 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 22:36:02 - Download.SupplierCheck() End
[INFO]: 2025-06-05 23:35:51 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-05 23:35:51 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-05 23:35:51 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-05 23:35:51 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-05 23:35:51 - PreDownloadProcess.Init() End
[INFO]: 2025-06-05 23:35:51 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-05 23:35:51 - UseHttpProxy = false
[ERROR]: 2025-06-05 23:35:56 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-05 23:35:56 - Check Sub Supplier Start...
[ERROR]: 2025-06-05 23:35:56 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-05 23:35:56 - a4k Check Alive = false
[INFO]: 2025-06-05 23:35:56 - xunlei Check Alive = true, Speed = 428 ms
[INFO]: 2025-06-05 23:35:58 - shooter Check Alive = true, Speed = 2956 ms
[INFO]: 2025-06-05 23:35:58 - Alive Supplier: xunlei
[INFO]: 2025-06-05 23:35:58 - Alive Supplier: shooter
[INFO]: 2025-06-05 23:35:58 - Check Sub Supplier End
[INFO]: 2025-06-05 23:35:58 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-05 23:35:58 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-05 23:35:58 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-05 23:35:58 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-05 23:35:58 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-05 23:35:58 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-05 23:35:58 - PreDownloadProcess.Check() End
[INFO]: 2025-06-05 23:35:58 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-05 23:35:58 - Download.SupplierCheck() End
