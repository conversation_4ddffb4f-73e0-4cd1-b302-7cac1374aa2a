[INFO]: 2025-06-06 00:35:51 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 00:35:51 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 00:35:51 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 00:35:51 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 00:35:51 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 00:35:51 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 00:35:51 - UseHttpProxy = false
[INFO]: 2025-06-06 00:35:51 - UrlConnectednessTest Target Site https://baidu.com Speed: 178 ms, Status: true
[INFO]: 2025-06-06 00:35:51 - Check Sub Supplier Start...
[INFO]: 2025-06-06 00:35:51 - xunlei Check Alive = true, Speed = 112 ms
[ERROR]: 2025-06-06 00:35:51 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 00:35:51 - a4k Check Alive = false
[INFO]: 2025-06-06 00:35:55 - shooter Check Alive = true, Speed = 4428 ms
[INFO]: 2025-06-06 00:35:55 - Alive Supplier: xunlei
[INFO]: 2025-06-06 00:35:55 - Alive Supplier: shooter
[INFO]: 2025-06-06 00:35:55 - Check Sub Supplier End
[INFO]: 2025-06-06 00:35:55 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 00:35:55 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 00:35:55 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 00:35:55 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 00:35:55 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 00:35:55 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 00:35:55 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 00:35:55 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 00:35:55 - Download.SupplierCheck() End
[INFO]: 2025-06-06 01:35:51 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 01:35:51 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 01:35:51 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 01:35:51 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 01:35:51 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 01:35:51 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 01:35:51 - UseHttpProxy = false
[ERROR]: 2025-06-06 01:35:56 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-06 01:35:56 - Check Sub Supplier Start...
[INFO]: 2025-06-06 01:35:56 - xunlei Check Alive = true, Speed = 61 ms
[ERROR]: 2025-06-06 01:35:56 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 01:35:56 - a4k Check Alive = false
[INFO]: 2025-06-06 01:35:57 - shooter Check Alive = true, Speed = 1093 ms
[INFO]: 2025-06-06 01:35:57 - Alive Supplier: xunlei
[INFO]: 2025-06-06 01:35:57 - Alive Supplier: shooter
[INFO]: 2025-06-06 01:35:57 - Check Sub Supplier End
[INFO]: 2025-06-06 01:35:57 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 01:35:57 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 01:35:57 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 01:35:57 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 01:35:57 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 01:35:57 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 01:35:57 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 01:35:57 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 01:35:57 - Download.SupplierCheck() End
[INFO]: 2025-06-06 02:35:51 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 02:35:51 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 02:35:51 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 02:35:51 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 02:35:51 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 02:35:51 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 02:35:51 - UseHttpProxy = false
[INFO]: 2025-06-06 02:35:51 - UrlConnectednessTest Target Site https://baidu.com Speed: 168 ms, Status: true
[INFO]: 2025-06-06 02:35:51 - Check Sub Supplier Start...
[INFO]: 2025-06-06 02:35:51 - xunlei Check Alive = true, Speed = 83 ms
[ERROR]: 2025-06-06 02:35:51 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 02:35:51 - a4k Check Alive = false
[INFO]: 2025-06-06 02:35:51 - shooter Check Alive = true, Speed = 630 ms
[INFO]: 2025-06-06 02:35:51 - Alive Supplier: xunlei
[INFO]: 2025-06-06 02:35:51 - Alive Supplier: shooter
[INFO]: 2025-06-06 02:35:51 - Check Sub Supplier End
[INFO]: 2025-06-06 02:35:51 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 02:35:51 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 02:35:51 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 02:35:51 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 02:35:51 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 02:35:51 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 02:35:51 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 02:35:51 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 02:35:51 - Download.SupplierCheck() End
[INFO]: 2025-06-06 03:35:51 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 03:35:51 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 03:35:51 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 03:35:51 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 03:35:51 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 03:35:51 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 03:35:51 - UseHttpProxy = false
[INFO]: 2025-06-06 03:35:51 - UrlConnectednessTest Target Site https://baidu.com Speed: 175 ms, Status: true
[INFO]: 2025-06-06 03:35:51 - Check Sub Supplier Start...
[ERROR]: 2025-06-06 03:35:51 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 03:35:51 - a4k Check Alive = false
[INFO]: 2025-06-06 03:35:51 - xunlei Check Alive = true, Speed = 231 ms
[INFO]: 2025-06-06 03:35:51 - shooter Check Alive = true, Speed = 657 ms
[INFO]: 2025-06-06 03:35:51 - Alive Supplier: xunlei
[INFO]: 2025-06-06 03:35:51 - Alive Supplier: shooter
[INFO]: 2025-06-06 03:35:51 - Check Sub Supplier End
[INFO]: 2025-06-06 03:35:51 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 03:35:51 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 03:35:51 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 03:35:51 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 03:35:51 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 03:35:51 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 03:35:51 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 03:35:51 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 03:35:51 - Download.SupplierCheck() End
[INFO]: 2025-06-06 04:35:51 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 04:35:51 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 04:35:51 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 04:35:51 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 04:35:51 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 04:35:51 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 04:35:51 - UseHttpProxy = false
[ERROR]: 2025-06-06 04:35:56 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-06 04:35:56 - Check Sub Supplier Start...
[ERROR]: 2025-06-06 04:35:56 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 04:35:56 - a4k Check Alive = false
[INFO]: 2025-06-06 04:35:56 - xunlei Check Alive = true, Speed = 241 ms
[INFO]: 2025-06-06 04:35:56 - shooter Check Alive = true, Speed = 622 ms
[INFO]: 2025-06-06 04:35:56 - Alive Supplier: xunlei
[INFO]: 2025-06-06 04:35:56 - Alive Supplier: shooter
[INFO]: 2025-06-06 04:35:56 - Check Sub Supplier End
[INFO]: 2025-06-06 04:35:56 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 04:35:56 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 04:35:56 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 04:35:56 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 04:35:56 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 04:35:56 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 04:35:56 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 04:35:56 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 04:35:56 - Download.SupplierCheck() End
[INFO]: 2025-06-06 05:35:51 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 05:35:51 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 05:35:51 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 05:35:51 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 05:35:51 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 05:35:51 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 05:35:51 - UseHttpProxy = false
[ERROR]: 2025-06-06 05:35:56 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-06 05:35:56 - Check Sub Supplier Start...
[INFO]: 2025-06-06 05:35:56 - xunlei Check Alive = true, Speed = 79 ms
[ERROR]: 2025-06-06 05:35:56 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 05:35:56 - a4k Check Alive = false
[INFO]: 2025-06-06 05:35:56 - shooter Check Alive = true, Speed = 635 ms
[INFO]: 2025-06-06 05:35:56 - Alive Supplier: xunlei
[INFO]: 2025-06-06 05:35:56 - Alive Supplier: shooter
[INFO]: 2025-06-06 05:35:56 - Check Sub Supplier End
[INFO]: 2025-06-06 05:35:56 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 05:35:56 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 05:35:56 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 05:35:56 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 05:35:56 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 05:35:56 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 05:35:56 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 05:35:56 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 05:35:56 - Download.SupplierCheck() End
[INFO]: 2025-06-06 06:35:51 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 06:35:51 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 06:35:51 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 06:35:51 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 06:35:51 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 06:35:51 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 06:35:51 - UseHttpProxy = false
[ERROR]: 2025-06-06 06:35:56 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-06 06:35:56 - Check Sub Supplier Start...
[INFO]: 2025-06-06 06:35:56 - xunlei Check Alive = true, Speed = 66 ms
[ERROR]: 2025-06-06 06:35:56 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 06:35:56 - a4k Check Alive = false
[INFO]: 2025-06-06 06:35:56 - shooter Check Alive = true, Speed = 615 ms
[INFO]: 2025-06-06 06:35:56 - Alive Supplier: xunlei
[INFO]: 2025-06-06 06:35:56 - Alive Supplier: shooter
[INFO]: 2025-06-06 06:35:56 - Check Sub Supplier End
[INFO]: 2025-06-06 06:35:56 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 06:35:56 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 06:35:56 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 06:35:56 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 06:35:56 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 06:35:56 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 06:35:56 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 06:35:56 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 06:35:56 - Download.SupplierCheck() End
[INFO]: 2025-06-06 07:35:51 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 07:35:51 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 07:35:51 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 07:35:51 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 07:35:51 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 07:35:51 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 07:35:51 - UseHttpProxy = false
[ERROR]: 2025-06-06 07:35:56 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-06 07:35:56 - Check Sub Supplier Start...
[INFO]: 2025-06-06 07:35:56 - xunlei Check Alive = true, Speed = 83 ms
[ERROR]: 2025-06-06 07:35:56 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 07:35:56 - a4k Check Alive = false
[INFO]: 2025-06-06 07:35:56 - shooter Check Alive = true, Speed = 640 ms
[INFO]: 2025-06-06 07:35:56 - Alive Supplier: xunlei
[INFO]: 2025-06-06 07:35:56 - Alive Supplier: shooter
[INFO]: 2025-06-06 07:35:56 - Check Sub Supplier End
[INFO]: 2025-06-06 07:35:56 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 07:35:56 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 07:35:56 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 07:35:56 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 07:35:56 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 07:35:56 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 07:35:56 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 07:35:56 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 07:35:56 - Download.SupplierCheck() End
[INFO]: 2025-06-06 08:35:51 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 08:35:51 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 08:35:51 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 08:35:51 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 08:35:51 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 08:35:51 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 08:35:51 - UseHttpProxy = false
[ERROR]: 2025-06-06 08:35:56 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-06 08:35:56 - Check Sub Supplier Start...
[INFO]: 2025-06-06 08:35:56 - xunlei Check Alive = true, Speed = 62 ms
[ERROR]: 2025-06-06 08:35:56 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 08:35:56 - a4k Check Alive = false
[INFO]: 2025-06-06 08:35:56 - shooter Check Alive = true, Speed = 636 ms
[INFO]: 2025-06-06 08:35:56 - Alive Supplier: xunlei
[INFO]: 2025-06-06 08:35:56 - Alive Supplier: shooter
[INFO]: 2025-06-06 08:35:56 - Check Sub Supplier End
[INFO]: 2025-06-06 08:35:56 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 08:35:56 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 08:35:56 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 08:35:56 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 08:35:56 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 08:35:56 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 08:35:56 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 08:35:56 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 08:35:56 - Download.SupplierCheck() End
[INFO]: 2025-06-06 09:35:51 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 09:35:51 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 09:35:51 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 09:35:51 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 09:35:51 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 09:35:51 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 09:35:51 - UseHttpProxy = false
[INFO]: 2025-06-06 09:35:51 - UrlConnectednessTest Target Site https://baidu.com Speed: 192 ms, Status: true
[INFO]: 2025-06-06 09:35:51 - Check Sub Supplier Start...
[INFO]: 2025-06-06 09:35:51 - xunlei Check Alive = true, Speed = 86 ms
[ERROR]: 2025-06-06 09:35:51 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 09:35:51 - a4k Check Alive = false
[INFO]: 2025-06-06 09:35:51 - shooter Check Alive = true, Speed = 678 ms
[INFO]: 2025-06-06 09:35:51 - Alive Supplier: xunlei
[INFO]: 2025-06-06 09:35:51 - Alive Supplier: shooter
[INFO]: 2025-06-06 09:35:51 - Check Sub Supplier End
[INFO]: 2025-06-06 09:35:51 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 09:35:51 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 09:35:51 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 09:35:51 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 09:35:51 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 09:35:51 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 09:35:51 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 09:35:51 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 09:35:51 - Download.SupplierCheck() End
[INFO]: 2025-06-06 10:35:51 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 10:35:51 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 10:35:51 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 10:35:51 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 10:35:51 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 10:35:51 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 10:35:51 - UseHttpProxy = false
[INFO]: 2025-06-06 10:35:51 - UrlConnectednessTest Target Site https://baidu.com Speed: 152 ms, Status: true
[INFO]: 2025-06-06 10:35:51 - Check Sub Supplier Start...
[INFO]: 2025-06-06 10:35:51 - xunlei Check Alive = true, Speed = 79 ms
[ERROR]: 2025-06-06 10:35:51 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 10:35:51 - a4k Check Alive = false
[INFO]: 2025-06-06 10:35:51 - shooter Check Alive = true, Speed = 611 ms
[INFO]: 2025-06-06 10:35:51 - Alive Supplier: xunlei
[INFO]: 2025-06-06 10:35:51 - Alive Supplier: shooter
[INFO]: 2025-06-06 10:35:51 - Check Sub Supplier End
[INFO]: 2025-06-06 10:35:51 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 10:35:51 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 10:35:51 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 10:35:51 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 10:35:51 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 10:35:51 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 10:35:51 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 10:35:51 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 10:35:51 - Download.SupplierCheck() End
[INFO]: 2025-06-06 11:16:53 - LiteMode is true
[INFO]: 2025-06-06 11:16:53 - ChineseSubFinder Version: v0.55.3 Lite
[INFO]: 2025-06-06 11:16:53 - Reload Log Settings, level = Info
[INFO]: 2025-06-06 11:16:53 - Speed Dev Mode is Off
[INFO]: 2025-06-06 11:16:53 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 11:16:53 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 11:16:53 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 11:16:53 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 11:16:53 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 11:16:53 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 11:16:53 - UseHttpProxy = false
[INFO]: 2025-06-06 11:16:54 - UrlConnectednessTest Target Site https://baidu.com Speed: 252 ms, Status: true
[INFO]: 2025-06-06 11:16:54 - Check Sub Supplier Start...
[ERROR]: 2025-06-06 11:16:54 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 11:16:54 - a4k Check Alive = false
[INFO]: 2025-06-06 11:16:54 - xunlei Check Alive = true, Speed = 206 ms
[INFO]: 2025-06-06 11:16:54 - shooter Check Alive = true, Speed = 726 ms
[INFO]: 2025-06-06 11:16:54 - Alive Supplier: xunlei
[INFO]: 2025-06-06 11:16:54 - Alive Supplier: shooter
[INFO]: 2025-06-06 11:16:54 - Check Sub Supplier End
[INFO]: 2025-06-06 11:16:54 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 11:16:54 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 11:16:54 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 11:16:54 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 11:16:54 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 11:16:54 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 11:16:54 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 11:16:54 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 11:16:54 - Download.SupplierCheck() End
[INFO]: 2025-06-06 11:16:55 - Tmdb Api is Alive 382
[INFO]: 2025-06-06 11:16:55 - Try Start Http Server At Port 19035
[INFO]: 2025-06-06 11:16:55 - Setup is Done
[INFO]: 2025-06-06 11:16:55 - PreJob Will Start...
[INFO]: 2025-06-06 11:16:55 - PreJob.HotFix() Start...
[INFO]: 2025-06-06 11:16:55 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-06 11:16:55 - PreJob.HotFix() End
[INFO]: 2025-06-06 11:16:55 - PreJob.ChangeSubNameFormat() Start...
[INFO]: 2025-06-06 11:16:55 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-06 11:16:55 - DesSubFormatter == LateTimeSubFormatter then skip process
[INFO]: 2025-06-06 11:16:55 - PreJob.ChangeSubNameFormat() End
[INFO]: 2025-06-06 11:16:55 - PreJob.Wait() Done.
[INFO]: 2025-06-06 11:16:55 - Setup is Done
[INFO]: 2025-06-06 11:16:55 - RunAtStartup: false, so will not Run At Startup
[INFO]: 2025-06-06 11:16:55 - CronHelper Start...
[INFO]: 2025-06-06 11:16:55 - Next Sub Scan Will Process At: 2025-06-06 20:08:00
[INFO]: 2025-06-06 12:16:55 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 12:16:55 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 12:16:55 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 12:16:55 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 12:16:55 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 12:16:55 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 12:16:55 - UseHttpProxy = false
[ERROR]: 2025-06-06 12:17:00 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-06 12:17:00 - Check Sub Supplier Start...
[ERROR]: 2025-06-06 12:17:00 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 12:17:00 - a4k Check Alive = false
[INFO]: 2025-06-06 12:17:00 - xunlei Check Alive = true, Speed = 204 ms
[INFO]: 2025-06-06 12:17:00 - shooter Check Alive = true, Speed = 639 ms
[INFO]: 2025-06-06 12:17:00 - Alive Supplier: xunlei
[INFO]: 2025-06-06 12:17:00 - Alive Supplier: shooter
[INFO]: 2025-06-06 12:17:00 - Check Sub Supplier End
[INFO]: 2025-06-06 12:17:00 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 12:17:00 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 12:17:00 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 12:17:00 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 12:17:00 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 12:17:00 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 12:17:00 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 12:17:00 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 12:17:00 - Download.SupplierCheck() End
[INFO]: 2025-06-06 13:16:55 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 13:16:55 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 13:16:55 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 13:16:55 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 13:16:55 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 13:16:55 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 13:16:55 - UseHttpProxy = false
[INFO]: 2025-06-06 13:16:55 - UrlConnectednessTest Target Site https://baidu.com Speed: 260 ms, Status: true
[INFO]: 2025-06-06 13:16:55 - Check Sub Supplier Start...
[INFO]: 2025-06-06 13:16:55 - xunlei Check Alive = true, Speed = 100 ms
[ERROR]: 2025-06-06 13:16:55 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 13:16:55 - a4k Check Alive = false
[INFO]: 2025-06-06 13:16:55 - shooter Check Alive = true, Speed = 666 ms
[INFO]: 2025-06-06 13:16:55 - Alive Supplier: xunlei
[INFO]: 2025-06-06 13:16:55 - Alive Supplier: shooter
[INFO]: 2025-06-06 13:16:55 - Check Sub Supplier End
[INFO]: 2025-06-06 13:16:55 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 13:16:55 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 13:16:55 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 13:16:55 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 13:16:55 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 13:16:55 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 13:16:55 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 13:16:55 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 13:16:55 - Download.SupplierCheck() End
[INFO]: 2025-06-06 14:16:55 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 14:16:55 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 14:16:55 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 14:16:55 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 14:16:55 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 14:16:55 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 14:16:55 - UseHttpProxy = false
[ERROR]: 2025-06-06 14:16:57 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": EOF
[INFO]: 2025-06-06 14:16:57 - Check Sub Supplier Start...
[INFO]: 2025-06-06 14:16:57 - xunlei Check Alive = true, Speed = 94 ms
[ERROR]: 2025-06-06 14:16:57 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 14:16:57 - a4k Check Alive = false
[INFO]: 2025-06-06 14:16:58 - shooter Check Alive = true, Speed = 1205 ms
[INFO]: 2025-06-06 14:16:58 - Alive Supplier: xunlei
[INFO]: 2025-06-06 14:16:58 - Alive Supplier: shooter
[INFO]: 2025-06-06 14:16:58 - Check Sub Supplier End
[INFO]: 2025-06-06 14:16:58 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 14:16:58 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 14:16:58 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 14:16:58 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 14:16:58 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 14:16:58 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 14:16:58 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 14:16:58 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 14:16:58 - Download.SupplierCheck() End
[INFO]: 2025-06-06 15:16:55 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 15:16:55 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 15:16:55 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 15:16:55 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 15:16:55 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 15:16:55 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 15:16:55 - UseHttpProxy = false
[INFO]: 2025-06-06 15:16:55 - UrlConnectednessTest Target Site https://baidu.com Speed: 293 ms, Status: true
[INFO]: 2025-06-06 15:16:55 - Check Sub Supplier Start...
[INFO]: 2025-06-06 15:16:55 - xunlei Check Alive = true, Speed = 93 ms
[ERROR]: 2025-06-06 15:16:55 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 15:16:55 - a4k Check Alive = false
[INFO]: 2025-06-06 15:16:55 - shooter Check Alive = true, Speed = 658 ms
[INFO]: 2025-06-06 15:16:55 - Alive Supplier: xunlei
[INFO]: 2025-06-06 15:16:55 - Alive Supplier: shooter
[INFO]: 2025-06-06 15:16:55 - Check Sub Supplier End
[INFO]: 2025-06-06 15:16:55 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 15:16:55 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 15:16:55 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 15:16:55 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 15:16:55 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 15:16:55 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 15:16:55 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 15:16:55 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 15:16:55 - Download.SupplierCheck() End
[INFO]: 2025-06-06 16:16:55 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 16:16:55 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 16:16:55 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 16:16:55 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 16:16:55 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 16:16:55 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 16:16:55 - UseHttpProxy = false
[INFO]: 2025-06-06 16:16:55 - UrlConnectednessTest Target Site https://baidu.com Speed: 185 ms, Status: true
[INFO]: 2025-06-06 16:16:55 - Check Sub Supplier Start...
[ERROR]: 2025-06-06 16:16:55 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 16:16:55 - a4k Check Alive = false
[INFO]: 2025-06-06 16:16:55 - xunlei Check Alive = true, Speed = 295 ms
[INFO]: 2025-06-06 16:16:55 - shooter Check Alive = true, Speed = 670 ms
[INFO]: 2025-06-06 16:16:55 - Alive Supplier: xunlei
[INFO]: 2025-06-06 16:16:55 - Alive Supplier: shooter
[INFO]: 2025-06-06 16:16:55 - Check Sub Supplier End
[INFO]: 2025-06-06 16:16:55 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 16:16:55 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 16:16:55 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 16:16:55 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 16:16:55 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 16:16:55 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 16:16:55 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 16:16:55 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 16:16:55 - Download.SupplierCheck() End
[INFO]: 2025-06-06 16:19:41 - LiteMode is true
[INFO]: 2025-06-06 16:19:41 - ChineseSubFinder Version: v0.55.3 Lite
[INFO]: 2025-06-06 16:19:41 - Reload Log Settings, level = Info
[INFO]: 2025-06-06 16:19:41 - Speed Dev Mode is Off
[INFO]: 2025-06-06 16:19:41 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 16:19:41 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 16:19:41 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 16:19:41 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 16:19:41 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 16:19:41 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 16:19:41 - UseHttpProxy = false
[ERROR]: 2025-06-06 16:19:46 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-06 16:19:46 - Check Sub Supplier Start...
[INFO]: 2025-06-06 16:19:46 - xunlei Check Alive = true, Speed = 65 ms
[ERROR]: 2025-06-06 16:19:46 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 16:19:46 - a4k Check Alive = false
[INFO]: 2025-06-06 16:19:47 - shooter Check Alive = true, Speed = 622 ms
[INFO]: 2025-06-06 16:19:47 - Alive Supplier: xunlei
[INFO]: 2025-06-06 16:19:47 - Alive Supplier: shooter
[INFO]: 2025-06-06 16:19:47 - Check Sub Supplier End
[INFO]: 2025-06-06 16:19:47 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 16:19:47 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 16:19:47 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 16:19:47 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 16:19:47 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 16:19:47 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 16:19:47 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 16:19:47 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 16:19:47 - Download.SupplierCheck() End
[INFO]: 2025-06-06 16:19:48 - Tmdb Api is Alive 382
[INFO]: 2025-06-06 16:19:48 - Try Start Http Server At Port 19035
[INFO]: 2025-06-06 16:19:48 - Setup is Done
[INFO]: 2025-06-06 16:19:48 - PreJob Will Start...
[INFO]: 2025-06-06 16:19:48 - PreJob.HotFix() Start...
[INFO]: 2025-06-06 16:19:48 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-06 16:19:48 - PreJob.HotFix() End
[INFO]: 2025-06-06 16:19:48 - PreJob.ChangeSubNameFormat() Start...
[INFO]: 2025-06-06 16:19:48 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-06 16:19:48 - DesSubFormatter == LateTimeSubFormatter then skip process
[INFO]: 2025-06-06 16:19:48 - PreJob.ChangeSubNameFormat() End
[INFO]: 2025-06-06 16:19:48 - PreJob.Wait() Done.
[INFO]: 2025-06-06 16:19:48 - Setup is Done
[INFO]: 2025-06-06 16:19:48 - RunAtStartup: false, so will not Run At Startup
[INFO]: 2025-06-06 16:19:48 - CronHelper Start...
[INFO]: 2025-06-06 16:19:48 - Next Sub Scan Will Process At: 2025-06-06 20:08:00
[INFO]: 2025-06-06 16:55:45 - LiteMode is true
[INFO]: 2025-06-06 16:55:45 - ChineseSubFinder Version: v0.55.3 Lite
[INFO]: 2025-06-06 16:55:45 - Reload Log Settings, level = Info
[INFO]: 2025-06-06 16:55:45 - Speed Dev Mode is Off
[INFO]: 2025-06-06 16:55:45 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 16:55:45 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 16:55:45 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 16:55:45 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 16:55:45 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 16:55:45 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 16:55:45 - UseHttpProxy = false
[ERROR]: 2025-06-06 16:55:50 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-06 16:55:50 - Check Sub Supplier Start...
[INFO]: 2025-06-06 16:55:50 - xunlei Check Alive = true, Speed = 75 ms
[ERROR]: 2025-06-06 16:55:50 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 16:55:50 - a4k Check Alive = false
[INFO]: 2025-06-06 16:55:52 - shooter Check Alive = true, Speed = 2539 ms
[INFO]: 2025-06-06 16:55:52 - Alive Supplier: xunlei
[INFO]: 2025-06-06 16:55:52 - Alive Supplier: shooter
[INFO]: 2025-06-06 16:55:52 - Check Sub Supplier End
[INFO]: 2025-06-06 16:55:52 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 16:55:52 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 16:55:52 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 16:55:52 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 16:55:52 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 16:55:52 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 16:55:52 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 16:55:52 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 16:55:52 - Download.SupplierCheck() End
[INFO]: 2025-06-06 16:55:53 - Tmdb Api is Alive 382
[INFO]: 2025-06-06 16:55:53 - Try Start Http Server At Port 19035
[INFO]: 2025-06-06 16:55:53 - Setup is Done
[INFO]: 2025-06-06 16:55:53 - PreJob Will Start...
[INFO]: 2025-06-06 16:55:53 - PreJob.HotFix() Start...
[INFO]: 2025-06-06 16:55:53 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-06 16:55:53 - PreJob.HotFix() End
[INFO]: 2025-06-06 16:55:53 - PreJob.ChangeSubNameFormat() Start...
[INFO]: 2025-06-06 16:55:53 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-06 16:55:53 - DesSubFormatter == LateTimeSubFormatter then skip process
[INFO]: 2025-06-06 16:55:53 - PreJob.ChangeSubNameFormat() End
[INFO]: 2025-06-06 16:55:53 - PreJob.Wait() Done.
[INFO]: 2025-06-06 16:55:53 - Setup is Done
[INFO]: 2025-06-06 16:55:53 - RunAtStartup: false, so will not Run At Startup
[INFO]: 2025-06-06 16:55:53 - CronHelper Start...
[INFO]: 2025-06-06 16:55:53 - Next Sub Scan Will Process At: 2025-06-06 20:08:00
[INFO]: 2025-06-06 17:55:53 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 17:55:53 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 17:55:53 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 17:55:53 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 17:55:53 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 17:55:53 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 17:55:53 - UseHttpProxy = false
[INFO]: 2025-06-06 17:55:53 - UrlConnectednessTest Target Site https://baidu.com Speed: 237 ms, Status: true
[INFO]: 2025-06-06 17:55:53 - Check Sub Supplier Start...
[ERROR]: 2025-06-06 17:55:53 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 17:55:53 - a4k Check Alive = false
[INFO]: 2025-06-06 17:55:53 - xunlei Check Alive = true, Speed = 129 ms
[INFO]: 2025-06-06 17:55:53 - shooter Check Alive = true, Speed = 630 ms
[INFO]: 2025-06-06 17:55:53 - Alive Supplier: xunlei
[INFO]: 2025-06-06 17:55:53 - Alive Supplier: shooter
[INFO]: 2025-06-06 17:55:53 - Check Sub Supplier End
[INFO]: 2025-06-06 17:55:53 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 17:55:53 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 17:55:53 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 17:55:53 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 17:55:53 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 17:55:53 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 17:55:53 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 17:55:53 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 17:55:53 - Download.SupplierCheck() End
[INFO]: 2025-06-06 18:55:53 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 18:55:53 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 18:55:53 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 18:55:53 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 18:55:53 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 18:55:53 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 18:55:53 - UseHttpProxy = false
[ERROR]: 2025-06-06 18:55:53 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": EOF
[INFO]: 2025-06-06 18:55:53 - Check Sub Supplier Start...
[INFO]: 2025-06-06 18:55:53 - xunlei Check Alive = true, Speed = 99 ms
[ERROR]: 2025-06-06 18:55:53 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 18:55:53 - a4k Check Alive = false
[INFO]: 2025-06-06 18:55:53 - shooter Check Alive = true, Speed = 575 ms
[INFO]: 2025-06-06 18:55:53 - Alive Supplier: xunlei
[INFO]: 2025-06-06 18:55:53 - Alive Supplier: shooter
[INFO]: 2025-06-06 18:55:53 - Check Sub Supplier End
[INFO]: 2025-06-06 18:55:53 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 18:55:53 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 18:55:53 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 18:55:53 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 18:55:53 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 18:55:53 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 18:55:53 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 18:55:53 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 18:55:53 - Download.SupplierCheck() End
[INFO]: 2025-06-06 19:55:53 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 19:55:53 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 19:55:53 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 19:55:53 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 19:55:53 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 19:55:53 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 19:55:53 - UseHttpProxy = false
[INFO]: 2025-06-06 19:55:54 - UrlConnectednessTest Target Site https://baidu.com Speed: 1591 ms, Status: true
[INFO]: 2025-06-06 19:55:54 - Check Sub Supplier Start...
[INFO]: 2025-06-06 19:55:54 - xunlei Check Alive = true, Speed = 65 ms
[ERROR]: 2025-06-06 19:55:54 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 19:55:54 - a4k Check Alive = false
[INFO]: 2025-06-06 19:55:55 - shooter Check Alive = true, Speed = 835 ms
[INFO]: 2025-06-06 19:55:55 - Alive Supplier: xunlei
[INFO]: 2025-06-06 19:55:55 - Alive Supplier: shooter
[INFO]: 2025-06-06 19:55:55 - Check Sub Supplier End
[INFO]: 2025-06-06 19:55:55 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 19:55:55 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 19:55:55 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 19:55:55 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 19:55:55 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 19:55:55 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 19:55:55 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 19:55:55 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 19:55:55 - Download.SupplierCheck() End
[INFO]: 2025-06-06 20:08:00 - scanVideoProcessAdd2DownloadQueue Start: 2025-06-06 20:08:00
[INFO]: 2025-06-06 20:08:00 - ------------------------------------
[INFO]: 2025-06-06 20:08:00 - Video Scan Started...
[INFO]: 2025-06-06 20:08:00 - ScanNormalMovieAndSeries Start...
[INFO]: 2025-06-06 20:08:00 -  --------------------------------------------------
[INFO]: 2025-06-06 20:08:00 - MatchedVideoFileFromDirs Start...
[INFO]: 2025-06-06 20:08:00 - ------------------------------------------
[INFO]: 2025-06-06 20:08:00 - GetSeriesListFromDirs Start...
[INFO]: 2025-06-06 20:08:00 - MatchedVideoFileFromDirs End
[INFO]: 2025-06-06 20:08:00 -  --------------------------------------------------
[INFO]: 2025-06-06 20:08:00 - GetSeriesListFromDirs End
[INFO]: 2025-06-06 20:08:00 - ------------------------------------------
[INFO]: 2025-06-06 20:08:00 - ScanNormalMovieAndSeries End
[INFO]: 2025-06-06 20:08:00 - ScanEmbyMovieAndSeries Start...
[INFO]: 2025-06-06 20:08:00 - Not Forced Scan And DownSub
[INFO]: 2025-06-06 20:08:00 - Movie Sub Dl From Emby API...
[INFO]: 2025-06-06 20:08:00 - Refresh Emby Sub List Start...
[ERROR]: 2025-06-06 20:08:21 - Refresh Emby Sub List Error
[ERROR]: 2025-06-06 20:08:21 - refreshEmbySubList Get "http://10.0.0.236:8096/emby/Items?Filters=IsNotFolder&IncludeItemTypes=Episode%2CMovie&IsUnaired=false&Limit=1000000&Recursive=true&SortBy=DateCreated&SortOrder=Descending&api_key=626d06abd06545529572a37c9a6b1392": dial tcp 10.0.0.236:8096: connect: connection refused
[INFO]: 2025-06-06 20:08:21 - ScanEmbyMovieAndSeries End
[ERROR]: 2025-06-06 20:08:21 - ScanEmbyMovieAndSeries Get "http://10.0.0.236:8096/emby/Items?Filters=IsNotFolder&IncludeItemTypes=Episode%2CMovie&IsUnaired=false&Limit=1000000&Recursive=true&SortBy=DateCreated&SortOrder=Descending&api_key=626d06abd06545529572a37c9a6b1392": dial tcp 10.0.0.236:8096: connect: connection refused
[INFO]: 2025-06-06 20:08:21 - VideoScanAndRefreshHelper finished, cost: 0.3509804179166667 min
[INFO]: 2025-06-06 20:08:21 - Video Scan End
[INFO]: 2025-06-06 20:08:21 - ------------------------------------
[ERROR]: 2025-06-06 20:08:21 - Get "http://10.0.0.236:8096/emby/Items?Filters=IsNotFolder&IncludeItemTypes=Episode%2CMovie&IsUnaired=false&Limit=1000000&Recursive=true&SortBy=DateCreated&SortOrder=Descending&api_key=626d06abd06545529572a37c9a6b1392": dial tcp 10.0.0.236:8096: connect: connection refused
[INFO]: 2025-06-06 20:55:53 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 20:55:53 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 20:55:53 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 20:55:53 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 20:55:53 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 20:55:53 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 20:55:53 - UseHttpProxy = false
[ERROR]: 2025-06-06 20:55:58 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-06 20:55:58 - Check Sub Supplier Start...
[INFO]: 2025-06-06 20:55:58 - xunlei Check Alive = true, Speed = 60 ms
[ERROR]: 2025-06-06 20:55:58 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 20:55:58 - a4k Check Alive = false
[INFO]: 2025-06-06 20:55:59 - shooter Check Alive = true, Speed = 1086 ms
[INFO]: 2025-06-06 20:55:59 - Alive Supplier: xunlei
[INFO]: 2025-06-06 20:55:59 - Alive Supplier: shooter
[INFO]: 2025-06-06 20:55:59 - Check Sub Supplier End
[INFO]: 2025-06-06 20:55:59 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 20:55:59 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 20:55:59 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 20:55:59 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 20:55:59 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 20:55:59 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 20:55:59 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 20:55:59 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 20:55:59 - Download.SupplierCheck() End
[INFO]: 2025-06-06 21:55:53 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 21:55:53 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 21:55:53 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 21:55:53 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 21:55:53 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 21:55:53 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 21:55:53 - UseHttpProxy = false
[ERROR]: 2025-06-06 21:55:58 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-06 21:55:58 - Check Sub Supplier Start...
[INFO]: 2025-06-06 21:55:58 - xunlei Check Alive = true, Speed = 69 ms
[ERROR]: 2025-06-06 21:55:58 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 21:55:58 - a4k Check Alive = false
[INFO]: 2025-06-06 21:56:00 - shooter Check Alive = true, Speed = 2135 ms
[INFO]: 2025-06-06 21:56:00 - Alive Supplier: xunlei
[INFO]: 2025-06-06 21:56:00 - Alive Supplier: shooter
[INFO]: 2025-06-06 21:56:00 - Check Sub Supplier End
[INFO]: 2025-06-06 21:56:00 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 21:56:00 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 21:56:00 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 21:56:00 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 21:56:00 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 21:56:00 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 21:56:00 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 21:56:00 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 21:56:00 - Download.SupplierCheck() End
[INFO]: 2025-06-06 22:55:53 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 22:55:53 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 22:55:53 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 22:55:53 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 22:55:53 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 22:55:53 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 22:55:53 - UseHttpProxy = false
[INFO]: 2025-06-06 22:55:53 - UrlConnectednessTest Target Site https://baidu.com Speed: 324 ms, Status: true
[INFO]: 2025-06-06 22:55:53 - Check Sub Supplier Start...
[INFO]: 2025-06-06 22:55:53 - xunlei Check Alive = true, Speed = 69 ms
[ERROR]: 2025-06-06 22:55:53 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 22:55:53 - a4k Check Alive = false
[INFO]: 2025-06-06 22:56:00 - shooter Check Alive = true, Speed = 7423 ms
[INFO]: 2025-06-06 22:56:00 - Alive Supplier: xunlei
[INFO]: 2025-06-06 22:56:00 - Alive Supplier: shooter
[INFO]: 2025-06-06 22:56:00 - Check Sub Supplier End
[INFO]: 2025-06-06 22:56:00 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 22:56:00 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 22:56:00 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 22:56:00 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 22:56:00 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 22:56:00 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 22:56:00 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 22:56:00 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 22:56:00 - Download.SupplierCheck() End
[INFO]: 2025-06-06 23:55:53 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-06 23:55:53 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-06 23:55:53 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-06 23:55:53 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-06 23:55:53 - PreDownloadProcess.Init() End
[INFO]: 2025-06-06 23:55:53 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-06 23:55:53 - UseHttpProxy = false
[ERROR]: 2025-06-06 23:55:58 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-06 23:55:58 - Check Sub Supplier Start...
[ERROR]: 2025-06-06 23:55:58 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-06 23:55:58 - a4k Check Alive = false
[INFO]: 2025-06-06 23:55:58 - xunlei Check Alive = true, Speed = 154 ms
[INFO]: 2025-06-06 23:56:01 - shooter Check Alive = true, Speed = 3452 ms
[INFO]: 2025-06-06 23:56:01 - Alive Supplier: xunlei
[INFO]: 2025-06-06 23:56:01 - Alive Supplier: shooter
[INFO]: 2025-06-06 23:56:01 - Check Sub Supplier End
[INFO]: 2025-06-06 23:56:01 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-06 23:56:01 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-06 23:56:01 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-06 23:56:01 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-06 23:56:01 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-06 23:56:01 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-06 23:56:01 - PreDownloadProcess.Check() End
[INFO]: 2025-06-06 23:56:01 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-06 23:56:01 - Download.SupplierCheck() End
