[INFO]: 2025-06-07 00:26:04 - LiteMode is true
[INFO]: 2025-06-07 00:26:04 - ChineseSubFinder Version: v0.55.3 Lite
[INFO]: 2025-06-07 00:26:05 - Reload Log Settings, level = Info
[INFO]: 2025-06-07 00:26:05 - Speed Dev Mode is Off
[INFO]: 2025-06-07 00:26:05 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-07 00:26:05 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-07 00:26:05 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-07 00:26:05 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-07 00:26:05 - PreDownloadProcess.Init() End
[INFO]: 2025-06-07 00:26:05 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-07 00:26:05 - UseHttpProxy = false
[INFO]: 2025-06-07 00:26:05 - UrlConnectednessTest Target Site https://baidu.com Speed: 497 ms, Status: true
[INFO]: 2025-06-07 00:26:05 - Check Sub Supplier Start...
[ERROR]: 2025-06-07 00:26:05 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-07 00:26:05 - a4k Check Alive = false
[INFO]: 2025-06-07 00:26:05 - xunlei Check Alive = true, Speed = 192 ms
[INFO]: 2025-06-07 00:26:07 - shooter Check Alive = true, Speed = 2234 ms
[INFO]: 2025-06-07 00:26:07 - Alive Supplier: xunlei
[INFO]: 2025-06-07 00:26:07 - Alive Supplier: shooter
[INFO]: 2025-06-07 00:26:07 - Check Sub Supplier End
[INFO]: 2025-06-07 00:26:07 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-07 00:26:07 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-07 00:26:07 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-07 00:26:08 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-07 00:26:08 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-07 00:26:08 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-07 00:26:08 - PreDownloadProcess.Check() End
[INFO]: 2025-06-07 00:26:08 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-07 00:26:08 - Download.SupplierCheck() End
[INFO]: 2025-06-07 00:26:08 - Tmdb Api is Alive 382
[INFO]: 2025-06-07 00:26:08 - Try Start Http Server At Port 19035
[INFO]: 2025-06-07 00:26:08 - Setup is Done
[INFO]: 2025-06-07 00:26:08 - PreJob Will Start...
[INFO]: 2025-06-07 00:26:08 - PreJob.HotFix() Start...
[INFO]: 2025-06-07 00:26:08 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-07 00:26:08 - PreJob.HotFix() End
[INFO]: 2025-06-07 00:26:08 - PreJob.ChangeSubNameFormat() Start...
[INFO]: 2025-06-07 00:26:08 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-07 00:26:08 - DesSubFormatter == LateTimeSubFormatter then skip process
[INFO]: 2025-06-07 00:26:08 - PreJob.ChangeSubNameFormat() End
[INFO]: 2025-06-07 00:26:08 - PreJob.Wait() Done.
[INFO]: 2025-06-07 00:26:08 - Setup is Done
[INFO]: 2025-06-07 00:26:08 - RunAtStartup: false, so will not Run At Startup
[INFO]: 2025-06-07 00:26:08 - CronHelper Start...
[INFO]: 2025-06-07 00:26:08 - Next Sub Scan Will Process At: 2025-06-07 20:08:00
[INFO]: 2025-06-07 00:27:07 - LiteMode is true
[INFO]: 2025-06-07 00:27:07 - ChineseSubFinder Version: v0.55.3 Lite
[INFO]: 2025-06-07 00:27:07 - Reload Log Settings, level = Info
[INFO]: 2025-06-07 00:27:07 - Speed Dev Mode is Off
[INFO]: 2025-06-07 00:27:07 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-07 00:27:07 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-07 00:27:07 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-07 00:27:07 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-07 00:27:07 - PreDownloadProcess.Init() End
[INFO]: 2025-06-07 00:27:07 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-07 00:27:07 - UseHttpProxy = false
[INFO]: 2025-06-07 00:27:08 - UrlConnectednessTest Target Site https://baidu.com Speed: 502 ms, Status: true
[INFO]: 2025-06-07 00:27:08 - Check Sub Supplier Start...
[INFO]: 2025-06-07 00:27:08 - xunlei Check Alive = true, Speed = 82 ms
[ERROR]: 2025-06-07 00:27:08 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-07 00:27:08 - a4k Check Alive = false
[INFO]: 2025-06-07 00:27:10 - shooter Check Alive = true, Speed = 1871 ms
[INFO]: 2025-06-07 00:27:10 - Alive Supplier: xunlei
[INFO]: 2025-06-07 00:27:10 - Alive Supplier: shooter
[INFO]: 2025-06-07 00:27:10 - Check Sub Supplier End
[INFO]: 2025-06-07 00:27:10 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-07 00:27:10 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-07 00:27:10 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-07 00:27:10 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-07 00:27:10 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-07 00:27:10 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-07 00:27:10 - PreDownloadProcess.Check() End
[INFO]: 2025-06-07 00:27:10 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-07 00:27:10 - Download.SupplierCheck() End
[INFO]: 2025-06-07 00:27:12 - Tmdb Api is Alive 382
[INFO]: 2025-06-07 00:27:12 - Try Start Http Server At Port 19035
[INFO]: 2025-06-07 00:27:12 - Setup is Done
[INFO]: 2025-06-07 00:27:12 - PreJob Will Start...
[INFO]: 2025-06-07 00:27:12 - PreJob.HotFix() Start...
[INFO]: 2025-06-07 00:27:12 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-07 00:27:12 - PreJob.HotFix() End
[INFO]: 2025-06-07 00:27:12 - PreJob.ChangeSubNameFormat() Start...
[INFO]: 2025-06-07 00:27:12 - If you have a lot of videos, it may take a lot of time, just wait...
[INFO]: 2025-06-07 00:27:12 - DesSubFormatter == LateTimeSubFormatter then skip process
[INFO]: 2025-06-07 00:27:12 - PreJob.ChangeSubNameFormat() End
[INFO]: 2025-06-07 00:27:12 - PreJob.Wait() Done.
[INFO]: 2025-06-07 00:27:12 - Setup is Done
[INFO]: 2025-06-07 00:27:12 - RunAtStartup: false, so will not Run At Startup
[INFO]: 2025-06-07 00:27:12 - CronHelper Start...
[INFO]: 2025-06-07 00:27:12 - Next Sub Scan Will Process At: 2025-06-07 20:08:00
[INFO]: 2025-06-07 01:27:12 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-07 01:27:12 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-07 01:27:12 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-07 01:27:12 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-07 01:27:12 - PreDownloadProcess.Init() End
[INFO]: 2025-06-07 01:27:12 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-07 01:27:12 - UseHttpProxy = false
[ERROR]: 2025-06-07 01:27:17 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-07 01:27:17 - Check Sub Supplier Start...
[INFO]: 2025-06-07 01:27:17 - xunlei Check Alive = true, Speed = 83 ms
[ERROR]: 2025-06-07 01:27:17 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-07 01:27:17 - a4k Check Alive = false
[INFO]: 2025-06-07 01:27:18 - shooter Check Alive = true, Speed = 1331 ms
[INFO]: 2025-06-07 01:27:18 - Alive Supplier: xunlei
[INFO]: 2025-06-07 01:27:18 - Alive Supplier: shooter
[INFO]: 2025-06-07 01:27:18 - Check Sub Supplier End
[INFO]: 2025-06-07 01:27:18 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-07 01:27:18 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-07 01:27:18 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-07 01:27:18 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-07 01:27:18 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-07 01:27:18 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-07 01:27:18 - PreDownloadProcess.Check() End
[INFO]: 2025-06-07 01:27:18 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-07 01:27:18 - Download.SupplierCheck() End
[INFO]: 2025-06-07 02:27:12 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-07 02:27:12 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-07 02:27:12 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-07 02:27:12 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-07 02:27:12 - PreDownloadProcess.Init() End
[INFO]: 2025-06-07 02:27:12 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-07 02:27:12 - UseHttpProxy = false
[ERROR]: 2025-06-07 02:27:17 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-07 02:27:17 - Check Sub Supplier Start...
[ERROR]: 2025-06-07 02:27:17 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-07 02:27:17 - a4k Check Alive = false
[INFO]: 2025-06-07 02:27:17 - xunlei Check Alive = true, Speed = 158 ms
[INFO]: 2025-06-07 02:27:18 - shooter Check Alive = true, Speed = 1018 ms
[INFO]: 2025-06-07 02:27:18 - Alive Supplier: xunlei
[INFO]: 2025-06-07 02:27:18 - Alive Supplier: shooter
[INFO]: 2025-06-07 02:27:18 - Check Sub Supplier End
[INFO]: 2025-06-07 02:27:18 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-07 02:27:18 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-07 02:27:18 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-07 02:27:18 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-07 02:27:18 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-07 02:27:18 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-07 02:27:18 - PreDownloadProcess.Check() End
[INFO]: 2025-06-07 02:27:18 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-07 02:27:18 - Download.SupplierCheck() End
[INFO]: 2025-06-07 03:27:12 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-07 03:27:12 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-07 03:27:12 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-07 03:27:12 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-07 03:27:12 - PreDownloadProcess.Init() End
[INFO]: 2025-06-07 03:27:12 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-07 03:27:12 - UseHttpProxy = false
[INFO]: 2025-06-07 03:27:12 - UrlConnectednessTest Target Site https://baidu.com Speed: 205 ms, Status: true
[INFO]: 2025-06-07 03:27:12 - Check Sub Supplier Start...
[INFO]: 2025-06-07 03:27:12 - xunlei Check Alive = true, Speed = 99 ms
[ERROR]: 2025-06-07 03:27:12 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-07 03:27:12 - a4k Check Alive = false
[INFO]: 2025-06-07 03:27:12 - shooter Check Alive = true, Speed = 585 ms
[INFO]: 2025-06-07 03:27:12 - Alive Supplier: xunlei
[INFO]: 2025-06-07 03:27:12 - Alive Supplier: shooter
[INFO]: 2025-06-07 03:27:12 - Check Sub Supplier End
[INFO]: 2025-06-07 03:27:12 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-07 03:27:12 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-07 03:27:12 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-07 03:27:12 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-07 03:27:12 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-07 03:27:12 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-07 03:27:12 - PreDownloadProcess.Check() End
[INFO]: 2025-06-07 03:27:12 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-07 03:27:12 - Download.SupplierCheck() End
[INFO]: 2025-06-07 04:27:12 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-07 04:27:12 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-07 04:27:12 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-07 04:27:12 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-07 04:27:12 - PreDownloadProcess.Init() End
[INFO]: 2025-06-07 04:27:12 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-07 04:27:12 - UseHttpProxy = false
[INFO]: 2025-06-07 04:27:12 - UrlConnectednessTest Target Site https://baidu.com Speed: 545 ms, Status: true
[INFO]: 2025-06-07 04:27:12 - Check Sub Supplier Start...
[INFO]: 2025-06-07 04:27:12 - xunlei Check Alive = true, Speed = 66 ms
[ERROR]: 2025-06-07 04:27:12 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-07 04:27:12 - a4k Check Alive = false
[INFO]: 2025-06-07 04:27:13 - shooter Check Alive = true, Speed = 590 ms
[INFO]: 2025-06-07 04:27:13 - Alive Supplier: xunlei
[INFO]: 2025-06-07 04:27:13 - Alive Supplier: shooter
[INFO]: 2025-06-07 04:27:13 - Check Sub Supplier End
[INFO]: 2025-06-07 04:27:13 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-07 04:27:13 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-07 04:27:13 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-07 04:27:13 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-07 04:27:13 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-07 04:27:13 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-07 04:27:13 - PreDownloadProcess.Check() End
[INFO]: 2025-06-07 04:27:13 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-07 04:27:13 - Download.SupplierCheck() End
[INFO]: 2025-06-07 05:27:12 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-07 05:27:12 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-07 05:27:12 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-07 05:27:12 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-07 05:27:12 - PreDownloadProcess.Init() End
[INFO]: 2025-06-07 05:27:12 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-07 05:27:12 - UseHttpProxy = false
[ERROR]: 2025-06-07 05:27:17 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-07 05:27:17 - Check Sub Supplier Start...
[INFO]: 2025-06-07 05:27:17 - xunlei Check Alive = true, Speed = 85 ms
[ERROR]: 2025-06-07 05:27:17 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-07 05:27:17 - a4k Check Alive = false
[INFO]: 2025-06-07 05:27:17 - shooter Check Alive = true, Speed = 600 ms
[INFO]: 2025-06-07 05:27:17 - Alive Supplier: xunlei
[INFO]: 2025-06-07 05:27:17 - Alive Supplier: shooter
[INFO]: 2025-06-07 05:27:17 - Check Sub Supplier End
[INFO]: 2025-06-07 05:27:17 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-07 05:27:17 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-07 05:27:17 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-07 05:27:17 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-07 05:27:17 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-07 05:27:17 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-07 05:27:17 - PreDownloadProcess.Check() End
[INFO]: 2025-06-07 05:27:17 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-07 05:27:17 - Download.SupplierCheck() End
[INFO]: 2025-06-07 06:27:12 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-07 06:27:12 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-07 06:27:12 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-07 06:27:12 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-07 06:27:12 - PreDownloadProcess.Init() End
[INFO]: 2025-06-07 06:27:12 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-07 06:27:12 - UseHttpProxy = false
[INFO]: 2025-06-07 06:27:12 - UrlConnectednessTest Target Site https://baidu.com Speed: 167 ms, Status: true
[INFO]: 2025-06-07 06:27:12 - Check Sub Supplier Start...
[INFO]: 2025-06-07 06:27:12 - xunlei Check Alive = true, Speed = 59 ms
[ERROR]: 2025-06-07 06:27:12 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-07 06:27:12 - a4k Check Alive = false
[INFO]: 2025-06-07 06:27:12 - shooter Check Alive = true, Speed = 631 ms
[INFO]: 2025-06-07 06:27:12 - Alive Supplier: xunlei
[INFO]: 2025-06-07 06:27:12 - Alive Supplier: shooter
[INFO]: 2025-06-07 06:27:12 - Check Sub Supplier End
[INFO]: 2025-06-07 06:27:12 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-07 06:27:12 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-07 06:27:12 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-07 06:27:12 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-07 06:27:12 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-07 06:27:12 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-07 06:27:12 - PreDownloadProcess.Check() End
[INFO]: 2025-06-07 06:27:12 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-07 06:27:12 - Download.SupplierCheck() End
[INFO]: 2025-06-07 07:27:12 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-07 07:27:12 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-07 07:27:12 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-07 07:27:12 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-07 07:27:12 - PreDownloadProcess.Init() End
[INFO]: 2025-06-07 07:27:12 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-07 07:27:12 - UseHttpProxy = false
[ERROR]: 2025-06-07 07:27:17 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-07 07:27:17 - Check Sub Supplier Start...
[ERROR]: 2025-06-07 07:27:17 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-07 07:27:17 - a4k Check Alive = false
[INFO]: 2025-06-07 07:27:17 - xunlei Check Alive = true, Speed = 125 ms
[INFO]: 2025-06-07 07:27:17 - shooter Check Alive = true, Speed = 690 ms
[INFO]: 2025-06-07 07:27:17 - Alive Supplier: xunlei
[INFO]: 2025-06-07 07:27:17 - Alive Supplier: shooter
[INFO]: 2025-06-07 07:27:17 - Check Sub Supplier End
[INFO]: 2025-06-07 07:27:17 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-07 07:27:17 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-07 07:27:17 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-07 07:27:17 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-07 07:27:17 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-07 07:27:17 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-07 07:27:17 - PreDownloadProcess.Check() End
[INFO]: 2025-06-07 07:27:17 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-07 07:27:17 - Download.SupplierCheck() End
[INFO]: 2025-06-07 08:27:12 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-07 08:27:12 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-07 08:27:12 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-07 08:27:12 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-07 08:27:12 - PreDownloadProcess.Init() End
[INFO]: 2025-06-07 08:27:12 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-07 08:27:12 - UseHttpProxy = false
[ERROR]: 2025-06-07 08:27:12 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": EOF
[INFO]: 2025-06-07 08:27:12 - Check Sub Supplier Start...
[ERROR]: 2025-06-07 08:27:12 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-07 08:27:12 - a4k Check Alive = false
[INFO]: 2025-06-07 08:27:12 - xunlei Check Alive = true, Speed = 270 ms
[INFO]: 2025-06-07 08:27:12 - shooter Check Alive = true, Speed = 639 ms
[INFO]: 2025-06-07 08:27:12 - Alive Supplier: xunlei
[INFO]: 2025-06-07 08:27:12 - Alive Supplier: shooter
[INFO]: 2025-06-07 08:27:12 - Check Sub Supplier End
[INFO]: 2025-06-07 08:27:12 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-07 08:27:12 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-07 08:27:12 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-07 08:27:12 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-07 08:27:12 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-07 08:27:12 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-07 08:27:12 - PreDownloadProcess.Check() End
[INFO]: 2025-06-07 08:27:12 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-07 08:27:12 - Download.SupplierCheck() End
[INFO]: 2025-06-07 09:27:12 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-07 09:27:12 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-07 09:27:12 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-07 09:27:12 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-07 09:27:12 - PreDownloadProcess.Init() End
[INFO]: 2025-06-07 09:27:12 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-07 09:27:12 - UseHttpProxy = false
[ERROR]: 2025-06-07 09:27:17 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": context deadline exceeded (Client.Timeout exceeded while awaiting headers)
[INFO]: 2025-06-07 09:27:17 - Check Sub Supplier Start...
[INFO]: 2025-06-07 09:27:17 - xunlei Check Alive = true, Speed = 61 ms
[ERROR]: 2025-06-07 09:27:17 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-07 09:27:17 - a4k Check Alive = false
[INFO]: 2025-06-07 09:27:17 - shooter Check Alive = true, Speed = 619 ms
[INFO]: 2025-06-07 09:27:17 - Alive Supplier: xunlei
[INFO]: 2025-06-07 09:27:17 - Alive Supplier: shooter
[INFO]: 2025-06-07 09:27:17 - Check Sub Supplier End
[INFO]: 2025-06-07 09:27:17 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-07 09:27:17 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-07 09:27:17 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-07 09:27:17 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-07 09:27:17 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-07 09:27:17 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-07 09:27:17 - PreDownloadProcess.Check() End
[INFO]: 2025-06-07 09:27:17 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-07 09:27:17 - Download.SupplierCheck() End
[INFO]: 2025-06-07 10:27:12 - Download.SupplierCheck() Start ...
[INFO]: 2025-06-07 10:27:12 - PreDownloadProcess.Init().Check().Wait()...
[INFO]: 2025-06-07 10:27:12 - PreDownloadProcess.Init() Start...
[INFO]: 2025-06-07 10:27:12 - ClearRodTmpRootFolder Done
[INFO]: 2025-06-07 10:27:12 - PreDownloadProcess.Init() End
[INFO]: 2025-06-07 10:27:12 - PreDownloadProcess.Check() Start...
[INFO]: 2025-06-07 10:27:12 - UseHttpProxy = false
[ERROR]: 2025-06-07 10:27:12 - UrlConnectednessTest Target Site https://baidu.com, Get "https://baidu.com": EOF
[INFO]: 2025-06-07 10:27:12 - Check Sub Supplier Start...
[ERROR]: 2025-06-07 10:27:12 - a4k CheckAlive.Get Get "https://www.a4k.net": dial tcp: lookup www.a4k.net on 127.0.0.11:53: no such host
[WARNING]: 2025-06-07 10:27:12 - a4k Check Alive = false
[INFO]: 2025-06-07 10:27:12 - xunlei Check Alive = true, Speed = 176 ms
[INFO]: 2025-06-07 10:27:12 - shooter Check Alive = true, Speed = 678 ms
[INFO]: 2025-06-07 10:27:12 - Alive Supplier: xunlei
[INFO]: 2025-06-07 10:27:12 - Alive Supplier: shooter
[INFO]: 2025-06-07 10:27:12 - Check Sub Supplier End
[INFO]: 2025-06-07 10:27:12 - MovieFolder Index 0 -- /media/动画电影
[INFO]: 2025-06-07 10:27:12 - MovieFolder Index 1 -- /media/华语电影
[INFO]: 2025-06-07 10:27:12 - MovieFolder Index 2 -- /media/外语电影
[INFO]: 2025-06-07 10:27:12 - SeriesPaths Index 0 -- /media/国产剧
[INFO]: 2025-06-07 10:27:12 - SeriesPaths Index 1 -- /media/欧美剧
[INFO]: 2025-06-07 10:27:12 - SeriesPaths Index 2 -- /media/国漫
[INFO]: 2025-06-07 10:27:12 - PreDownloadProcess.Check() End
[INFO]: 2025-06-07 10:27:12 - PreDownloadProcess.Wait() Done.
[INFO]: 2025-06-07 10:27:12 - Download.SupplierCheck() End
