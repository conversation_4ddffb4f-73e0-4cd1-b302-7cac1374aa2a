version: '3.8'

services:
  moviepilot:   
    image: docker.cnb.cool/rakin/jxxghp/moviepilot-v2:latest
    container_name: moviepilot-v2
    hostname: moviepilot-v2
    restart: unless-stopped
    stdin_open: true
    tty: true
    networks:
      - moviepilot
    ports:
      - "3000:3000"
      - "3001:3001"
    volumes:
      - ./media:/media
      - ./moviepilot/config:/config
      - ./moviepilot/core:/moviepilot/.cache/ms-playwright
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      - NGINX_PORT=3000
      - PORT=3001
      - PUID=${PUID:-1000}
      - PGID=${PGID:-1000}
      - UMASK=${UMASK:-022}
      - TZ=${TZ:-Asia/Shanghai}
      - SUPERUSER=${MOVIEPILOT_SUPERUSER}
      - PROXY_HOST=
      - GITHUB_PROXY=${GITHUB_PROXY}
      - GITHUB_TOKEN=${MOVIEPILOT_GITHUB_TOKEN}
      - PIP_PROXY=${PIP_PROXY}
      - TMDB_API_DOMAIN=${TMDB_API_DOMAIN}
      - TMDB_IMAGE_DOMAIN=${TMDB_IMAGE_DOMAIN}
      - API_TOKEN=${MOVIEPILOT_API_TOKEN}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"
    
  emby:
    image: docker.cnb.cool/rakin/amilys/embyserver:latest
    container_name: emby
    restart: unless-stopped
    networks:
      - moviepilot
    ports:
      - "8096:8096"
    volumes:
      - ./emby/config:/config
      - ./media/links:/media:ro
    environment:
      - UID=${PUID:-1000}
      - GID=${PGID:-1000}
      - GIDLIST=${PGID:-1000}
      - TZ=${TZ:-Asia/Shanghai}
    devices:
      - /dev/dri:/dev/dri
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8096/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  qbittorrent:
    image: docker.cnb.cool/rakin/linuxserver/qbittorrent:latest
    container_name: qbittorrent
    restart: unless-stopped
    networks:
      - moviepilot
    ports:
      - "8099:8080"
      - "55881:55881"
      - "55881:55881/udp"
    volumes:
      - ./qbittorrent/config:/config
      - ./media/downloads:/media/downloads
    environment:
      - PUID=${PUID:-1000}
      - PGID=${PGID:-1000}
      - TZ=${TZ:-Asia/Shanghai}
      - WEBUI_PORT=8080
      - TORRENTING_PORT=55881
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  transmission:
    image: docker.cnb.cool/rakin/linuxserver/transmission:latest
    container_name: transmission
    restart: unless-stopped
    networks:
      - moviepilot
    ports:
      - "9091:9091"
      - "51413:51413"
      - "51413:51413/udp"
    volumes:
      - ./transmission/config:/config
      - ./media/downloads:/media/downloads
    environment:
      - PUID=${PUID:-1000}
      - PGID=${PGID:-1000}
      - TZ=${TZ:-Asia/Shanghai}
      - TRANSMISSION_WEB_HOME=/config/webui
      - USER=${TRANSMISSION_USER}
      - PASS=${TRANSMISSION_PASS}
      - PEERPORT=51413
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  chinesesubfinder:
    image: docker.cnb.cool/rakin/allanpk716/chinesesubfinder:latest
    container_name: chinesesubfinder
    hostname: chinesesubfinder
    restart: unless-stopped
    volumes:
      - ./media/links:/media:ro
      - ./chinesesubfinder/config:/config
      - ./chinesesubfinder/browser:/root/.cache/rod/browser
    environment:
      - PUID=${PUID:-1000}
      - PGID=${PGID:-1000}
      - UMASK=${UMASK:-022}
      - TZ=${TZ:-Asia/Shanghai}
      - CHINESE_SUBFINDER_PORT=19035
      - CHINESE_SUBFINDER_API_PORT=19037
      - PERMS=true
    networks:
      - moviepilot
    ports:
      - "19035:19035"
      - "19037:19037"
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  watchtower:
    image: docker.cnb.cool/rakin/containrrr/watchtower:latest
    container_name: watchtower
    networks:
      - moviepilot
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      - TZ=${TZ:-Asia/Shanghai}
    command: --cleanup --interval 120 moviepilot-v2 emby
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

networks:
  moviepilot:
    name: moviepilot
    driver: bridge
