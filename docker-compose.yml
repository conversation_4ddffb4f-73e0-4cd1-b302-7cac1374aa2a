services:
  moviepilot:   
    image: docker.cnb.cool/rakin/jxxghp/moviepilot-v2:latest
    container_name: moviepilot-v2
    hostname: moviepilot-v2
    privileged: true
    restart: always
    stdin_open: true
    tty: true
    networks:
      - moviepilot
    ports:
      - 3000:3000
      - 3001:3001
    volumes:
      - ./media:/media
      - ./moviepilot/config:/config
      - ./moviepilot/core:/moviepilot/.cache/ms-playwright
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      - NGINX_PORT=3000
      - PORT=3001
      - PUID=0
      - PGID=0
      - UMASK=022
      - TZ=Asia/Shanghai
      - SUPERUSER=admin
      - PROXY_HOST=
      - GITHUB_PROXY=https://ghfast.top/
      - GITHUB_TOKEN=****************************************
      - PIP_PROXY=https://pypi.mirrors.ustc.edu.cn/simple
      - TMDB_API_DOMAIN=tmdb.movie-pilot.org
      - TMDB_IMAGE_DOMAIN=static-mdb.v.geilijiasu.com
      - API_TOKEN=AU2pOSFIAdUiLKFM9TU88w
    
  emby:
    image: docker.cnb.cool/rakin/amilys/embyserver:latest
    container_name: emby
    restart: always
    # runtime: nvidia
    privileged: true
    stdin_open: true
    tty: true
    networks:
      - moviepilot
    ports:
      - 8096:8096
    volumes:
      - ./emby/config:/config
      - ./media/links:/media
    environment:
      - UID=1000
      - GID=100
      - GIDLIST=100
    devices:
      - /dev/dri:/dev/dri
    logging:
      driver: "json-file"
      options:
          max-size: "5m"

  qbittorrent:
    image: docker.cnb.cool/rakin/linuxserver/qbittorrent:latest
    container_name: qbittorrent
    network_mode: host
    restart: always
    privileged: true
    stdin_open: true
    tty: true
    volumes:
      - ./qbittorrent/config:/config
      - ./media/downloads:/media/downloads
    environment:
      - PUID=0
      - PGID=0
      - TZ=Asia/Shanghai
      - WEBUI_PORT=8099
      - TORRENTING_PORT=55881
    logging:
      driver: "json-file"
      options:
          max-size: "5m"

  transmission:
    image: docker.cnb.cool/rakin/linuxserver/transmission:latest
    container_name: transmission
    network_mode: host
    restart: always
    privileged: true
    stdin_open: true
    tty: true
    volumes:
      - ./transmission/config:/config
      - ./media/downloads:/media/downloads
    environment:
      - PUID=0
      - PGID=0
      - TZ=Asia/Shanghai
      - TRANSMISSION_WEB_HOME=/config/webui
      - USER=admin
      - PASS=Abc.147258
      - PEERPORT=51413
      - WHITELIST=
      - PEERPORT=
      - HOST_WHITELIST=
    logging:
      driver: "json-file"
      options:
          max-size: "5m"

  chinesesubfinder:
    image: docker.cnb.cool/rakin/allanpk716/chinesesubfinder:latest
    container_name: chinesesubfinder
    hostname: chinesesubfinder
    restart: always
    privileged: true
    stdin_open: true
    tty: true
    volumes:
      - ./media/links:/media
      - ./chinesesubfinder/config:/config
      - ./chinesesubfinder/browser:/root/.cache/rod/browser
    environment:
      - PUID=0
      - PGID=0
      - UMASK=022
      - TZ=Asia/Shanghai
      - CHINESE_SUBFINDER_PORT=19035
      - CHINESE_SUBFINDER_API_PORT=19037
      - PERMS=true
    networks:
      - moviepilot
    ports:
      - 19035:19035
      - 19037:19037
    logging:
      driver: "json-file"
      options:
        max-size: "100m"



  watchtower:
    image: docker.cnb.cool/rakin/containrrr/watchtower:latest
    container_name: watchtower
    networks:
      - moviepilot
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    environment:
      - TZ=Asia/Shanghai
    command: --cleanup --interval 120 moviepilot-v2 emby
    logging:
      driver: "json-file"
      options:
          max-size: "5m"

networks:
  moviepilot:
    name: moviepilot