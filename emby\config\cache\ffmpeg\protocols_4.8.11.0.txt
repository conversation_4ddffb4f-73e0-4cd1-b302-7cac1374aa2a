﻿Supported file protocols:
Input:
  async
  cache
  concat
  concatf
  crypto
  data
  ffrtmpcrypt
  ffrtmphttp
  file
  ftp
  gopher
  gophers
  hls
  http
  httpproxy
  https
  mmsh
  mmst
  pipe
  rtmp
  rtmpe
  rtmps
  rtmpt
  rtmpte
  rtmpts
  rtp
  srtp
  subfile
  tcp
  tls
  udp
  udplite
  unix
  ipfs
  ipns
Output:
  crypto
  ffrtmpcrypt
  ffrtmphttp
  file
  ftp
  gopher
  gophers
  http
  httpproxy
  https
  icecast
  md5
  pipe
  prompeg
  rtmp
  rtmpe
  rtmps
  rtmpt
  rtmpte
  rtmpts
  rtp
  srtp
  tee
  tcp
  tls
  udp
  udplite
  unix

