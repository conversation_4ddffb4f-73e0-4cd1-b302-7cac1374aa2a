﻿ffmpeg version 5.1-emby_2023_06_25 Copyright (c) 2000-2022 the FFmpeg developers and softworkz for Emby LLC
built with gcc 10.3.0 (crosstool-NG 1.25.0)
configuration: --cc=x86_64-emby-linux-gnu-gcc --prefix=/home/<USER>/Buildbot/x64/ffmpeg-x64/staging --disable-alsa --disable-doc --disable-ffplay --disable-gnutls --disable-libpulse --disable-librtmp --disable-libxcb --disable-openssl --disable-vdpau --disable-vulkan --disable-xlib --enable-chromaprint --enable-fontconfig --enable-gpl --enable-iconv --enable-libaribb24 --enable-libass --enable-libdav1d --enable-libfreetype --enable-libfribidi --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libvpx --enable-libwebp --enable-libx264 --enable-libx265 --enable-libzvbi --enable-mbedtls --enable-pic --enable-version3 --enable-libtesseract --enable-cuda-llvm --enable-cuvid --enable-libdrm --enable-libmfx --enable-nvdec --enable-nvenc --enable-vaapi --enable-opencl --enable-cross-compile --cross-prefix=x86_64-emby-linux-gnu- --arch=x86_64 --target-os=linux --enable-shared --disable-static --pkg-config=pkg-config --pkg-config-flags=--static --extra-libs='-lm -lstdc++ -lsharpyuv -pthread' --disable-debug
libavutil      57. 28.100 / 57. 28.100
libavcodec     59. 37.100 / 59. 37.100
libavformat    59. 27.100 / 59. 27.100
libavdevice    59.  7.100 / 59.  7.100
libavfilter     8. 44.100 /  8. 44.100
libswscale      6.  7.100 /  6.  7.100
libswresample   4.  7.100 /  4.  7.100
libpostproc    56.  6.100 / 56.  6.100
EXIT

