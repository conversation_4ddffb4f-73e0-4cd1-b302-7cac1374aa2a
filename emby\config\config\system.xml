<?xml version="1.0"?>
<ServerConfiguration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema">
  <EnableDebugLevelLogging>false</EnableDebugLevelLogging>
  <EnableAutoUpdate>true</EnableAutoUpdate>
  <LogFileRetentionDays>3</LogFileRetentionDays>
  <RunAtStartup>true</RunAtStartup>
  <IsStartupWizardCompleted>true</IsStartupWizardCompleted>
  <EnableUPnP>true</EnableUPnP>
  <PublicPort>8096</PublicPort>
  <PublicHttpsPort>8920</PublicHttpsPort>
  <HttpServerPortNumber>8096</HttpServerPortNumber>
  <HttpsPortNumber>8920</HttpsPortNumber>
  <EnableHttps>false</EnableHttps>
  <IsPortAuthorized>true</IsPortAuthorized>
  <AutoRunWebApp>true</AutoRunWebApp>
  <EnableRemoteAccess>true</EnableRemoteAccess>
  <LogAllQueryTimes>false</LogAllQueryTimes>
  <EnableCaseSensitiveItemIds>true</EnableCaseSensitiveItemIds>
  <PreferredMetadataLanguage>en</PreferredMetadataLanguage>
  <MetadataCountryCode>US</MetadataCountryCode>
  <SortRemoveWords>
    <string>the</string>
    <string>a</string>
    <string>an</string>
    <string>das</string>
    <string>der</string>
    <string>el</string>
    <string>la</string>
  </SortRemoveWords>
  <LibraryMonitorDelaySeconds>90</LibraryMonitorDelaySeconds>
  <EnableDashboardResponseCaching>true</EnableDashboardResponseCaching>
  <ImageSavingConvention>Compatible</ImageSavingConvention>
  <EnableAutomaticRestart>true</EnableAutomaticRestart>
  <ServerName>MediaServer</ServerName>
  <PreferredDetectedRemoteAddressFamily>InterNetwork</PreferredDetectedRemoteAddressFamily>
  <UICulture>zh-CN</UICulture>
  <RemoteClientBitrateLimit>0</RemoteClientBitrateLimit>
  <LocalNetworkSubnets />
  <LocalNetworkAddresses />
  <EnableExternalContentInSuggestions>true</EnableExternalContentInSuggestions>
  <RequireHttps>false</RequireHttps>
  <IsBehindProxy>false</IsBehindProxy>
  <RemoteIPFilter />
  <IsRemoteIPFilterBlacklist>false</IsRemoteIPFilterBlacklist>
  <ImageExtractionTimeoutMs>0</ImageExtractionTimeoutMs>
  <PathSubstitutions />
  <UninstalledPlugins />
  <CollapseVideoFolders>false</CollapseVideoFolders>
  <EnableOriginalTrackTitles>false</EnableOriginalTrackTitles>
  <VacuumDatabaseOnStartup>false</VacuumDatabaseOnStartup>
  <SimultaneousStreamLimit>0</SimultaneousStreamLimit>
  <DatabaseCacheSizeMB>128</DatabaseCacheSizeMB>
  <EnableSqLiteMmio>false</EnableSqLiteMmio>
  <PlaylistsUpgradedToM3U>true</PlaylistsUpgradedToM3U>
  <ImageExtractorUpgraded1>true</ImageExtractorUpgraded1>
  <EnablePeopleLetterSubFolders>true</EnablePeopleLetterSubFolders>
  <OptimizeDatabaseOnShutdown>true</OptimizeDatabaseOnShutdown>
  <DatabaseAnalysisLimit>400</DatabaseAnalysisLimit>
  <DisableAsyncIO>false</DisableAsyncIO>
  <MigratedToUserItemShares8>true</MigratedToUserItemShares8>
  <MigratedLibraryOptionsToDb>true</MigratedLibraryOptionsToDb>
  <AllowLegacyLocalNetworkPassword>false</AllowLegacyLocalNetworkPassword>
  <EnableSavedMetadataForPeople>false</EnableSavedMetadataForPeople>
  <TvChannelsRefreshed>true</TvChannelsRefreshed>
  <ProxyHeaderMode>AllAddresses</ProxyHeaderMode>
</ServerConfiguration>