2025-06-03 14:23:21.270 Info Main: Application path: /system/EmbyServer.dll
2025-06-03 14:23:21.366 Info NetworkManager: Adding event handler for NetworkChange.NetworkAddressChanged
2025-06-03 14:23:21.564 Info App: Setting default culture to zh-CN
2025-06-03 14:23:21.572 Info Main: Emby
	Command line: /system/EmbyServer.dll -programdata /config -ffdetect /bin/ffdetect -ffmpeg /bin/ffmpeg -ffprobe /bin/ffprobe -restartexitcode 3
	Operating system: Linux version ********-microsoft-standard-WSL2 (root@af282157c79e) (gcc (GCC) 11.2.0, GNU ld (GNU Binutils) 2.37) #1 SMP PREEMPT_DYNAMIC Mon Apr 21 17
	Framework: .NET 6.0.36
	OS/Process: x64/x64
	Runtime: system/System.Private.CoreLib.dll
	Processor count: 12
	Data path: /config
	Application path: /system
2025-06-03 14:23:21.572 Info Main: Logs path: /config/logs
2025-06-03 14:23:21.572 Info Main: Cache path: /config/cache
2025-06-03 14:23:21.572 Info Main: Internal metadata path: /config/metadata
2025-06-03 14:23:21.574 Info App: Emby Server Version: ********
2025-06-03 14:23:21.576 Info App: Loading assemblies
2025-06-03 14:23:21.645 Info App: File /config/plugins/AudioDb.dll has version ********
2025-06-03 14:23:21.646 Info App: File /system/plugins/AudioDb.dll has version 1.0.18.0
2025-06-03 14:23:21.653 Info App: File /config/plugins/MovieDb.dll has version 1.8.3.0
2025-06-03 14:23:21.654 Info App: File /system/plugins/MovieDb.dll has version 1.8.0.0
2025-06-03 14:23:21.660 Info App: File /config/plugins/OpenSubtitles.dll has version 1.0.64.0
2025-06-03 14:23:21.661 Info App: File /system/plugins/OpenSubtitles.dll has version 1.0.63.0
2025-06-03 14:23:21.667 Info App: File /config/plugins/Emby.Server.CinemaMode.dll has version 1.0.47.0
2025-06-03 14:23:21.668 Info App: File /system/plugins/Emby.Server.CinemaMode.dll has version 1.0.47.0
2025-06-03 14:23:21.675 Info App: File /config/plugins/Emby.Dlna.dll has version 1.5.0.0
2025-06-03 14:23:21.677 Info App: File /system/plugins/Emby.Dlna.dll has version 1.4.7.0
2025-06-03 14:23:21.683 Info App: File /config/plugins/NfoMetadata.dll has version 1.0.83.0
2025-06-03 14:23:21.684 Info App: File /system/plugins/NfoMetadata.dll has version 1.0.82.0
2025-06-03 14:23:21.691 Info App: File /config/plugins/Tvdb.dll has version 1.6.2.0
2025-06-03 14:23:21.692 Info App: File /system/plugins/Tvdb.dll has version 1.5.8.0
2025-06-03 14:23:21.697 Info App: File /config/plugins/StudioImages.dll has version 1.0.3.0
2025-06-03 14:23:21.698 Info App: File /system/plugins/StudioImages.dll has version 1.0.3.0
2025-06-03 14:23:21.704 Info App: File /config/plugins/OMDb.dll has version 1.0.22.0
2025-06-03 14:23:21.704 Info App: File /system/plugins/OMDb.dll has version 1.0.21.0
2025-06-03 14:23:21.710 Info App: File /config/plugins/EmbyGuideData.dll has version 1.0.18.0
2025-06-03 14:23:21.711 Info App: File /system/plugins/EmbyGuideData.dll has version 1.0.18.0
2025-06-03 14:23:21.716 Info App: File /config/plugins/DvdMounter.dll has version 1.0.0.0
2025-06-03 14:23:21.716 Info App: File /system/plugins/DvdMounter.dll has version 1.0.0.0
2025-06-03 14:23:21.722 Info App: File /config/plugins/Fanart.dll has version 1.0.16.0
2025-06-03 14:23:21.723 Info App: File /system/plugins/Fanart.dll has version 1.0.16.0
2025-06-03 14:23:21.729 Info App: File /config/plugins/Emby.M3UTuner.dll has version 1.0.39.0
2025-06-03 14:23:21.730 Info App: File /system/plugins/Emby.M3UTuner.dll has version 1.0.39.0
2025-06-03 14:23:21.735 Info App: File /config/plugins/Emby.Webhooks.dll has version 1.0.35.0
2025-06-03 14:23:21.736 Info App: File /system/plugins/Emby.Webhooks.dll has version 1.0.35.0
2025-06-03 14:23:21.742 Info App: File /config/plugins/Emby.PortMapper.dll has version 1.2.8.0
2025-06-03 14:23:21.743 Info App: File /system/plugins/Emby.PortMapper.dll has version 1.2.8.0
2025-06-03 14:23:21.749 Info App: File /config/plugins/MusicBrainz.dll has version 1.0.25.0
2025-06-03 14:23:21.750 Info App: File /system/plugins/MusicBrainz.dll has version 1.0.24.0
2025-06-03 14:23:21.755 Info App: File /config/plugins/Emby.XmlTV.dll has version 1.2.0.0
2025-06-03 14:23:21.756 Info App: File /system/plugins/Emby.XmlTV.dll has version 1.2.0.0
2025-06-03 14:23:21.762 Info App: File /config/plugins/BlurayMounter.dll has version 1.0.2.0
2025-06-03 14:23:21.763 Info App: File /system/plugins/BlurayMounter.dll has version 1.0.2.0
2025-06-03 14:23:21.769 Info App: File /config/plugins/MBBackup.dll has version 1.7.8.0
2025-06-03 14:23:21.770 Info App: File /system/plugins/MBBackup.dll has version 1.7.2.0
2025-06-03 14:23:21.778 Info App: File /config/plugins/StrmAssistant.dll has version 2.0.0.24
2025-06-03 14:23:21.784 Info App: File /system/plugins/StrmAssistant.dll has version 2.0.0.18
2025-06-03 14:23:21.893 Info App: Loading AudioDb, Version=********, Culture=neutral, PublicKeyToken=null from /config/plugins/AudioDb.dll
2025-06-03 14:23:21.893 Info App: Loading BlurayMounter, Version=1.0.2.0, Culture=neutral, PublicKeyToken=null from /config/plugins/BlurayMounter.dll
2025-06-03 14:23:21.893 Info App: Loading DvdMounter, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null from /config/plugins/DvdMounter.dll
2025-06-03 14:23:21.893 Info App: Loading Emby.Dlna, Version=1.5.0.0, Culture=neutral, PublicKeyToken=null from /config/plugins/Emby.Dlna.dll
2025-06-03 14:23:21.893 Info App: Loading Emby.M3UTuner, Version=1.0.39.0, Culture=neutral, PublicKeyToken=null from /config/plugins/Emby.M3UTuner.dll
2025-06-03 14:23:21.893 Info App: Loading Emby.PortMapper, Version=1.2.8.0, Culture=neutral, PublicKeyToken=null from /config/plugins/Emby.PortMapper.dll
2025-06-03 14:23:21.893 Info App: Loading Emby.Server.CinemaMode, Version=1.0.47.0, Culture=neutral, PublicKeyToken=null from /config/plugins/Emby.Server.CinemaMode.dll
2025-06-03 14:23:21.893 Info App: Loading Emby.Webhooks, Version=1.0.35.0, Culture=neutral, PublicKeyToken=null from /config/plugins/Emby.Webhooks.dll
2025-06-03 14:23:21.893 Info App: Loading Emby.XmlTV, Version=1.2.0.0, Culture=neutral, PublicKeyToken=null from /config/plugins/Emby.XmlTV.dll
2025-06-03 14:23:21.893 Info App: Loading EmbyGuideData, Version=1.0.18.0, Culture=neutral, PublicKeyToken=null from /config/plugins/EmbyGuideData.dll
2025-06-03 14:23:21.893 Info App: Loading Fanart, Version=1.0.16.0, Culture=neutral, PublicKeyToken=null from /config/plugins/Fanart.dll
2025-06-03 14:23:21.893 Info App: Loading MBBackup, Version=1.7.8.0, Culture=neutral, PublicKeyToken=null from /config/plugins/MBBackup.dll
2025-06-03 14:23:21.893 Info App: Loading MovieDb, Version=1.8.3.0, Culture=neutral, PublicKeyToken=null from /config/plugins/MovieDb.dll
2025-06-03 14:23:21.893 Info App: Loading MusicBrainz, Version=1.0.25.0, Culture=neutral, PublicKeyToken=null from /config/plugins/MusicBrainz.dll
2025-06-03 14:23:21.893 Info App: Loading NfoMetadata, Version=1.0.83.0, Culture=neutral, PublicKeyToken=null from /config/plugins/NfoMetadata.dll
2025-06-03 14:23:21.893 Info App: Loading OMDb, Version=1.0.22.0, Culture=neutral, PublicKeyToken=null from /config/plugins/OMDb.dll
2025-06-03 14:23:21.893 Info App: Loading OpenSubtitles, Version=1.0.64.0, Culture=neutral, PublicKeyToken=null from /config/plugins/OpenSubtitles.dll
2025-06-03 14:23:21.893 Info App: Loading StrmAssistant, Version=2.0.0.24, Culture=neutral, PublicKeyToken=null from /config/plugins/StrmAssistant.dll
2025-06-03 14:23:21.893 Info App: Loading StudioImages, Version=1.0.3.0, Culture=neutral, PublicKeyToken=null from /config/plugins/StudioImages.dll
2025-06-03 14:23:21.893 Info App: Loading Tvdb, Version=1.6.2.0, Culture=neutral, PublicKeyToken=null from /config/plugins/Tvdb.dll
2025-06-03 14:23:21.893 Info App: Loading Emby.Api, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-03 14:23:21.893 Info App: Loading Emby.Web, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-03 14:23:21.893 Info App: Loading MediaBrowser.Model, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-03 14:23:21.893 Info App: Loading MediaBrowser.Common, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-03 14:23:21.893 Info App: Loading MediaBrowser.Controller, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-03 14:23:21.893 Info App: Loading Emby.Providers, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-03 14:23:21.893 Info App: Loading Emby.Photos, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-03 14:23:21.893 Info App: Loading Emby.Server.Implementations, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-03 14:23:21.893 Info App: Loading Emby.LiveTV, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-03 14:23:21.893 Info App: Loading Emby.ActivityLog, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-03 14:23:21.893 Info App: Loading Emby.Server.MediaEncoding, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-03 14:23:21.893 Info App: Loading Emby.LocalMetadata, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-03 14:23:21.893 Info App: Loading Emby.Notifications, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-03 14:23:21.893 Info App: Loading Emby.Web.GenericUI, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-03 14:23:21.893 Info App: Loading Emby.Codecs.Dxva, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-03 14:23:21.894 Info App: Loading Emby.Codecs, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-03 14:23:21.894 Info App: Loading Emby.Server.Connect, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-03 14:23:21.894 Info App: Loading Emby.Server.Sync, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-03 14:23:21.894 Info App: Loading EmbyServer, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-03 14:23:22.181 Info SqliteUserRepository: Sqlite version: 3.42.0
2025-06-03 14:23:22.182 Info SqliteUserRepository: Sqlite compiler options: ATOMIC_INTRINSICS=1,COMPILER=gcc-10.3.0,DEFAULT_AUTOVACUUM,DEFAULT_CACHE_SIZE=-2000,DEFAULT_FILE_FORMAT=4,DEFAULT_JOURNAL_SIZE_LIMIT=-1,DEFAULT_MMAP_SIZE=0,DEFAULT_PAGE_SIZE=4096,DEFAULT_PCACHE_INITSZ=20,DEFAULT_RECURSIVE_TRIGGERS,DEFAULT_SECTOR_SIZE=4096,DEFAULT_SYNCHRONOUS=2,DEFAULT_WAL_AUTOCHECKPOINT=1000,DEFAULT_WAL_SYNCHRONOUS=2,DEFAULT_WORKER_THREADS=0,ENABLE_COLUMN_METADATA,ENABLE_DBSTAT_VTAB,ENABLE_FTS3,ENABLE_FTS3_PARENTHESIS,ENABLE_FTS3_TOKENIZER,ENABLE_FTS4,ENABLE_FTS5,ENABLE_GEOPOLY,ENABLE_MATH_FUNCTIONS,ENABLE_PREUPDATE_HOOK,ENABLE_RTREE,ENABLE_SESSION,ENABLE_UNLOCK_NOTIFY,ENABLE_UPDATE_DELETE_LIMIT,LIKE_DOESNT_MATCH_BLOBS,MALLOC_SOFT_LIMIT=1024,MAX_ATTACHED=10,MAX_COLUMN=2000,MAX_COMPOUND_SELECT=500,MAX_DEFAULT_PAGE_SIZE=8192,MAX_EXPR_DEPTH=1000,MAX_FUNCTION_ARG=127,MAX_LENGTH=1000000000,MAX_LIKE_PATTERN_LENGTH=50000,MAX_MMAP_SIZE=0x7fff0000,MAX_PAGE_COUNT=1073741823,MAX_PAGE_SIZE=65536,MAX_SCHEMA_RETRY=25,MAX_SQL_LENGTH=1000000000,MAX_TRIGGER_DEPTH=1000,MAX_VARIABLE_NUMBER=250000,MAX_VDBE_OP=250000000,MAX_WORKER_THREADS=8,MUTEX_PTHREADS,OMIT_LOOKASIDE,SECURE_DELETE,SYSTEM_MALLOC,TEMP_STORE=1,THREADSAFE=1
2025-06-03 14:23:22.182 Info SqliteUserRepository: Opening sqlite connection to /config/data/users.db
2025-06-03 14:23:22.205 Info SqliteUserRepository: Default journal_mode for /config/data/users.db is wal
2025-06-03 14:23:22.207 Info SqliteUserRepository: PRAGMA foreign_keys=1
2025-06-03 14:23:22.207 Info SqliteUserRepository: Result of setting SQLITE_DBCONFIG_DQS_DDL to 0 is 0
2025-06-03 14:23:22.207 Info SqliteUserRepository: Result of setting SQLITE_DBCONFIG_DQS_DML to 0 is 0
2025-06-03 14:23:22.255 Info ActivityRepository: Opening sqlite connection to /config/data/activitylog.db
2025-06-03 14:23:22.269 Info ActivityRepository: Default journal_mode for /config/data/activitylog.db is wal
2025-06-03 14:23:22.270 Info ActivityRepository: PRAGMA foreign_keys=1
2025-06-03 14:23:22.270 Info ActivityRepository: Result of setting SQLITE_DBCONFIG_DQS_DDL to 0 is 0
2025-06-03 14:23:22.270 Info ActivityRepository: Result of setting SQLITE_DBCONFIG_DQS_DML to 0 is 0
2025-06-03 14:23:22.283 Info NetworkManager: Detecting local network addresses
2025-06-03 14:23:22.283 Info NetworkManager: networkInterface: Ethernet eth0, Speed: 10000000000, Description: eth0
2025-06-03 14:23:22.283 Info NetworkManager: GatewayAddresses: **********
2025-06-03 14:23:22.284 Info NetworkManager: UnicastAddresses: **********
2025-06-03 14:23:22.284 Info NetworkManager: networkInterface: Loopback lo, Speed: -1, Description: lo
2025-06-03 14:23:22.284 Info NetworkManager: GatewayAddresses: 
2025-06-03 14:23:22.284 Info NetworkManager: UnicastAddresses: 127.0.0.1,::1
2025-06-03 14:23:22.314 Info NetworkManager: Detected local ip addresses: [{"IPAddress":"**********","HasGateWayAddress":true,"PrefixLength":16,"IPv4Mask":"***********"},{"IPAddress":"127.0.0.1","HasGateWayAddress":false,"PrefixLength":8,"IPv4Mask":"*********"},{"IPAddress":"::1","HasGateWayAddress":false,"PrefixLength":128}]
2025-06-03 14:23:22.316 Info SqliteDisplayPreferencesRepository: Opening sqlite connection to /config/data/displaypreferences.db
2025-06-03 14:23:22.328 Info SqliteDisplayPreferencesRepository: Default journal_mode for /config/data/displaypreferences.db is wal
2025-06-03 14:23:22.328 Info SqliteDisplayPreferencesRepository: PRAGMA foreign_keys=1
2025-06-03 14:23:22.328 Info SqliteDisplayPreferencesRepository: Result of setting SQLITE_DBCONFIG_DQS_DDL to 0 is 0
2025-06-03 14:23:22.328 Info SqliteDisplayPreferencesRepository: Result of setting SQLITE_DBCONFIG_DQS_DML to 0 is 0
2025-06-03 14:23:22.332 Info ServerConfigurationManager: Saving system configuration
2025-06-03 14:23:22.336 Info App: Begin vacumming SqliteItemRepository
2025-06-03 14:23:22.336 Info SqliteItemRepository: Opening sqlite connection to /config/data/library.db
2025-06-03 14:23:22.385 Info SqliteItemRepository: Default journal_mode for /config/data/library.db is wal
2025-06-03 14:23:22.386 Info SqliteItemRepository: PRAGMA cache_size=-131072
2025-06-03 14:23:22.386 Info SqliteItemRepository: PRAGMA page_size=4096
2025-06-03 14:23:22.386 Info SqliteItemRepository: PRAGMA foreign_keys=1
2025-06-03 14:23:22.386 Info SqliteItemRepository: Result of setting SQLITE_DBCONFIG_DQS_DDL to 0 is 0
2025-06-03 14:23:22.386 Info SqliteItemRepository: Result of setting SQLITE_DBCONFIG_DQS_DML to 0 is 0
2025-06-03 14:23:22.422 Info App: Completed vacumming SqliteItemRepository after 87ms
2025-06-03 14:23:22.422 Info App: Begin vacumming SqliteUserRepository
2025-06-03 14:23:22.426 Info App: Completed vacumming SqliteUserRepository after 3ms
2025-06-03 14:23:22.426 Info App: Begin vacumming AuthenticationRepository
2025-06-03 14:23:22.426 Info AuthenticationRepository: Opening sqlite connection to /config/data/authentication.db
2025-06-03 14:23:22.442 Info AuthenticationRepository: Default journal_mode for /config/data/authentication.db is wal
2025-06-03 14:23:22.442 Info AuthenticationRepository: PRAGMA foreign_keys=1
2025-06-03 14:23:22.442 Info AuthenticationRepository: Result of setting SQLITE_DBCONFIG_DQS_DDL to 0 is 0
2025-06-03 14:23:22.442 Info AuthenticationRepository: Result of setting SQLITE_DBCONFIG_DQS_DML to 0 is 0
2025-06-03 14:23:22.447 Info App: Completed vacumming AuthenticationRepository after 22ms
2025-06-03 14:23:22.447 Info App: Begin vacumming SqliteDisplayPreferencesRepository
2025-06-03 14:23:22.449 Info App: Completed vacumming SqliteDisplayPreferencesRepository after 2ms
2025-06-03 14:23:22.449 Info App: Begin vacumming ActivityRepository
2025-06-03 14:23:22.451 Info App: Completed vacumming ActivityRepository after 2ms
2025-06-03 14:23:22.453 Info App: Adding HttpListener prefix http://+:8096/
2025-06-03 14:23:22.549 Info SqliteItemRepository: Init Complete
2025-06-03 14:23:22.726 Info App: Emby
	Command line: /system/EmbyServer.dll -programdata /config -ffdetect /bin/ffdetect -ffmpeg /bin/ffmpeg -ffprobe /bin/ffprobe -restartexitcode 3
	Operating system: Linux version ********-microsoft-standard-WSL2 (root@af282157c79e) (gcc (GCC) 11.2.0, GNU ld (GNU Binutils) 2.37) #1 SMP PREEMPT_DYNAMIC Mon Apr 21 17
	Framework: .NET 6.0.36
	OS/Process: x64/x64
	Runtime: system/System.Private.CoreLib.dll
	Processor count: 12
	Data path: /config
	Application path: /system
2025-06-03 14:23:22.726 Info App: Logs path: /config/logs
2025-06-03 14:23:22.726 Info App: Cache path: /config/cache
2025-06-03 14:23:22.726 Info App: Internal metadata path: /config/metadata
2025-06-03 14:23:22.727 Info App: Transcoding temporary files path: /config/transcoding-temp
2025-06-03 14:23:22.740 Info Strm Assistant: Plugin is getting loaded.
2025-06-03 14:23:24.051 Info FfmpegManager: FFMpeg: /bin/ffmpeg
2025-06-03 14:23:24.051 Info FfmpegManager: FFProbe: /bin/ffprobe
2025-06-03 14:23:24.051 Info FfmpegManager: FFDetect: /bin/ffdetect
2025-06-03 14:23:24.069 Info Skia: SkiaSharp version: ********
2025-06-03 14:23:24.070 Info ImageProcessor: Adding image processor Skia
2025-06-03 14:23:24.139 Info libvips: NetVips version: *******
2025-06-03 14:23:24.139 Info ImageProcessor: Adding image processor libvips
2025-06-03 14:23:24.180 Info TaskManager: Daily trigger for Emby Server Backup set to fire at 06/04/2025 00:10:00, which is 586.5969930666666 minutes from now.
2025-06-03 14:23:24.213 Info TaskManager: Daily trigger for Video preview thumbnail extraction set to fire at 06/04/2025 02:00:00, which is 696.596446295 minutes from now.
2025-06-03 14:23:24.234 Info TaskManager: Daily trigger for Rotate log file set to fire at 06/04/2025 00:00:00, which is 576.5960851233333 minutes from now.
2025-06-03 14:23:24.253 Info TaskManager: Queueing task HardwareDetectionScheduledTask
2025-06-03 14:23:24.255 Info TaskManager: Executing Hardware Detection
2025-06-03 14:23:24.267 Info FfmpegManager: ProcessRun 'ffmpeg -hide_banner -version' Execute: /bin/ffmpeg -hide_banner -version
2025-06-03 14:23:24.274 Info App: ServerId: abe2b0ac784c4ed894756b1f12c63150
2025-06-03 14:23:24.301 Info App: Starting entry point Emby.Dlna.Main.DlnaEntryPoint
2025-06-03 14:23:24.325 Info FfmpegManager: ProcessRun 'ffmpeg -hide_banner -version' Process exited with code 0 - Succeeded
2025-06-03 14:23:24.343 Info FfmpegManager: ProcessRun 'ffmpeg -hide_banner -decoders' Execute: /bin/ffmpeg -hide_banner -decoders
2025-06-03 14:23:24.361 Info App: Entry point completed: Emby.Dlna.Main.DlnaEntryPoint. Duration: 0.0598597 seconds
2025-06-03 14:23:24.361 Info App: Starting entry point Emby.Server.Implementations.Networking.RemoteAddressEntryPoint
2025-06-03 14:23:24.362 Info App: Loading data from /config/data/wan.dat
2025-06-03 14:23:24.374 Info App: Entry point completed: Emby.Server.Implementations.Networking.RemoteAddressEntryPoint. Duration: 0.0135283 seconds
2025-06-03 14:23:24.374 Info App: Starting entry point Emby.Server.Connect.ConnectEntryPoint
2025-06-03 14:23:24.376 Info App: Loading data from /config/data/connect.txt
2025-06-03 14:23:24.378 Info App: Entry point completed: Emby.Server.Connect.ConnectEntryPoint. Duration: 0.0032049 seconds
2025-06-03 14:23:24.378 Info App: Core startup complete
2025-06-03 14:23:24.379 Info App: Starting entry point Emby.PortMapper.ExternalPortForwarding
2025-06-03 14:23:24.381 Info App: Entry point completed: Emby.PortMapper.ExternalPortForwarding. Duration: 0.001362 seconds
2025-06-03 14:23:24.381 Info App: Starting entry point Emby.Security.PluginSecurityManager
2025-06-03 14:23:24.381 Info App: Entry point completed: Emby.Security.PluginSecurityManager. Duration: 4.17E-05 seconds
2025-06-03 14:23:24.381 Info App: Starting entry point Emby.Server.CinemaMode.IntrosEntryPoint
2025-06-03 14:23:24.381 Info App: Entry point completed: Emby.Server.CinemaMode.IntrosEntryPoint. Duration: 9.02E-05 seconds
2025-06-03 14:23:24.381 Info App: Starting entry point Emby.Webhooks.MigrationEntryPoint
2025-06-03 14:23:24.383 Info App: Entry point completed: Emby.Webhooks.MigrationEntryPoint. Duration: 0.0022119 seconds
2025-06-03 14:23:24.383 Info App: Starting entry point MBBackup.ServerEntryPoint
2025-06-03 14:23:24.383 Info App: Entry point completed: MBBackup.ServerEntryPoint. Duration: 4.65E-05 seconds
2025-06-03 14:23:24.383 Info App: Starting entry point MovieDb.Security.PluginStartup
2025-06-03 14:23:24.386 Info App: Entry point completed: MovieDb.Security.PluginStartup. Duration: 0.0026927 seconds
2025-06-03 14:23:24.386 Info App: Starting entry point NfoMetadata.EntryPoint
2025-06-03 14:23:24.386 Info App: Entry point completed: NfoMetadata.EntryPoint. Duration: 0.0001293 seconds
2025-06-03 14:23:24.386 Info App: Starting entry point Tvdb.EntryPoint
2025-06-03 14:23:24.387 Info App: Entry point completed: Tvdb.EntryPoint. Duration: 4.53E-05 seconds
2025-06-03 14:23:24.387 Info App: Starting entry point Emby.Server.Implementations.Udp.UdpServerEntryPoint
2025-06-03 14:23:24.388 Info App: Entry point completed: Emby.Server.Implementations.Udp.UdpServerEntryPoint. Duration: 0.0011488 seconds
2025-06-03 14:23:24.388 Info App: Starting entry point Emby.Server.Implementations.Playlists.PlaylistUpgradeEntryPoint
2025-06-03 14:23:24.392 Info FfmpegManager: ProcessRun 'ffmpeg -hide_banner -decoders' Process exited with code 0 - Succeeded
2025-06-03 14:23:24.393 Info App: Entry point completed: Emby.Server.Implementations.Playlists.PlaylistUpgradeEntryPoint. Duration: 0.0051011 seconds
2025-06-03 14:23:24.393 Info App: Starting entry point Emby.Server.Implementations.Library.DeviceAccessEntryPoint
2025-06-03 14:23:24.394 Info App: Entry point completed: Emby.Server.Implementations.Library.DeviceAccessEntryPoint. Duration: 0.0004603 seconds
2025-06-03 14:23:24.394 Info App: Starting entry point Emby.Server.Implementations.IO.LibraryMonitorStartup
2025-06-03 14:23:24.404 Info FfmpegManager: ProcessRun 'ffmpeg -hide_banner -encoders' Execute: /bin/ffmpeg -hide_banner -encoders
2025-06-03 14:23:24.415 Info App: Entry point completed: Emby.Server.Implementations.IO.LibraryMonitorStartup. Duration: 0.0209225 seconds
2025-06-03 14:23:24.415 Info App: Starting entry point Emby.Server.Implementations.EntryPoints.AutomaticRestartEntryPoint
2025-06-03 14:23:24.416 Info App: Entry point completed: Emby.Server.Implementations.EntryPoints.AutomaticRestartEntryPoint. Duration: 0.0004906 seconds
2025-06-03 14:23:24.416 Info App: Starting entry point Emby.Server.Implementations.EntryPoints.KeepServerAwake
2025-06-03 14:23:24.416 Info App: Entry point completed: Emby.Server.Implementations.EntryPoints.KeepServerAwake. Duration: 0.0001099 seconds
2025-06-03 14:23:24.416 Info App: Starting entry point Emby.Server.Implementations.EntryPoints.LibraryChangedNotifier
2025-06-03 14:23:24.417 Info App: Entry point completed: Emby.Server.Implementations.EntryPoints.LibraryChangedNotifier. Duration: 0.0006541 seconds
2025-06-03 14:23:24.417 Info App: Starting entry point Emby.Server.Implementations.EntryPoints.ServerEventNotifier
2025-06-03 14:23:24.418 Info App: Entry point completed: Emby.Server.Implementations.EntryPoints.ServerEventNotifier. Duration: 0.0016824 seconds
2025-06-03 14:23:24.418 Info App: Starting entry point Emby.Server.Implementations.EntryPoints.StartupWizard
2025-06-03 14:23:24.419 Info App: Entry point completed: Emby.Server.Implementations.EntryPoints.StartupWizard. Duration: 0.0002395 seconds
2025-06-03 14:23:24.419 Info App: Starting entry point Emby.Server.Implementations.EntryPoints.SystemEvents
2025-06-03 14:23:24.419 Info App: Entry point completed: Emby.Server.Implementations.EntryPoints.SystemEvents. Duration: 0.0002719 seconds
2025-06-03 14:23:24.419 Info App: Starting entry point Emby.Server.Implementations.EntryPoints.UserDataChangeNotifier
2025-06-03 14:23:24.419 Info App: Entry point completed: Emby.Server.Implementations.EntryPoints.UserDataChangeNotifier. Duration: 8.02E-05 seconds
2025-06-03 14:23:24.419 Info App: Starting entry point Emby.Server.Implementations.Channels.ChannelsEntryPoint
2025-06-03 14:23:24.444 Info App: Entry point completed: Emby.Server.Implementations.Channels.ChannelsEntryPoint. Duration: 0.0251499 seconds
2025-06-03 14:23:24.445 Info App: Starting entry point Emby.LiveTV.EntryPoint
2025-06-03 14:23:24.446 Info LiveTV: Loading live tv data from /config/data/livetv/timers
2025-06-03 14:23:24.448 Info App: Entry point completed: Emby.LiveTV.EntryPoint. Duration: 0.0030492 seconds
2025-06-03 14:23:24.448 Info App: Starting entry point Emby.LiveTV.RecordingNotifier
2025-06-03 14:23:24.448 Info LibraryMonitor: Watching directory with new FileSystemWatcher for /media/动画电影 for item 13
2025-06-03 14:23:24.450 Info App: Entry point completed: Emby.LiveTV.RecordingNotifier. Duration: 0.0018478 seconds
2025-06-03 14:23:24.450 Info App: Starting entry point Emby.ActivityLog.ActivityLogEntryPoint
2025-06-03 14:23:24.452 Info App: Entry point completed: Emby.ActivityLog.ActivityLogEntryPoint. Duration: 0.0023232 seconds
2025-06-03 14:23:24.452 Info App: Starting entry point Emby.Server.MediaEncoding.Api.EncodingManagerEntryPoint
2025-06-03 14:23:24.453 Info App: Entry point completed: Emby.Server.MediaEncoding.Api.EncodingManagerEntryPoint. Duration: 0.0009861 seconds
2025-06-03 14:23:24.453 Info App: Starting entry point Emby.Notifications.NotificationManagerEntryPoint
2025-06-03 14:23:24.453 Info LibraryMonitor: Watching directory with new FileSystemWatcher for /media/国产剧 for item 7
2025-06-03 14:23:24.457 Info FfmpegManager: ProcessRun 'ffmpeg -hide_banner -encoders' Process exited with code 0 - Succeeded
2025-06-03 14:23:24.459 Info LibraryMonitor: Watching directory with new FileSystemWatcher for /media/国漫 for item 15
2025-06-03 14:23:24.464 Info LibraryMonitor: Watching directory with new FileSystemWatcher for /media/华语电影 for item 5
2025-06-03 14:23:24.465 Info Notifications: Registering event nofitier Emby Server User Notifications
2025-06-03 14:23:24.465 Info FfmpegManager: ProcessRun 'ffmpeg -hide_banner -hwaccels' Execute: /bin/ffmpeg -hide_banner -hwaccels
2025-06-03 14:23:24.468 Info LibraryMonitor: Watching directory with new FileSystemWatcher for /media/欧美剧 for item 11
2025-06-03 14:23:24.469 Info App: Entry point completed: Emby.Notifications.NotificationManagerEntryPoint. Duration: 0.0154169 seconds
2025-06-03 14:23:24.469 Info App: Starting entry point Emby.Server.Sync.SyncNotificationEntryPoint
2025-06-03 14:23:24.470 Info App: Entry point completed: Emby.Server.Sync.SyncNotificationEntryPoint. Duration: 0.0014234 seconds
2025-06-03 14:23:24.470 Info App: Starting entry point EmbyServer.Windows.LoopUtilEntryPoint
2025-06-03 14:23:24.470 Info App: Entry point completed: EmbyServer.Windows.LoopUtilEntryPoint. Duration: 0.0001132 seconds
2025-06-03 14:23:24.470 Info App: All entry points have started
2025-06-03 14:23:24.473 Info LibraryMonitor: Watching directory with new FileSystemWatcher for /media/外语电影 for item 9
2025-06-03 14:23:24.477 Info FfmpegManager: ProcessRun 'ffmpeg -hide_banner -hwaccels' Process exited with code 0 - Succeeded
2025-06-03 14:23:24.483 Info FfmpegManager: ProcessRun 'ffmpeg -hide_banner -protocols' Execute: /bin/ffmpeg -hide_banner -protocols
2025-06-03 14:23:24.495 Info FfmpegManager: ProcessRun 'ffmpeg -hide_banner -protocols' Process exited with code 0 - Succeeded
2025-06-03 14:23:24.501 Info FfmpegManager: ProcessRun 'ffmpeg -hide_banner -filters' Execute: /bin/ffmpeg -hide_banner -filters
2025-06-03 14:23:24.513 Info FfmpegManager: ProcessRun 'ffmpeg -hide_banner -filters' Process exited with code 0 - Succeeded
2025-06-03 14:23:24.513 Info App: Init BeginReceive on 0.0.0.0
2025-06-03 14:23:24.513 Info App: Init BeginReceive on 0.0.0.0
2025-06-03 14:23:24.513 Info App: Init BeginReceive on **********
2025-06-03 14:23:24.513 Info App: Init BeginReceive on 127.0.0.1
2025-06-03 14:23:24.521 Info FfmpegManager: FfmpegValidator.Validate complete
2025-06-03 14:23:24.581 Info SoftwareCodecProvider: h264, libx264, x264, V-E-libx264
2025-06-03 14:23:24.581 Info SoftwareCodecProvider: hevc, libx265, x265, V-E-libx265
2025-06-03 14:23:24.581 Info SoftwareCodecProvider: mpeg4, mpeg4, MPEG-4 part 2, V-E-mpeg4
2025-06-03 14:23:24.581 Info SoftwareCodecProvider: msmpeg4v3, msmpeg4, MPEG-4 part 2 (MS Variant 3), V-E-msmpeg4
2025-06-03 14:23:24.582 Info SoftwareCodecProvider: vp8, libvpx, libvpx VP8, V-E-libvpx
2025-06-03 14:23:24.583 Info SoftwareCodecProvider: h264, libx264, x264, V-E-libx264
2025-06-03 14:23:24.583 Info SoftwareCodecProvider: hevc, libx265, x265, V-E-libx265
2025-06-03 14:23:24.583 Info SoftwareCodecProvider: mpeg4, mpeg4, MPEG-4 part 2, V-E-mpeg4
2025-06-03 14:23:24.583 Info SoftwareCodecProvider: msmpeg4v3, msmpeg4, MPEG-4 part 2 (MS Variant 3), V-E-msmpeg4
2025-06-03 14:23:24.583 Info SoftwareCodecProvider: vp8, libvpx, libvpx VP8, V-E-libvpx
2025-06-03 14:23:24.593 Info VaapiCodecProvider: ProcessRun 'ffdetect_vaencdec' Execute: /bin/ffdetect -hide_banner -show_program_version -loglevel 48 -show_error -show_log 40 vaencdec -print_format json 
2025-06-03 14:23:24.609 Info VaapiCodecProvider: ProcessRun 'ffdetect_vaencdec' Process exited with code 0 - Succeeded
2025-06-03 14:23:24.680 Info QuickSyncCodecProvider: ProcessRun 'ffdetect_qsvencdec' Execute: /bin/ffdetect -hide_banner -show_program_version -loglevel 48 -show_error -show_log 40 qsvencdec -print_format json 
2025-06-03 14:23:24.695 Info QuickSyncCodecProvider: ProcessRun 'ffdetect_qsvencdec' Process exited with code 0 - Succeeded
2025-06-03 14:23:24.751 Info NvidiaCodecProvider: ProcessRun 'ffdetect_nvencdec' Execute: /bin/ffdetect -hide_banner -show_program_version -loglevel 48 -show_error -show_log 40 nvencdec -print_format json 
2025-06-03 14:23:24.756 Info NvidiaCodecProvider: ProcessRun 'ffdetect_nvencdec' Process exited with code 1 - Failed
2025-06-03 14:23:24.943 Info CodecManager: CodecList:
2025-06-03 14:23:24.944 Info TaskManager: Hardware Detection Completed after 0 minute(s) and 0 seconds
2025-06-03 14:23:27.221 Info TaskManager: Queueing task PluginUpdateTask
2025-06-03 14:23:27.221 Info TaskManager: Executing Check for plugin updates
2025-06-03 14:23:27.225 Info TaskManager: Queueing task SystemUpdateTask
2025-06-03 14:23:27.225 Info TaskManager: Executing Check for application updates
2025-06-03 14:23:27.226 Info HttpClient: GET https://www.mb3admin.com/admin/service/EmbyPackages.json
2025-06-03 14:23:27.249 Info TaskManager: Queueing task SubtitleOcrDataTask
2025-06-03 14:23:27.249 Info TaskManager: Executing Download OCR Data
2025-06-03 14:23:27.270 Info TaskManager: Download OCR Data Completed after 0 minute(s) and 0 seconds
2025-06-03 14:23:27.357 Info App: No application update available.
2025-06-03 14:23:27.357 Info TaskManager: Check for application updates Completed after 0 minute(s) and 0 seconds
2025-06-03 14:23:29.330 Info TaskManager: Check for plugin updates Completed after 0 minute(s) and 2 seconds
2025-06-03 14:27:31.228 Info Server: http/1.1 POST http://‌‍‍localhost‌:8096/emby/Sessions/Capabilities/Full?X-Emby-Client=Emby Web&X-Emby-Device-Name=Microsoft Edge Windows&X-Emby-Device-Id=93bfdabd-add0-49c6-8f3d-6f43294d70e2&X-Emby-Client-Version=********&X-Emby-Token=‌651a0c8b79b64c3d94765629e1318294‌&X-Emby-Language=zh-cn&reqformat=json. Source Ip: ‌‍‍**********‌, UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-06-03 14:27:31.239 Info Server: http/1.1 Response 204 to ‌‍‍**********‌. Time: 11ms. POST http://‌‍‍localhost‌:8096/emby/Sessions/Capabilities/Full?X-Emby-Client=Emby Web&X-Emby-Device-Name=Microsoft Edge Windows&X-Emby-Device-Id=93bfdabd-add0-49c6-8f3d-6f43294d70e2&X-Emby-Client-Version=********&X-Emby-Token=‌651a0c8b79b64c3d94765629e1318294‌&X-Emby-Language=zh-cn&reqformat=json
2025-06-03 14:27:31.309 Info Server: http/1.1 POST http://‌‍‍localhost‌:8096/?serverId=abe2b0ac784c4ed894756b1f12c63150&deviceId=93bfdabd-add0-49c6-8f3d-6f43294d70e2&deviceName=Microsoft Edge Windows&appName=Emby Web&appVersion=********&viewOnly=true. Source Ip: ‌‍‍**********‌, UserAgent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********
2025-06-03 14:27:31.309 Info Server: http/1.1 Response 302 to ‌‍‍**********‌. Time: 0ms. POST http://‌‍‍localhost‌:8096/?serverId=abe2b0ac784c4ed894756b1f12c63150&deviceId=93bfdabd-add0-49c6-8f3d-6f43294d70e2&deviceName=Microsoft Edge Windows&appName=Emby Web&appVersion=********&viewOnly=true
2025-06-03 14:27:31.594 Info App: Sqlite: 284 - automatic index on LastWatchedEpisodes(SeriesPresentationUniqueKey)
2025-06-03 14:27:43.818 Info Server: http/1.1 GET http://‌‍‍localhost‌:8096/emby/Auth/Keys?IncludeItemTypes=ApiKey&Fields=BasicSyncInfo,CanDelete,CanDownload,PrimaryImageAspectRatio,ProductionYear,Status,EndDate,CommunityRating,OfficialRating,CriticRating,DateCreated&StartIndex=0&EnableImageTypes=Primary,Backdrop,Thumb&ImageTypeLimit=1&Limit=30&X-Emby-Client=Emby Web&X-Emby-Device-Name=Microsoft Edge Windows&X-Emby-Device-Id=93bfdabd-add0-49c6-8f3d-6f43294d70e2&X-Emby-Client-Version=********&X-Emby-Token=‌651a0c8b79b64c3d94765629e1318294‌&X-Emby-Language=zh-cn. Source Ip: ‌‍‍**********‌, Accept=application/json, Connection=keep-alive, Host=‌‍‍localhost:8096‌, User-Agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********, Accept-Encoding=gzip, deflate, br, zstd, Accept-Language=zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6, Referer=‌‍‍http://localhost:8096/web/index.html‌, sec-ch-ua-platform="Windows", sec-ch-ua="Microsoft Edge";v="137", "Chromium";v="137", "Not/A)Brand";v="24", sec-ch-ua-mobile=?0, Sec-Fetch-Site=same-origin, Sec-Fetch-Mode=cors, Sec-Fetch-Dest=empty
2025-06-03 14:27:43.823 Info Server: http/1.1 Response 200 to ‌‍‍**********‌. Time: 5ms. GET http://‌‍‍localhost‌:8096/emby/Auth/Keys?IncludeItemTypes=ApiKey&Fields=BasicSyncInfo,CanDelete,CanDownload,PrimaryImageAspectRatio,ProductionYear,Status,EndDate,CommunityRating,OfficialRating,CriticRating,DateCreated&StartIndex=0&EnableImageTypes=Primary,Backdrop,Thumb&ImageTypeLimit=1&Limit=30&X-Emby-Client=Emby Web&X-Emby-Device-Name=Microsoft Edge Windows&X-Emby-Device-Id=93bfdabd-add0-49c6-8f3d-6f43294d70e2&X-Emby-Client-Version=********&X-Emby-Token=‌651a0c8b79b64c3d94765629e1318294‌&X-Emby-Language=zh-cn
