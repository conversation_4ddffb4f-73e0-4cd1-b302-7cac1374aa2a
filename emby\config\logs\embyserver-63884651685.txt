2025-06-04 15:23:34.309 Info Main: Application path: /system/EmbyServer.dll
2025-06-04 15:23:34.415 Info NetworkManager: Adding event handler for NetworkChange.NetworkAddressChanged
2025-06-04 15:23:34.626 Info App: Setting default culture to zh-CN
2025-06-04 15:23:34.633 Info Main: Emby
	Command line: /system/EmbyServer.dll -programdata /config -ffdetect /bin/ffdetect -ffmpeg /bin/ffmpeg -ffprobe /bin/ffprobe -restartexitcode 3
	Operating system: Linux version ********-microsoft-standard-WSL2 (root@af282157c79e) (gcc (GCC) 11.2.0, GNU ld (GNU Binutils) 2.37) #1 SMP PREEMPT_DYNAMIC Mon Apr 21 17
	Framework: .NET 6.0.36
	OS/Process: x64/x64
	Runtime: system/System.Private.CoreLib.dll
	Processor count: 12
	Data path: /config
	Application path: /system
2025-06-04 15:23:34.633 Info Main: Logs path: /config/logs
2025-06-04 15:23:34.633 Info Main: Cache path: /config/cache
2025-06-04 15:23:34.633 Info Main: Internal metadata path: /config/metadata
2025-06-04 15:23:34.634 Info App: Emby Server Version: ********
2025-06-04 15:23:34.637 Info App: Loading assemblies
2025-06-04 15:23:34.721 Info App: File /config/plugins/AudioDb.dll has version ********
2025-06-04 15:23:34.722 Info App: File /system/plugins/AudioDb.dll has version 1.0.18.0
2025-06-04 15:23:34.729 Info App: File /config/plugins/MovieDb.dll has version 1.8.3.0
2025-06-04 15:23:34.730 Info App: File /system/plugins/MovieDb.dll has version 1.8.0.0
2025-06-04 15:23:34.735 Info App: File /config/plugins/OpenSubtitles.dll has version 1.0.64.0
2025-06-04 15:23:34.736 Info App: File /system/plugins/OpenSubtitles.dll has version 1.0.63.0
2025-06-04 15:23:34.742 Info App: File /config/plugins/Emby.Server.CinemaMode.dll has version 1.0.47.0
2025-06-04 15:23:34.743 Info App: File /system/plugins/Emby.Server.CinemaMode.dll has version 1.0.47.0
2025-06-04 15:23:34.750 Info App: File /config/plugins/Emby.Dlna.dll has version 1.5.0.0
2025-06-04 15:23:34.752 Info App: File /system/plugins/Emby.Dlna.dll has version 1.4.7.0
2025-06-04 15:23:34.758 Info App: File /config/plugins/NfoMetadata.dll has version 1.0.83.0
2025-06-04 15:23:34.759 Info App: File /system/plugins/NfoMetadata.dll has version 1.0.82.0
2025-06-04 15:23:34.765 Info App: File /config/plugins/Tvdb.dll has version 1.6.2.0
2025-06-04 15:23:34.766 Info App: File /system/plugins/Tvdb.dll has version 1.5.8.0
2025-06-04 15:23:34.771 Info App: File /config/plugins/StudioImages.dll has version 1.0.3.0
2025-06-04 15:23:34.771 Info App: File /system/plugins/StudioImages.dll has version 1.0.3.0
2025-06-04 15:23:34.777 Info App: File /config/plugins/OMDb.dll has version 1.0.22.0
2025-06-04 15:23:34.778 Info App: File /system/plugins/OMDb.dll has version 1.0.21.0
2025-06-04 15:23:34.784 Info App: File /config/plugins/EmbyGuideData.dll has version 1.0.18.0
2025-06-04 15:23:34.786 Info App: File /system/plugins/EmbyGuideData.dll has version 1.0.18.0
2025-06-04 15:23:34.791 Info App: File /config/plugins/DvdMounter.dll has version 1.0.0.0
2025-06-04 15:23:34.792 Info App: File /system/plugins/DvdMounter.dll has version 1.0.0.0
2025-06-04 15:23:34.797 Info App: File /config/plugins/Fanart.dll has version 1.0.16.0
2025-06-04 15:23:34.798 Info App: File /system/plugins/Fanart.dll has version 1.0.16.0
2025-06-04 15:23:34.804 Info App: File /config/plugins/Emby.M3UTuner.dll has version 1.0.39.0
2025-06-04 15:23:34.805 Info App: File /system/plugins/Emby.M3UTuner.dll has version 1.0.39.0
2025-06-04 15:23:34.810 Info App: File /config/plugins/Emby.Webhooks.dll has version 1.0.35.0
2025-06-04 15:23:34.810 Info App: File /system/plugins/Emby.Webhooks.dll has version 1.0.35.0
2025-06-04 15:23:34.816 Info App: File /config/plugins/Emby.PortMapper.dll has version 1.2.8.0
2025-06-04 15:23:34.817 Info App: File /system/plugins/Emby.PortMapper.dll has version 1.2.8.0
2025-06-04 15:23:34.823 Info App: File /config/plugins/MusicBrainz.dll has version 1.0.25.0
2025-06-04 15:23:34.823 Info App: File /system/plugins/MusicBrainz.dll has version 1.0.24.0
2025-06-04 15:23:34.829 Info App: File /config/plugins/Emby.XmlTV.dll has version 1.2.0.0
2025-06-04 15:23:34.830 Info App: File /system/plugins/Emby.XmlTV.dll has version 1.2.0.0
2025-06-04 15:23:34.836 Info App: File /config/plugins/BlurayMounter.dll has version 1.0.2.0
2025-06-04 15:23:34.837 Info App: File /system/plugins/BlurayMounter.dll has version 1.0.2.0
2025-06-04 15:23:34.842 Info App: File /config/plugins/MBBackup.dll has version 1.7.8.0
2025-06-04 15:23:34.843 Info App: File /system/plugins/MBBackup.dll has version 1.7.2.0
2025-06-04 15:23:34.852 Info App: File /config/plugins/StrmAssistant.dll has version 2.0.0.24
2025-06-04 15:23:34.857 Info App: File /system/plugins/StrmAssistant.dll has version 2.0.0.18
2025-06-04 15:23:34.947 Info App: Loading AudioDb, Version=********, Culture=neutral, PublicKeyToken=null from /config/plugins/AudioDb.dll
2025-06-04 15:23:34.947 Info App: Loading BlurayMounter, Version=1.0.2.0, Culture=neutral, PublicKeyToken=null from /config/plugins/BlurayMounter.dll
2025-06-04 15:23:34.947 Info App: Loading DvdMounter, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null from /config/plugins/DvdMounter.dll
2025-06-04 15:23:34.947 Info App: Loading Emby.Dlna, Version=1.5.0.0, Culture=neutral, PublicKeyToken=null from /config/plugins/Emby.Dlna.dll
2025-06-04 15:23:34.947 Info App: Loading Emby.M3UTuner, Version=1.0.39.0, Culture=neutral, PublicKeyToken=null from /config/plugins/Emby.M3UTuner.dll
2025-06-04 15:23:34.947 Info App: Loading Emby.PortMapper, Version=1.2.8.0, Culture=neutral, PublicKeyToken=null from /config/plugins/Emby.PortMapper.dll
2025-06-04 15:23:34.947 Info App: Loading Emby.Server.CinemaMode, Version=1.0.47.0, Culture=neutral, PublicKeyToken=null from /config/plugins/Emby.Server.CinemaMode.dll
2025-06-04 15:23:34.947 Info App: Loading Emby.Webhooks, Version=1.0.35.0, Culture=neutral, PublicKeyToken=null from /config/plugins/Emby.Webhooks.dll
2025-06-04 15:23:34.947 Info App: Loading Emby.XmlTV, Version=1.2.0.0, Culture=neutral, PublicKeyToken=null from /config/plugins/Emby.XmlTV.dll
2025-06-04 15:23:34.948 Info App: Loading EmbyGuideData, Version=1.0.18.0, Culture=neutral, PublicKeyToken=null from /config/plugins/EmbyGuideData.dll
2025-06-04 15:23:34.948 Info App: Loading Fanart, Version=1.0.16.0, Culture=neutral, PublicKeyToken=null from /config/plugins/Fanart.dll
2025-06-04 15:23:34.948 Info App: Loading MBBackup, Version=1.7.8.0, Culture=neutral, PublicKeyToken=null from /config/plugins/MBBackup.dll
2025-06-04 15:23:34.948 Info App: Loading MovieDb, Version=1.8.3.0, Culture=neutral, PublicKeyToken=null from /config/plugins/MovieDb.dll
2025-06-04 15:23:34.948 Info App: Loading MusicBrainz, Version=1.0.25.0, Culture=neutral, PublicKeyToken=null from /config/plugins/MusicBrainz.dll
2025-06-04 15:23:34.948 Info App: Loading NfoMetadata, Version=1.0.83.0, Culture=neutral, PublicKeyToken=null from /config/plugins/NfoMetadata.dll
2025-06-04 15:23:34.948 Info App: Loading OMDb, Version=1.0.22.0, Culture=neutral, PublicKeyToken=null from /config/plugins/OMDb.dll
2025-06-04 15:23:34.948 Info App: Loading OpenSubtitles, Version=1.0.64.0, Culture=neutral, PublicKeyToken=null from /config/plugins/OpenSubtitles.dll
2025-06-04 15:23:34.948 Info App: Loading StrmAssistant, Version=2.0.0.24, Culture=neutral, PublicKeyToken=null from /config/plugins/StrmAssistant.dll
2025-06-04 15:23:34.948 Info App: Loading StudioImages, Version=1.0.3.0, Culture=neutral, PublicKeyToken=null from /config/plugins/StudioImages.dll
2025-06-04 15:23:34.948 Info App: Loading Tvdb, Version=1.6.2.0, Culture=neutral, PublicKeyToken=null from /config/plugins/Tvdb.dll
2025-06-04 15:23:34.948 Info App: Loading Emby.Api, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-04 15:23:34.948 Info App: Loading Emby.Web, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-04 15:23:34.948 Info App: Loading MediaBrowser.Model, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-04 15:23:34.948 Info App: Loading MediaBrowser.Common, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-04 15:23:34.948 Info App: Loading MediaBrowser.Controller, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-04 15:23:34.948 Info App: Loading Emby.Providers, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-04 15:23:34.948 Info App: Loading Emby.Photos, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-04 15:23:34.948 Info App: Loading Emby.Server.Implementations, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-04 15:23:34.948 Info App: Loading Emby.LiveTV, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-04 15:23:34.948 Info App: Loading Emby.ActivityLog, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-04 15:23:34.948 Info App: Loading Emby.Server.MediaEncoding, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-04 15:23:34.948 Info App: Loading Emby.LocalMetadata, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-04 15:23:34.948 Info App: Loading Emby.Notifications, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-04 15:23:34.948 Info App: Loading Emby.Web.GenericUI, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-04 15:23:34.948 Info App: Loading Emby.Codecs.Dxva, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-04 15:23:34.948 Info App: Loading Emby.Codecs, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-04 15:23:34.948 Info App: Loading Emby.Server.Connect, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-04 15:23:34.948 Info App: Loading Emby.Server.Sync, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-04 15:23:34.948 Info App: Loading EmbyServer, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-04 15:23:35.227 Info SqliteUserRepository: Sqlite version: 3.42.0
2025-06-04 15:23:35.228 Info SqliteUserRepository: Sqlite compiler options: ATOMIC_INTRINSICS=1,COMPILER=gcc-10.3.0,DEFAULT_AUTOVACUUM,DEFAULT_CACHE_SIZE=-2000,DEFAULT_FILE_FORMAT=4,DEFAULT_JOURNAL_SIZE_LIMIT=-1,DEFAULT_MMAP_SIZE=0,DEFAULT_PAGE_SIZE=4096,DEFAULT_PCACHE_INITSZ=20,DEFAULT_RECURSIVE_TRIGGERS,DEFAULT_SECTOR_SIZE=4096,DEFAULT_SYNCHRONOUS=2,DEFAULT_WAL_AUTOCHECKPOINT=1000,DEFAULT_WAL_SYNCHRONOUS=2,DEFAULT_WORKER_THREADS=0,ENABLE_COLUMN_METADATA,ENABLE_DBSTAT_VTAB,ENABLE_FTS3,ENABLE_FTS3_PARENTHESIS,ENABLE_FTS3_TOKENIZER,ENABLE_FTS4,ENABLE_FTS5,ENABLE_GEOPOLY,ENABLE_MATH_FUNCTIONS,ENABLE_PREUPDATE_HOOK,ENABLE_RTREE,ENABLE_SESSION,ENABLE_UNLOCK_NOTIFY,ENABLE_UPDATE_DELETE_LIMIT,LIKE_DOESNT_MATCH_BLOBS,MALLOC_SOFT_LIMIT=1024,MAX_ATTACHED=10,MAX_COLUMN=2000,MAX_COMPOUND_SELECT=500,MAX_DEFAULT_PAGE_SIZE=8192,MAX_EXPR_DEPTH=1000,MAX_FUNCTION_ARG=127,MAX_LENGTH=1000000000,MAX_LIKE_PATTERN_LENGTH=50000,MAX_MMAP_SIZE=0x7fff0000,MAX_PAGE_COUNT=1073741823,MAX_PAGE_SIZE=65536,MAX_SCHEMA_RETRY=25,MAX_SQL_LENGTH=1000000000,MAX_TRIGGER_DEPTH=1000,MAX_VARIABLE_NUMBER=250000,MAX_VDBE_OP=250000000,MAX_WORKER_THREADS=8,MUTEX_PTHREADS,OMIT_LOOKASIDE,SECURE_DELETE,SYSTEM_MALLOC,TEMP_STORE=1,THREADSAFE=1
2025-06-04 15:23:35.228 Info SqliteUserRepository: Opening sqlite connection to /config/data/users.db
2025-06-04 15:23:35.252 Info SqliteUserRepository: Default journal_mode for /config/data/users.db is wal
2025-06-04 15:23:35.254 Info SqliteUserRepository: PRAGMA foreign_keys=1
2025-06-04 15:23:35.254 Info SqliteUserRepository: Result of setting SQLITE_DBCONFIG_DQS_DDL to 0 is 0
2025-06-04 15:23:35.254 Info SqliteUserRepository: Result of setting SQLITE_DBCONFIG_DQS_DML to 0 is 0
2025-06-04 15:23:35.304 Info ActivityRepository: Opening sqlite connection to /config/data/activitylog.db
2025-06-04 15:23:35.319 Info ActivityRepository: Default journal_mode for /config/data/activitylog.db is wal
2025-06-04 15:23:35.319 Info ActivityRepository: PRAGMA foreign_keys=1
2025-06-04 15:23:35.319 Info ActivityRepository: Result of setting SQLITE_DBCONFIG_DQS_DDL to 0 is 0
2025-06-04 15:23:35.319 Info ActivityRepository: Result of setting SQLITE_DBCONFIG_DQS_DML to 0 is 0
2025-06-04 15:23:35.338 Info NetworkManager: Detecting local network addresses
2025-06-04 15:23:35.338 Info NetworkManager: networkInterface: Ethernet eth0, Speed: 10000000000, Description: eth0
2025-06-04 15:23:35.338 Info NetworkManager: GatewayAddresses: **********
2025-06-04 15:23:35.339 Info NetworkManager: UnicastAddresses: **********
2025-06-04 15:23:35.339 Info NetworkManager: networkInterface: Loopback lo, Speed: -1, Description: lo
2025-06-04 15:23:35.339 Info NetworkManager: GatewayAddresses: 
2025-06-04 15:23:35.339 Info NetworkManager: UnicastAddresses: 127.0.0.1,::1
2025-06-04 15:23:35.372 Info NetworkManager: Detected local ip addresses: [{"IPAddress":"**********","HasGateWayAddress":true,"PrefixLength":16,"IPv4Mask":"***********"},{"IPAddress":"127.0.0.1","HasGateWayAddress":false,"PrefixLength":8,"IPv4Mask":"*********"},{"IPAddress":"::1","HasGateWayAddress":false,"PrefixLength":128}]
2025-06-04 15:23:35.374 Info SqliteDisplayPreferencesRepository: Opening sqlite connection to /config/data/displaypreferences.db
2025-06-04 15:23:35.387 Info SqliteDisplayPreferencesRepository: Default journal_mode for /config/data/displaypreferences.db is wal
2025-06-04 15:23:35.387 Info SqliteDisplayPreferencesRepository: PRAGMA foreign_keys=1
2025-06-04 15:23:35.387 Info SqliteDisplayPreferencesRepository: Result of setting SQLITE_DBCONFIG_DQS_DDL to 0 is 0
2025-06-04 15:23:35.388 Info SqliteDisplayPreferencesRepository: Result of setting SQLITE_DBCONFIG_DQS_DML to 0 is 0
2025-06-04 15:23:35.393 Info App: Adding HttpListener prefix http://+:8096/
2025-06-04 15:23:35.456 Info AuthenticationRepository: Opening sqlite connection to /config/data/authentication.db
2025-06-04 15:23:35.480 Info AuthenticationRepository: Default journal_mode for /config/data/authentication.db is wal
2025-06-04 15:23:35.480 Info AuthenticationRepository: PRAGMA foreign_keys=1
2025-06-04 15:23:35.480 Info AuthenticationRepository: Result of setting SQLITE_DBCONFIG_DQS_DDL to 0 is 0
2025-06-04 15:23:35.480 Info AuthenticationRepository: Result of setting SQLITE_DBCONFIG_DQS_DML to 0 is 0
2025-06-04 15:23:35.486 Info SqliteItemRepository: Opening sqlite connection to /config/data/library.db
2025-06-04 15:23:35.542 Info SqliteItemRepository: Default journal_mode for /config/data/library.db is wal
2025-06-04 15:23:35.543 Info SqliteItemRepository: PRAGMA cache_size=-131072
2025-06-04 15:23:35.543 Info SqliteItemRepository: PRAGMA page_size=4096
2025-06-04 15:23:35.543 Info SqliteItemRepository: PRAGMA foreign_keys=1
2025-06-04 15:23:35.543 Info SqliteItemRepository: Result of setting SQLITE_DBCONFIG_DQS_DDL to 0 is 0
2025-06-04 15:23:35.543 Info SqliteItemRepository: Result of setting SQLITE_DBCONFIG_DQS_DML to 0 is 0
2025-06-04 15:23:35.582 Info SqliteItemRepository: Init Complete
2025-06-04 15:23:35.733 Info App: Emby
	Command line: /system/EmbyServer.dll -programdata /config -ffdetect /bin/ffdetect -ffmpeg /bin/ffmpeg -ffprobe /bin/ffprobe -restartexitcode 3
	Operating system: Linux version ********-microsoft-standard-WSL2 (root@af282157c79e) (gcc (GCC) 11.2.0, GNU ld (GNU Binutils) 2.37) #1 SMP PREEMPT_DYNAMIC Mon Apr 21 17
	Framework: .NET 6.0.36
	OS/Process: x64/x64
	Runtime: system/System.Private.CoreLib.dll
	Processor count: 12
	Data path: /config
	Application path: /system
2025-06-04 15:23:35.733 Info App: Logs path: /config/logs
2025-06-04 15:23:35.733 Info App: Cache path: /config/cache
2025-06-04 15:23:35.733 Info App: Internal metadata path: /config/metadata
2025-06-04 15:23:35.733 Info App: Transcoding temporary files path: /config/transcoding-temp
2025-06-04 15:23:35.746 Info Strm Assistant: Plugin is getting loaded.
2025-06-04 15:23:36.838 Info FfmpegManager: FFMpeg: /bin/ffmpeg
2025-06-04 15:23:36.838 Info FfmpegManager: FFProbe: /bin/ffprobe
2025-06-04 15:23:36.838 Info FfmpegManager: FFDetect: /bin/ffdetect
2025-06-04 15:23:36.855 Info Skia: SkiaSharp version: ********
2025-06-04 15:23:36.855 Info ImageProcessor: Adding image processor Skia
2025-06-04 15:23:36.921 Info libvips: NetVips version: *******
2025-06-04 15:23:36.921 Info ImageProcessor: Adding image processor libvips
2025-06-04 15:23:36.952 Info TaskManager: Daily trigger for Emby Server Backup set to fire at 06/05/2025 00:10:00, which is 526.3841350666667 minutes from now.
2025-06-04 15:23:36.980 Info TaskManager: Daily trigger for Video preview thumbnail extraction set to fire at 06/05/2025 02:00:00, which is 636.3836583916667 minutes from now.
2025-06-04 15:23:37.000 Info TaskManager: Daily trigger for Rotate log file set to fire at 06/05/2025 00:00:00, which is 516.383327835 minutes from now.
2025-06-04 15:23:37.016 Info TaskManager: Queueing task HardwareDetectionScheduledTask
2025-06-04 15:23:37.018 Info TaskManager: Executing Hardware Detection
2025-06-04 15:23:37.033 Info App: ServerId: abe2b0ac784c4ed894756b1f12c63150
2025-06-04 15:23:37.036 Info FfmpegManager: ProcessRun 'ffmpeg -hide_banner -version' Execute: /bin/ffmpeg -hide_banner -version
2025-06-04 15:23:37.057 Info App: Starting entry point Emby.Dlna.Main.DlnaEntryPoint
2025-06-04 15:23:37.089 Info FfmpegManager: ProcessRun 'ffmpeg -hide_banner -version' Process exited with code 0 - Succeeded
2025-06-04 15:23:37.101 Info App: Entry point completed: Emby.Dlna.Main.DlnaEntryPoint. Duration: 0.0441859 seconds
2025-06-04 15:23:37.101 Info App: Starting entry point Emby.Server.Implementations.Networking.RemoteAddressEntryPoint
2025-06-04 15:23:37.102 Info App: Loading data from /config/data/wan.dat
2025-06-04 15:23:37.104 Info App: Entry point completed: Emby.Server.Implementations.Networking.RemoteAddressEntryPoint. Duration: 0.0023746 seconds
2025-06-04 15:23:37.104 Info App: Starting entry point Emby.Server.Connect.ConnectEntryPoint
2025-06-04 15:23:37.105 Info App: Loading data from /config/data/connect.txt
2025-06-04 15:23:37.105 Info App: Entry point completed: Emby.Server.Connect.ConnectEntryPoint. Duration: 0.001484 seconds
2025-06-04 15:23:37.105 Info App: Core startup complete
2025-06-04 15:23:37.106 Info App: Starting entry point Emby.PortMapper.ExternalPortForwarding
2025-06-04 15:23:37.108 Info App: Entry point completed: Emby.PortMapper.ExternalPortForwarding. Duration: 0.0015335 seconds
2025-06-04 15:23:37.108 Info App: Starting entry point Emby.Security.PluginSecurityManager
2025-06-04 15:23:37.108 Info App: Entry point completed: Emby.Security.PluginSecurityManager. Duration: 4.13E-05 seconds
2025-06-04 15:23:37.108 Info App: Starting entry point Emby.Server.CinemaMode.IntrosEntryPoint
2025-06-04 15:23:37.108 Info App: Entry point completed: Emby.Server.CinemaMode.IntrosEntryPoint. Duration: 8.99E-05 seconds
2025-06-04 15:23:37.108 Info App: Starting entry point Emby.Webhooks.MigrationEntryPoint
2025-06-04 15:23:37.110 Info App: Entry point completed: Emby.Webhooks.MigrationEntryPoint. Duration: 0.0014638 seconds
2025-06-04 15:23:37.110 Info App: Starting entry point MBBackup.ServerEntryPoint
2025-06-04 15:23:37.110 Info App: Entry point completed: MBBackup.ServerEntryPoint. Duration: 7.36E-05 seconds
2025-06-04 15:23:37.110 Info App: Starting entry point MovieDb.Security.PluginStartup
2025-06-04 15:23:37.112 Info App: Entry point completed: MovieDb.Security.PluginStartup. Duration: 0.0015798 seconds
2025-06-04 15:23:37.112 Info App: Starting entry point NfoMetadata.EntryPoint
2025-06-04 15:23:37.112 Info App: Entry point completed: NfoMetadata.EntryPoint. Duration: 0.0001024 seconds
2025-06-04 15:23:37.112 Info App: Starting entry point Tvdb.EntryPoint
2025-06-04 15:23:37.112 Info App: Entry point completed: Tvdb.EntryPoint. Duration: 4.19E-05 seconds
2025-06-04 15:23:37.112 Info App: Starting entry point Emby.Server.Implementations.Udp.UdpServerEntryPoint
2025-06-04 15:23:37.114 Info App: Entry point completed: Emby.Server.Implementations.Udp.UdpServerEntryPoint. Duration: 0.001667 seconds
2025-06-04 15:23:37.114 Info App: Starting entry point Emby.Server.Implementations.Playlists.PlaylistUpgradeEntryPoint
2025-06-04 15:23:37.115 Info App: Entry point completed: Emby.Server.Implementations.Playlists.PlaylistUpgradeEntryPoint. Duration: 0.0013624 seconds
2025-06-04 15:23:37.115 Info App: Starting entry point Emby.Server.Implementations.Library.DeviceAccessEntryPoint
2025-06-04 15:23:37.116 Info App: Entry point completed: Emby.Server.Implementations.Library.DeviceAccessEntryPoint. Duration: 0.0002325 seconds
2025-06-04 15:23:37.116 Info App: Starting entry point Emby.Server.Implementations.IO.LibraryMonitorStartup
2025-06-04 15:23:37.120 Info FfmpegManager: FfmpegValidator.Validate complete
2025-06-04 15:23:37.129 Info App: Entry point completed: Emby.Server.Implementations.IO.LibraryMonitorStartup. Duration: 0.0136131 seconds
2025-06-04 15:23:37.129 Info App: Starting entry point Emby.Server.Implementations.EntryPoints.AutomaticRestartEntryPoint
2025-06-04 15:23:37.130 Info App: Entry point completed: Emby.Server.Implementations.EntryPoints.AutomaticRestartEntryPoint. Duration: 0.0004858 seconds
2025-06-04 15:23:37.130 Info App: Starting entry point Emby.Server.Implementations.EntryPoints.KeepServerAwake
2025-06-04 15:23:37.130 Info App: Entry point completed: Emby.Server.Implementations.EntryPoints.KeepServerAwake. Duration: 0.000114 seconds
2025-06-04 15:23:37.130 Info App: Starting entry point Emby.Server.Implementations.EntryPoints.LibraryChangedNotifier
2025-06-04 15:23:37.131 Info App: Entry point completed: Emby.Server.Implementations.EntryPoints.LibraryChangedNotifier. Duration: 0.0004875 seconds
2025-06-04 15:23:37.131 Info App: Starting entry point Emby.Server.Implementations.EntryPoints.ServerEventNotifier
2025-06-04 15:23:37.132 Info App: Entry point completed: Emby.Server.Implementations.EntryPoints.ServerEventNotifier. Duration: 0.0013006 seconds
2025-06-04 15:23:37.132 Info App: Starting entry point Emby.Server.Implementations.EntryPoints.StartupWizard
2025-06-04 15:23:37.132 Info App: Entry point completed: Emby.Server.Implementations.EntryPoints.StartupWizard. Duration: 0.0003118 seconds
2025-06-04 15:23:37.132 Info App: Starting entry point Emby.Server.Implementations.EntryPoints.SystemEvents
2025-06-04 15:23:37.133 Info App: Entry point completed: Emby.Server.Implementations.EntryPoints.SystemEvents. Duration: 0.0003058 seconds
2025-06-04 15:23:37.133 Info App: Starting entry point Emby.Server.Implementations.EntryPoints.UserDataChangeNotifier
2025-06-04 15:23:37.133 Info App: Entry point completed: Emby.Server.Implementations.EntryPoints.UserDataChangeNotifier. Duration: 9.54E-05 seconds
2025-06-04 15:23:37.133 Info App: Starting entry point Emby.Server.Implementations.Channels.ChannelsEntryPoint
2025-06-04 15:23:37.135 Info App: Entry point completed: Emby.Server.Implementations.Channels.ChannelsEntryPoint. Duration: 0.0020869 seconds
2025-06-04 15:23:37.135 Info App: Starting entry point Emby.LiveTV.EntryPoint
2025-06-04 15:23:37.137 Info LiveTV: Loading live tv data from /config/data/livetv/timers
2025-06-04 15:23:37.138 Info App: Entry point completed: Emby.LiveTV.EntryPoint. Duration: 0.0024585 seconds
2025-06-04 15:23:37.138 Info App: Starting entry point Emby.LiveTV.RecordingNotifier
2025-06-04 15:23:37.139 Info App: Entry point completed: Emby.LiveTV.RecordingNotifier. Duration: 0.0011983 seconds
2025-06-04 15:23:37.139 Info App: Starting entry point Emby.ActivityLog.ActivityLogEntryPoint
2025-06-04 15:23:37.141 Info App: Entry point completed: Emby.ActivityLog.ActivityLogEntryPoint. Duration: 0.0017729 seconds
2025-06-04 15:23:37.141 Info App: Starting entry point Emby.Server.MediaEncoding.Api.EncodingManagerEntryPoint
2025-06-04 15:23:37.141 Info App: Entry point completed: Emby.Server.MediaEncoding.Api.EncodingManagerEntryPoint. Duration: 0.0006781 seconds
2025-06-04 15:23:37.141 Info App: Starting entry point Emby.Notifications.NotificationManagerEntryPoint
2025-06-04 15:23:37.147 Info Notifications: Registering event nofitier Emby Server User Notifications
2025-06-04 15:23:37.150 Info App: Entry point completed: Emby.Notifications.NotificationManagerEntryPoint. Duration: 0.0082078 seconds
2025-06-04 15:23:37.150 Info App: Starting entry point Emby.Server.Sync.SyncNotificationEntryPoint
2025-06-04 15:23:37.151 Info App: Entry point completed: Emby.Server.Sync.SyncNotificationEntryPoint. Duration: 0.0013176 seconds
2025-06-04 15:23:37.151 Info App: Starting entry point EmbyServer.Windows.LoopUtilEntryPoint
2025-06-04 15:23:37.151 Info App: Entry point completed: EmbyServer.Windows.LoopUtilEntryPoint. Duration: 0.0001036 seconds
2025-06-04 15:23:37.151 Info App: All entry points have started
2025-06-04 15:23:37.173 Info SoftwareCodecProvider: h264, libx264, x264, V-E-libx264
2025-06-04 15:23:37.173 Info SoftwareCodecProvider: hevc, libx265, x265, V-E-libx265
2025-06-04 15:23:37.173 Info SoftwareCodecProvider: mpeg4, mpeg4, MPEG-4 part 2, V-E-mpeg4
2025-06-04 15:23:37.173 Info SoftwareCodecProvider: msmpeg4v3, msmpeg4, MPEG-4 part 2 (MS Variant 3), V-E-msmpeg4
2025-06-04 15:23:37.173 Info SoftwareCodecProvider: vp8, libvpx, libvpx VP8, V-E-libvpx
2025-06-04 15:23:37.175 Info SoftwareCodecProvider: h264, libx264, x264, V-E-libx264
2025-06-04 15:23:37.175 Info SoftwareCodecProvider: hevc, libx265, x265, V-E-libx265
2025-06-04 15:23:37.175 Info SoftwareCodecProvider: mpeg4, mpeg4, MPEG-4 part 2, V-E-mpeg4
2025-06-04 15:23:37.175 Info SoftwareCodecProvider: msmpeg4v3, msmpeg4, MPEG-4 part 2 (MS Variant 3), V-E-msmpeg4
2025-06-04 15:23:37.175 Info SoftwareCodecProvider: vp8, libvpx, libvpx VP8, V-E-libvpx
2025-06-04 15:23:37.178 Info LibraryMonitor: Watching directory with new FileSystemWatcher for /media/动画电影 for item 13
2025-06-04 15:23:37.182 Info LibraryMonitor: Watching directory with new FileSystemWatcher for /media/国产剧 for item 7
2025-06-04 15:23:37.185 Info LibraryMonitor: Watching directory with new FileSystemWatcher for /media/国漫 for item 15
2025-06-04 15:23:37.186 Info VaapiCodecProvider: ProcessRun 'ffdetect_vaencdec' Execute: /bin/ffdetect -hide_banner -show_program_version -loglevel 48 -show_error -show_log 40 vaencdec -print_format json 
2025-06-04 15:23:37.188 Info LibraryMonitor: Watching directory with new FileSystemWatcher for /media/华语电影 for item 5
2025-06-04 15:23:37.192 Info LibraryMonitor: Watching directory with new FileSystemWatcher for /media/欧美剧 for item 11
2025-06-04 15:23:37.195 Info LibraryMonitor: Watching directory with new FileSystemWatcher for /media/外语电影 for item 9
2025-06-04 15:23:37.204 Info VaapiCodecProvider: ProcessRun 'ffdetect_vaencdec' Process exited with code 0 - Succeeded
2025-06-04 15:23:37.237 Info App: Init BeginReceive on 0.0.0.0
2025-06-04 15:23:37.238 Info App: Init BeginReceive on 0.0.0.0
2025-06-04 15:23:37.238 Info App: Init BeginReceive on **********
2025-06-04 15:23:37.238 Info App: Init BeginReceive on 127.0.0.1
2025-06-04 15:23:37.274 Info QuickSyncCodecProvider: ProcessRun 'ffdetect_qsvencdec' Execute: /bin/ffdetect -hide_banner -show_program_version -loglevel 48 -show_error -show_log 40 qsvencdec -print_format json 
2025-06-04 15:23:37.287 Info QuickSyncCodecProvider: ProcessRun 'ffdetect_qsvencdec' Process exited with code 0 - Succeeded
2025-06-04 15:23:37.342 Info NvidiaCodecProvider: ProcessRun 'ffdetect_nvencdec' Execute: /bin/ffdetect -hide_banner -show_program_version -loglevel 48 -show_error -show_log 40 nvencdec -print_format json 
2025-06-04 15:23:37.346 Info NvidiaCodecProvider: ProcessRun 'ffdetect_nvencdec' Process exited with code 1 - Failed
2025-06-04 15:23:37.494 Info CodecManager: CodecList:
2025-06-04 15:23:37.495 Info TaskManager: Hardware Detection Completed after 0 minute(s) and 0 seconds
2025-06-04 15:23:39.986 Info TaskManager: Queueing task PluginUpdateTask
2025-06-04 15:23:39.986 Info TaskManager: Executing Check for plugin updates
2025-06-04 15:23:39.990 Info HttpClient: GET https://www.mb3admin.com/admin/service/EmbyPackages.json
2025-06-04 15:23:39.993 Info TaskManager: Queueing task SystemUpdateTask
2025-06-04 15:23:39.993 Info TaskManager: Executing Check for application updates
2025-06-04 15:23:40.010 Info TaskManager: Queueing task SubtitleOcrDataTask
2025-06-04 15:23:40.010 Info TaskManager: Executing Download OCR Data
2025-06-04 15:23:40.024 Info TaskManager: Download OCR Data Completed after 0 minute(s) and 0 seconds
2025-06-04 15:23:40.124 Info App: No application update available.
2025-06-04 15:23:40.124 Info TaskManager: Check for application updates Completed after 0 minute(s) and 0 seconds
2025-06-04 15:23:44.411 Info TaskManager: Check for plugin updates Completed after 0 minute(s) and 4 seconds
