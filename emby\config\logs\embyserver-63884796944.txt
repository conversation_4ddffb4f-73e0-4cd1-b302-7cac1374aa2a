2025-06-06 08:19:42.064 Info Main: Application path: /system/EmbyServer.dll
2025-06-06 08:19:42.201 Info NetworkManager: Adding event handler for NetworkChange.NetworkAddressChanged
2025-06-06 08:19:42.453 Info App: Setting default culture to zh-CN
2025-06-06 08:19:42.462 Info Main: Emby
	Command line: /system/EmbyServer.dll -programdata /config -ffdetect /bin/ffdetect -ffmpeg /bin/ffmpeg -ffprobe /bin/ffprobe -restartexitcode 3
	Operating system: Linux version ********-microsoft-standard-WSL2 (root@af282157c79e) (gcc (GCC) 11.2.0, GNU ld (GNU Binutils) 2.37) #1 SMP PREEMPT_DYNAMIC Mon Apr 21 17
	Framework: .NET 6.0.36
	OS/Process: x64/x64
	Runtime: system/System.Private.CoreLib.dll
	Processor count: 12
	Data path: /config
	Application path: /system
2025-06-06 08:19:42.462 Info Main: Logs path: /config/logs
2025-06-06 08:19:42.462 Info Main: Cache path: /config/cache
2025-06-06 08:19:42.462 Info Main: Internal metadata path: /config/metadata
2025-06-06 08:19:42.464 Info App: Emby Server Version: ********
2025-06-06 08:19:42.466 Info App: Loading assemblies
2025-06-06 08:19:42.563 Info App: File /config/plugins/AudioDb.dll has version ********
2025-06-06 08:19:42.564 Info App: File /system/plugins/AudioDb.dll has version 1.0.18.0
2025-06-06 08:19:42.571 Info App: File /config/plugins/MovieDb.dll has version 1.8.3.0
2025-06-06 08:19:42.572 Info App: File /system/plugins/MovieDb.dll has version 1.8.0.0
2025-06-06 08:19:42.579 Info App: File /config/plugins/OpenSubtitles.dll has version 1.0.64.0
2025-06-06 08:19:42.580 Info App: File /system/plugins/OpenSubtitles.dll has version 1.0.63.0
2025-06-06 08:19:42.587 Info App: File /config/plugins/Emby.Server.CinemaMode.dll has version 1.0.47.0
2025-06-06 08:19:42.588 Info App: File /system/plugins/Emby.Server.CinemaMode.dll has version 1.0.47.0
2025-06-06 08:19:42.595 Info App: File /config/plugins/Emby.Dlna.dll has version 1.5.0.0
2025-06-06 08:19:42.597 Info App: File /system/plugins/Emby.Dlna.dll has version 1.4.7.0
2025-06-06 08:19:42.605 Info App: File /config/plugins/NfoMetadata.dll has version 1.0.83.0
2025-06-06 08:19:42.606 Info App: File /system/plugins/NfoMetadata.dll has version 1.0.82.0
2025-06-06 08:19:42.612 Info App: File /config/plugins/Tvdb.dll has version 1.6.2.0
2025-06-06 08:19:42.613 Info App: File /system/plugins/Tvdb.dll has version 1.5.8.0
2025-06-06 08:19:42.620 Info App: File /config/plugins/StudioImages.dll has version 1.0.3.0
2025-06-06 08:19:42.620 Info App: File /system/plugins/StudioImages.dll has version 1.0.3.0
2025-06-06 08:19:42.627 Info App: File /config/plugins/OMDb.dll has version 1.0.22.0
2025-06-06 08:19:42.628 Info App: File /system/plugins/OMDb.dll has version 1.0.21.0
2025-06-06 08:19:42.635 Info App: File /config/plugins/EmbyGuideData.dll has version 1.0.18.0
2025-06-06 08:19:42.636 Info App: File /system/plugins/EmbyGuideData.dll has version 1.0.18.0
2025-06-06 08:19:42.642 Info App: File /config/plugins/DvdMounter.dll has version 1.0.0.0
2025-06-06 08:19:42.642 Info App: File /system/plugins/DvdMounter.dll has version 1.0.0.0
2025-06-06 08:19:42.649 Info App: File /config/plugins/Fanart.dll has version 1.0.16.0
2025-06-06 08:19:42.650 Info App: File /system/plugins/Fanart.dll has version 1.0.16.0
2025-06-06 08:19:42.656 Info App: File /config/plugins/Emby.M3UTuner.dll has version 1.0.39.0
2025-06-06 08:19:42.657 Info App: File /system/plugins/Emby.M3UTuner.dll has version 1.0.39.0
2025-06-06 08:19:42.663 Info App: File /config/plugins/Emby.Webhooks.dll has version 1.0.35.0
2025-06-06 08:19:42.664 Info App: File /system/plugins/Emby.Webhooks.dll has version 1.0.35.0
2025-06-06 08:19:42.670 Info App: File /config/plugins/Emby.PortMapper.dll has version 1.2.8.0
2025-06-06 08:19:42.671 Info App: File /system/plugins/Emby.PortMapper.dll has version 1.2.8.0
2025-06-06 08:19:42.677 Info App: File /config/plugins/MusicBrainz.dll has version 1.0.25.0
2025-06-06 08:19:42.678 Info App: File /system/plugins/MusicBrainz.dll has version 1.0.24.0
2025-06-06 08:19:42.685 Info App: File /config/plugins/Emby.XmlTV.dll has version 1.2.0.0
2025-06-06 08:19:42.686 Info App: File /system/plugins/Emby.XmlTV.dll has version 1.2.0.0
2025-06-06 08:19:42.693 Info App: File /config/plugins/BlurayMounter.dll has version 1.0.2.0
2025-06-06 08:19:42.694 Info App: File /system/plugins/BlurayMounter.dll has version 1.0.2.0
2025-06-06 08:19:42.702 Info App: File /config/plugins/MBBackup.dll has version 1.7.8.0
2025-06-06 08:19:42.703 Info App: File /system/plugins/MBBackup.dll has version 1.7.2.0
2025-06-06 08:19:42.717 Info App: File /config/plugins/StrmAssistant.dll has version 2.0.0.24
2025-06-06 08:19:42.723 Info App: File /system/plugins/StrmAssistant.dll has version 2.0.0.18
2025-06-06 08:19:42.823 Info App: Loading AudioDb, Version=********, Culture=neutral, PublicKeyToken=null from /config/plugins/AudioDb.dll
2025-06-06 08:19:42.823 Info App: Loading BlurayMounter, Version=1.0.2.0, Culture=neutral, PublicKeyToken=null from /config/plugins/BlurayMounter.dll
2025-06-06 08:19:42.823 Info App: Loading DvdMounter, Version=1.0.0.0, Culture=neutral, PublicKeyToken=null from /config/plugins/DvdMounter.dll
2025-06-06 08:19:42.823 Info App: Loading Emby.Dlna, Version=1.5.0.0, Culture=neutral, PublicKeyToken=null from /config/plugins/Emby.Dlna.dll
2025-06-06 08:19:42.823 Info App: Loading Emby.M3UTuner, Version=1.0.39.0, Culture=neutral, PublicKeyToken=null from /config/plugins/Emby.M3UTuner.dll
2025-06-06 08:19:42.823 Info App: Loading Emby.PortMapper, Version=1.2.8.0, Culture=neutral, PublicKeyToken=null from /config/plugins/Emby.PortMapper.dll
2025-06-06 08:19:42.823 Info App: Loading Emby.Server.CinemaMode, Version=1.0.47.0, Culture=neutral, PublicKeyToken=null from /config/plugins/Emby.Server.CinemaMode.dll
2025-06-06 08:19:42.823 Info App: Loading Emby.Webhooks, Version=1.0.35.0, Culture=neutral, PublicKeyToken=null from /config/plugins/Emby.Webhooks.dll
2025-06-06 08:19:42.823 Info App: Loading Emby.XmlTV, Version=1.2.0.0, Culture=neutral, PublicKeyToken=null from /config/plugins/Emby.XmlTV.dll
2025-06-06 08:19:42.823 Info App: Loading EmbyGuideData, Version=1.0.18.0, Culture=neutral, PublicKeyToken=null from /config/plugins/EmbyGuideData.dll
2025-06-06 08:19:42.823 Info App: Loading Fanart, Version=1.0.16.0, Culture=neutral, PublicKeyToken=null from /config/plugins/Fanart.dll
2025-06-06 08:19:42.823 Info App: Loading MBBackup, Version=1.7.8.0, Culture=neutral, PublicKeyToken=null from /config/plugins/MBBackup.dll
2025-06-06 08:19:42.823 Info App: Loading MovieDb, Version=1.8.3.0, Culture=neutral, PublicKeyToken=null from /config/plugins/MovieDb.dll
2025-06-06 08:19:42.823 Info App: Loading MusicBrainz, Version=1.0.25.0, Culture=neutral, PublicKeyToken=null from /config/plugins/MusicBrainz.dll
2025-06-06 08:19:42.823 Info App: Loading NfoMetadata, Version=1.0.83.0, Culture=neutral, PublicKeyToken=null from /config/plugins/NfoMetadata.dll
2025-06-06 08:19:42.823 Info App: Loading OMDb, Version=1.0.22.0, Culture=neutral, PublicKeyToken=null from /config/plugins/OMDb.dll
2025-06-06 08:19:42.823 Info App: Loading OpenSubtitles, Version=1.0.64.0, Culture=neutral, PublicKeyToken=null from /config/plugins/OpenSubtitles.dll
2025-06-06 08:19:42.823 Info App: Loading StrmAssistant, Version=2.0.0.24, Culture=neutral, PublicKeyToken=null from /config/plugins/StrmAssistant.dll
2025-06-06 08:19:42.823 Info App: Loading StudioImages, Version=1.0.3.0, Culture=neutral, PublicKeyToken=null from /config/plugins/StudioImages.dll
2025-06-06 08:19:42.823 Info App: Loading Tvdb, Version=1.6.2.0, Culture=neutral, PublicKeyToken=null from /config/plugins/Tvdb.dll
2025-06-06 08:19:42.823 Info App: Loading Emby.Api, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-06 08:19:42.823 Info App: Loading Emby.Web, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-06 08:19:42.823 Info App: Loading MediaBrowser.Model, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-06 08:19:42.823 Info App: Loading MediaBrowser.Common, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-06 08:19:42.823 Info App: Loading MediaBrowser.Controller, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-06 08:19:42.823 Info App: Loading Emby.Providers, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-06 08:19:42.823 Info App: Loading Emby.Photos, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-06 08:19:42.823 Info App: Loading Emby.Server.Implementations, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-06 08:19:42.823 Info App: Loading Emby.LiveTV, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-06 08:19:42.823 Info App: Loading Emby.ActivityLog, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-06 08:19:42.823 Info App: Loading Emby.Server.MediaEncoding, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-06 08:19:42.823 Info App: Loading Emby.LocalMetadata, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-06 08:19:42.824 Info App: Loading Emby.Notifications, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-06 08:19:42.824 Info App: Loading Emby.Web.GenericUI, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-06 08:19:42.824 Info App: Loading Emby.Codecs.Dxva, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-06 08:19:42.824 Info App: Loading Emby.Codecs, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-06 08:19:42.824 Info App: Loading Emby.Server.Connect, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-06 08:19:42.824 Info App: Loading Emby.Server.Sync, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-06 08:19:42.824 Info App: Loading EmbyServer, Version=********, Culture=neutral, PublicKeyToken=null
2025-06-06 08:19:43.170 Info SqliteUserRepository: Sqlite version: 3.42.0
2025-06-06 08:19:43.171 Info SqliteUserRepository: Sqlite compiler options: ATOMIC_INTRINSICS=1,COMPILER=gcc-10.3.0,DEFAULT_AUTOVACUUM,DEFAULT_CACHE_SIZE=-2000,DEFAULT_FILE_FORMAT=4,DEFAULT_JOURNAL_SIZE_LIMIT=-1,DEFAULT_MMAP_SIZE=0,DEFAULT_PAGE_SIZE=4096,DEFAULT_PCACHE_INITSZ=20,DEFAULT_RECURSIVE_TRIGGERS,DEFAULT_SECTOR_SIZE=4096,DEFAULT_SYNCHRONOUS=2,DEFAULT_WAL_AUTOCHECKPOINT=1000,DEFAULT_WAL_SYNCHRONOUS=2,DEFAULT_WORKER_THREADS=0,ENABLE_COLUMN_METADATA,ENABLE_DBSTAT_VTAB,ENABLE_FTS3,ENABLE_FTS3_PARENTHESIS,ENABLE_FTS3_TOKENIZER,ENABLE_FTS4,ENABLE_FTS5,ENABLE_GEOPOLY,ENABLE_MATH_FUNCTIONS,ENABLE_PREUPDATE_HOOK,ENABLE_RTREE,ENABLE_SESSION,ENABLE_UNLOCK_NOTIFY,ENABLE_UPDATE_DELETE_LIMIT,LIKE_DOESNT_MATCH_BLOBS,MALLOC_SOFT_LIMIT=1024,MAX_ATTACHED=10,MAX_COLUMN=2000,MAX_COMPOUND_SELECT=500,MAX_DEFAULT_PAGE_SIZE=8192,MAX_EXPR_DEPTH=1000,MAX_FUNCTION_ARG=127,MAX_LENGTH=1000000000,MAX_LIKE_PATTERN_LENGTH=50000,MAX_MMAP_SIZE=0x7fff0000,MAX_PAGE_COUNT=1073741823,MAX_PAGE_SIZE=65536,MAX_SCHEMA_RETRY=25,MAX_SQL_LENGTH=1000000000,MAX_TRIGGER_DEPTH=1000,MAX_VARIABLE_NUMBER=250000,MAX_VDBE_OP=250000000,MAX_WORKER_THREADS=8,MUTEX_PTHREADS,OMIT_LOOKASIDE,SECURE_DELETE,SYSTEM_MALLOC,TEMP_STORE=1,THREADSAFE=1
2025-06-06 08:19:43.171 Info SqliteUserRepository: Opening sqlite connection to /config/data/users.db
2025-06-06 08:19:43.195 Info SqliteUserRepository: Default journal_mode for /config/data/users.db is wal
2025-06-06 08:19:43.196 Info SqliteUserRepository: PRAGMA foreign_keys=1
2025-06-06 08:19:43.196 Info SqliteUserRepository: Result of setting SQLITE_DBCONFIG_DQS_DDL to 0 is 0
2025-06-06 08:19:43.196 Info SqliteUserRepository: Result of setting SQLITE_DBCONFIG_DQS_DML to 0 is 0
2025-06-06 08:19:43.247 Info ActivityRepository: Opening sqlite connection to /config/data/activitylog.db
2025-06-06 08:19:43.263 Info ActivityRepository: Default journal_mode for /config/data/activitylog.db is wal
2025-06-06 08:19:43.263 Info ActivityRepository: PRAGMA foreign_keys=1
2025-06-06 08:19:43.263 Info ActivityRepository: Result of setting SQLITE_DBCONFIG_DQS_DDL to 0 is 0
2025-06-06 08:19:43.263 Info ActivityRepository: Result of setting SQLITE_DBCONFIG_DQS_DML to 0 is 0
2025-06-06 08:19:43.276 Info NetworkManager: Detecting local network addresses
2025-06-06 08:19:43.276 Info NetworkManager: networkInterface: Ethernet eth0, Speed: 10000000000, Description: eth0
2025-06-06 08:19:43.277 Info NetworkManager: GatewayAddresses: **********
2025-06-06 08:19:43.277 Info NetworkManager: UnicastAddresses: **********
2025-06-06 08:19:43.277 Info NetworkManager: networkInterface: Loopback lo, Speed: -1, Description: lo
2025-06-06 08:19:43.278 Info NetworkManager: GatewayAddresses: 
2025-06-06 08:19:43.278 Info NetworkManager: UnicastAddresses: 127.0.0.1,::1
2025-06-06 08:19:43.313 Info NetworkManager: Detected local ip addresses: [{"IPAddress":"**********","HasGateWayAddress":true,"PrefixLength":16,"IPv4Mask":"***********"},{"IPAddress":"127.0.0.1","HasGateWayAddress":false,"PrefixLength":8,"IPv4Mask":"*********"},{"IPAddress":"::1","HasGateWayAddress":false,"PrefixLength":128}]
2025-06-06 08:19:43.315 Info SqliteDisplayPreferencesRepository: Opening sqlite connection to /config/data/displaypreferences.db
2025-06-06 08:19:43.329 Info SqliteDisplayPreferencesRepository: Default journal_mode for /config/data/displaypreferences.db is wal
2025-06-06 08:19:43.329 Info SqliteDisplayPreferencesRepository: PRAGMA foreign_keys=1
2025-06-06 08:19:43.329 Info SqliteDisplayPreferencesRepository: Result of setting SQLITE_DBCONFIG_DQS_DDL to 0 is 0
2025-06-06 08:19:43.329 Info SqliteDisplayPreferencesRepository: Result of setting SQLITE_DBCONFIG_DQS_DML to 0 is 0
2025-06-06 08:19:43.335 Info App: Adding HttpListener prefix http://+:8096/
2025-06-06 08:19:43.398 Info AuthenticationRepository: Opening sqlite connection to /config/data/authentication.db
2025-06-06 08:19:43.436 Info AuthenticationRepository: Default journal_mode for /config/data/authentication.db is wal
2025-06-06 08:19:43.436 Info AuthenticationRepository: PRAGMA foreign_keys=1
2025-06-06 08:19:43.436 Info AuthenticationRepository: Result of setting SQLITE_DBCONFIG_DQS_DDL to 0 is 0
2025-06-06 08:19:43.436 Info AuthenticationRepository: Result of setting SQLITE_DBCONFIG_DQS_DML to 0 is 0
2025-06-06 08:19:43.442 Info SqliteItemRepository: Opening sqlite connection to /config/data/library.db
2025-06-06 08:19:43.503 Info SqliteItemRepository: Default journal_mode for /config/data/library.db is wal
2025-06-06 08:19:43.503 Info SqliteItemRepository: PRAGMA cache_size=-131072
2025-06-06 08:19:43.503 Info SqliteItemRepository: PRAGMA page_size=4096
2025-06-06 08:19:43.504 Info SqliteItemRepository: PRAGMA foreign_keys=1
2025-06-06 08:19:43.504 Info SqliteItemRepository: Result of setting SQLITE_DBCONFIG_DQS_DDL to 0 is 0
2025-06-06 08:19:43.504 Info SqliteItemRepository: Result of setting SQLITE_DBCONFIG_DQS_DML to 0 is 0
2025-06-06 08:19:43.546 Info SqliteItemRepository: Init Complete
2025-06-06 08:19:43.693 Info App: Emby
	Command line: /system/EmbyServer.dll -programdata /config -ffdetect /bin/ffdetect -ffmpeg /bin/ffmpeg -ffprobe /bin/ffprobe -restartexitcode 3
	Operating system: Linux version ********-microsoft-standard-WSL2 (root@af282157c79e) (gcc (GCC) 11.2.0, GNU ld (GNU Binutils) 2.37) #1 SMP PREEMPT_DYNAMIC Mon Apr 21 17
	Framework: .NET 6.0.36
	OS/Process: x64/x64
	Runtime: system/System.Private.CoreLib.dll
	Processor count: 12
	Data path: /config
	Application path: /system
2025-06-06 08:19:43.693 Info App: Logs path: /config/logs
2025-06-06 08:19:43.693 Info App: Cache path: /config/cache
2025-06-06 08:19:43.693 Info App: Internal metadata path: /config/metadata
2025-06-06 08:19:43.693 Info App: Transcoding temporary files path: /config/transcoding-temp
2025-06-06 08:19:43.706 Info Strm Assistant: Plugin is getting loaded.
2025-06-06 08:19:44.933 Info FfmpegManager: FFMpeg: /bin/ffmpeg
2025-06-06 08:19:44.934 Info FfmpegManager: FFProbe: /bin/ffprobe
2025-06-06 08:19:44.934 Info FfmpegManager: FFDetect: /bin/ffdetect
2025-06-06 08:19:44.950 Info Skia: SkiaSharp version: ********
2025-06-06 08:19:44.951 Info ImageProcessor: Adding image processor Skia
2025-06-06 08:19:45.017 Info libvips: NetVips version: *******
2025-06-06 08:19:45.018 Info ImageProcessor: Adding image processor libvips
2025-06-06 08:19:45.053 Info TaskManager: Daily trigger for Emby Server Backup set to fire at 06/07/2025 00:10:00, which is 950.249114625 minutes from now.
2025-06-06 08:19:45.082 Info TaskManager: Daily trigger for Video preview thumbnail extraction set to fire at 06/07/2025 02:00:00, which is 1060.248618915 minutes from now.
2025-06-06 08:19:45.103 Info TaskManager: Daily trigger for Rotate log file set to fire at 06/07/2025 00:00:00, which is 940.2482806233334 minutes from now.
2025-06-06 08:19:45.119 Info TaskManager: Queueing task HardwareDetectionScheduledTask
2025-06-06 08:19:45.121 Info TaskManager: Executing Hardware Detection
2025-06-06 08:19:45.136 Info App: ServerId: abe2b0ac784c4ed894756b1f12c63150
2025-06-06 08:19:45.139 Info FfmpegManager: ProcessRun 'ffmpeg -hide_banner -version' Execute: /bin/ffmpeg -hide_banner -version
2025-06-06 08:19:45.161 Info App: Starting entry point Emby.Dlna.Main.DlnaEntryPoint
2025-06-06 08:19:45.194 Info FfmpegManager: ProcessRun 'ffmpeg -hide_banner -version' Process exited with code 0 - Succeeded
2025-06-06 08:19:45.207 Info App: Entry point completed: Emby.Dlna.Main.DlnaEntryPoint. Duration: 0.045615 seconds
2025-06-06 08:19:45.207 Info App: Starting entry point Emby.Server.Implementations.Networking.RemoteAddressEntryPoint
2025-06-06 08:19:45.208 Info App: Loading data from /config/data/wan.dat
2025-06-06 08:19:45.209 Info App: Entry point completed: Emby.Server.Implementations.Networking.RemoteAddressEntryPoint. Duration: 0.0024085 seconds
2025-06-06 08:19:45.210 Info App: Starting entry point Emby.Server.Connect.ConnectEntryPoint
2025-06-06 08:19:45.211 Info App: Loading data from /config/data/connect.txt
2025-06-06 08:19:45.211 Info App: Entry point completed: Emby.Server.Connect.ConnectEntryPoint. Duration: 0.001818 seconds
2025-06-06 08:19:45.211 Info App: Core startup complete
2025-06-06 08:19:45.213 Info App: Starting entry point Emby.PortMapper.ExternalPortForwarding
2025-06-06 08:19:45.215 Info App: Entry point completed: Emby.PortMapper.ExternalPortForwarding. Duration: 0.0017295 seconds
2025-06-06 08:19:45.215 Info App: Starting entry point Emby.Security.PluginSecurityManager
2025-06-06 08:19:45.215 Info App: Entry point completed: Emby.Security.PluginSecurityManager. Duration: 5.46E-05 seconds
2025-06-06 08:19:45.215 Info App: Starting entry point Emby.Server.CinemaMode.IntrosEntryPoint
2025-06-06 08:19:45.215 Info App: Entry point completed: Emby.Server.CinemaMode.IntrosEntryPoint. Duration: 8.73E-05 seconds
2025-06-06 08:19:45.215 Info App: Starting entry point Emby.Webhooks.MigrationEntryPoint
2025-06-06 08:19:45.217 Info App: Entry point completed: Emby.Webhooks.MigrationEntryPoint. Duration: 0.0014637 seconds
2025-06-06 08:19:45.217 Info App: Starting entry point MBBackup.ServerEntryPoint
2025-06-06 08:19:45.217 Info App: Entry point completed: MBBackup.ServerEntryPoint. Duration: 3.35E-05 seconds
2025-06-06 08:19:45.217 Info App: Starting entry point MovieDb.Security.PluginStartup
2025-06-06 08:19:45.218 Info App: Entry point completed: MovieDb.Security.PluginStartup. Duration: 0.0014949 seconds
2025-06-06 08:19:45.218 Info App: Starting entry point NfoMetadata.EntryPoint
2025-06-06 08:19:45.219 Info App: Entry point completed: NfoMetadata.EntryPoint. Duration: 8.55E-05 seconds
2025-06-06 08:19:45.219 Info App: Starting entry point Tvdb.EntryPoint
2025-06-06 08:19:45.219 Info App: Entry point completed: Tvdb.EntryPoint. Duration: 4.64E-05 seconds
2025-06-06 08:19:45.219 Info App: Starting entry point Emby.Server.Implementations.Udp.UdpServerEntryPoint
2025-06-06 08:19:45.220 Info App: Entry point completed: Emby.Server.Implementations.Udp.UdpServerEntryPoint. Duration: 0.0015897 seconds
2025-06-06 08:19:45.220 Info App: Starting entry point Emby.Server.Implementations.Playlists.PlaylistUpgradeEntryPoint
2025-06-06 08:19:45.222 Info App: Entry point completed: Emby.Server.Implementations.Playlists.PlaylistUpgradeEntryPoint. Duration: 0.0012206 seconds
2025-06-06 08:19:45.222 Info App: Starting entry point Emby.Server.Implementations.Library.DeviceAccessEntryPoint
2025-06-06 08:19:45.222 Info App: Entry point completed: Emby.Server.Implementations.Library.DeviceAccessEntryPoint. Duration: 0.0002947 seconds
2025-06-06 08:19:45.222 Info App: Starting entry point Emby.Server.Implementations.IO.LibraryMonitorStartup
2025-06-06 08:19:45.227 Info FfmpegManager: FfmpegValidator.Validate complete
2025-06-06 08:19:45.236 Info App: Entry point completed: Emby.Server.Implementations.IO.LibraryMonitorStartup. Duration: 0.0138262 seconds
2025-06-06 08:19:45.236 Info App: Starting entry point Emby.Server.Implementations.EntryPoints.AutomaticRestartEntryPoint
2025-06-06 08:19:45.236 Info App: Entry point completed: Emby.Server.Implementations.EntryPoints.AutomaticRestartEntryPoint. Duration: 0.0003764 seconds
2025-06-06 08:19:45.236 Info App: Starting entry point Emby.Server.Implementations.EntryPoints.KeepServerAwake
2025-06-06 08:19:45.237 Info App: Entry point completed: Emby.Server.Implementations.EntryPoints.KeepServerAwake. Duration: 0.0001585 seconds
2025-06-06 08:19:45.237 Info App: Starting entry point Emby.Server.Implementations.EntryPoints.LibraryChangedNotifier
2025-06-06 08:19:45.237 Info App: Entry point completed: Emby.Server.Implementations.EntryPoints.LibraryChangedNotifier. Duration: 0.0005765 seconds
2025-06-06 08:19:45.237 Info App: Starting entry point Emby.Server.Implementations.EntryPoints.ServerEventNotifier
2025-06-06 08:19:45.239 Info App: Entry point completed: Emby.Server.Implementations.EntryPoints.ServerEventNotifier. Duration: 0.0016877 seconds
2025-06-06 08:19:45.239 Info App: Starting entry point Emby.Server.Implementations.EntryPoints.StartupWizard
2025-06-06 08:19:45.239 Info App: Entry point completed: Emby.Server.Implementations.EntryPoints.StartupWizard. Duration: 0.0003267 seconds
2025-06-06 08:19:45.239 Info App: Starting entry point Emby.Server.Implementations.EntryPoints.SystemEvents
2025-06-06 08:19:45.240 Info App: Entry point completed: Emby.Server.Implementations.EntryPoints.SystemEvents. Duration: 0.0003253 seconds
2025-06-06 08:19:45.240 Info App: Starting entry point Emby.Server.Implementations.EntryPoints.UserDataChangeNotifier
2025-06-06 08:19:45.240 Info App: Entry point completed: Emby.Server.Implementations.EntryPoints.UserDataChangeNotifier. Duration: 8.17E-05 seconds
2025-06-06 08:19:45.240 Info App: Starting entry point Emby.Server.Implementations.Channels.ChannelsEntryPoint
2025-06-06 08:19:45.242 Info App: Entry point completed: Emby.Server.Implementations.Channels.ChannelsEntryPoint. Duration: 0.0018058 seconds
2025-06-06 08:19:45.242 Info App: Starting entry point Emby.LiveTV.EntryPoint
2025-06-06 08:19:45.244 Info LiveTV: Loading live tv data from /config/data/livetv/timers
2025-06-06 08:19:45.245 Info App: Entry point completed: Emby.LiveTV.EntryPoint. Duration: 0.0027347 seconds
2025-06-06 08:19:45.245 Info App: Starting entry point Emby.LiveTV.RecordingNotifier
2025-06-06 08:19:45.246 Info App: Entry point completed: Emby.LiveTV.RecordingNotifier. Duration: 0.0015613 seconds
2025-06-06 08:19:45.246 Info App: Starting entry point Emby.ActivityLog.ActivityLogEntryPoint
2025-06-06 08:19:45.248 Info App: Entry point completed: Emby.ActivityLog.ActivityLogEntryPoint. Duration: 0.0018795 seconds
2025-06-06 08:19:45.248 Info App: Starting entry point Emby.Server.MediaEncoding.Api.EncodingManagerEntryPoint
2025-06-06 08:19:45.249 Info App: Entry point completed: Emby.Server.MediaEncoding.Api.EncodingManagerEntryPoint. Duration: 0.0009525 seconds
2025-06-06 08:19:45.249 Info App: Starting entry point Emby.Notifications.NotificationManagerEntryPoint
2025-06-06 08:19:45.256 Info Notifications: Registering event nofitier Emby Server User Notifications
2025-06-06 08:19:45.258 Info App: Entry point completed: Emby.Notifications.NotificationManagerEntryPoint. Duration: 0.0087777 seconds
2025-06-06 08:19:45.258 Info App: Starting entry point Emby.Server.Sync.SyncNotificationEntryPoint
2025-06-06 08:19:45.260 Info App: Entry point completed: Emby.Server.Sync.SyncNotificationEntryPoint. Duration: 0.0011833 seconds
2025-06-06 08:19:45.260 Info App: Starting entry point EmbyServer.Windows.LoopUtilEntryPoint
2025-06-06 08:19:45.260 Info App: Entry point completed: EmbyServer.Windows.LoopUtilEntryPoint. Duration: 5.5E-05 seconds
2025-06-06 08:19:45.260 Info App: All entry points have started
2025-06-06 08:19:45.270 Info LibraryMonitor: Watching directory with new FileSystemWatcher for /media/动画电影 for item 13
2025-06-06 08:19:45.274 Info LibraryMonitor: Watching directory with new FileSystemWatcher for /media/国产剧 for item 7
2025-06-06 08:19:45.278 Info LibraryMonitor: Watching directory with new FileSystemWatcher for /media/国漫 for item 15
2025-06-06 08:19:45.281 Info LibraryMonitor: Watching directory with new FileSystemWatcher for /media/华语电影 for item 5
2025-06-06 08:19:45.285 Info LibraryMonitor: Watching directory with new FileSystemWatcher for /media/欧美剧 for item 11
2025-06-06 08:19:45.285 Info SoftwareCodecProvider: h264, libx264, x264, V-E-libx264
2025-06-06 08:19:45.285 Info SoftwareCodecProvider: hevc, libx265, x265, V-E-libx265
2025-06-06 08:19:45.285 Info SoftwareCodecProvider: mpeg4, mpeg4, MPEG-4 part 2, V-E-mpeg4
2025-06-06 08:19:45.285 Info SoftwareCodecProvider: msmpeg4v3, msmpeg4, MPEG-4 part 2 (MS Variant 3), V-E-msmpeg4
2025-06-06 08:19:45.285 Info SoftwareCodecProvider: vp8, libvpx, libvpx VP8, V-E-libvpx
2025-06-06 08:19:45.287 Info SoftwareCodecProvider: h264, libx264, x264, V-E-libx264
2025-06-06 08:19:45.287 Info SoftwareCodecProvider: hevc, libx265, x265, V-E-libx265
2025-06-06 08:19:45.287 Info SoftwareCodecProvider: mpeg4, mpeg4, MPEG-4 part 2, V-E-mpeg4
2025-06-06 08:19:45.287 Info SoftwareCodecProvider: msmpeg4v3, msmpeg4, MPEG-4 part 2 (MS Variant 3), V-E-msmpeg4
2025-06-06 08:19:45.287 Info SoftwareCodecProvider: vp8, libvpx, libvpx VP8, V-E-libvpx
2025-06-06 08:19:45.288 Info LibraryMonitor: Watching directory with new FileSystemWatcher for /media/外语电影 for item 9
2025-06-06 08:19:45.297 Info VaapiCodecProvider: ProcessRun 'ffdetect_vaencdec' Execute: /bin/ffdetect -hide_banner -show_program_version -loglevel 48 -show_error -show_log 40 vaencdec -print_format json 
2025-06-06 08:19:45.314 Info VaapiCodecProvider: ProcessRun 'ffdetect_vaencdec' Process exited with code 0 - Succeeded
2025-06-06 08:19:45.337 Info App: Init BeginReceive on 0.0.0.0
2025-06-06 08:19:45.337 Info App: Init BeginReceive on 0.0.0.0
2025-06-06 08:19:45.337 Info App: Init BeginReceive on **********
2025-06-06 08:19:45.337 Info App: Init BeginReceive on 127.0.0.1
2025-06-06 08:19:45.382 Info QuickSyncCodecProvider: ProcessRun 'ffdetect_qsvencdec' Execute: /bin/ffdetect -hide_banner -show_program_version -loglevel 48 -show_error -show_log 40 qsvencdec -print_format json 
2025-06-06 08:19:45.398 Info QuickSyncCodecProvider: ProcessRun 'ffdetect_qsvencdec' Process exited with code 0 - Succeeded
2025-06-06 08:19:45.454 Info NvidiaCodecProvider: ProcessRun 'ffdetect_nvencdec' Execute: /bin/ffdetect -hide_banner -show_program_version -loglevel 48 -show_error -show_log 40 nvencdec -print_format json 
2025-06-06 08:19:45.459 Info NvidiaCodecProvider: ProcessRun 'ffdetect_nvencdec' Process exited with code 1 - Failed
2025-06-06 08:19:45.615 Info CodecManager: CodecList:
2025-06-06 08:19:45.616 Info TaskManager: Hardware Detection Completed after 0 minute(s) and 0 seconds
2025-06-06 08:19:48.088 Info TaskManager: Queueing task PluginUpdateTask
2025-06-06 08:19:48.088 Info TaskManager: Executing Check for plugin updates
2025-06-06 08:19:48.092 Info HttpClient: GET https://www.mb3admin.com/admin/service/EmbyPackages.json
2025-06-06 08:19:48.097 Info TaskManager: Queueing task SystemUpdateTask
2025-06-06 08:19:48.097 Info TaskManager: Executing Check for application updates
2025-06-06 08:19:48.116 Info TaskManager: Queueing task SubtitleOcrDataTask
2025-06-06 08:19:48.116 Info TaskManager: Executing Download OCR Data
2025-06-06 08:19:48.131 Info TaskManager: Download OCR Data Completed after 0 minute(s) and 0 seconds
2025-06-06 08:19:48.230 Info App: No application update available.
2025-06-06 08:19:48.230 Info TaskManager: Check for application updates Completed after 0 minute(s) and 0 seconds
2025-06-06 08:19:50.354 Info TaskManager: Check for plugin updates Completed after 0 minute(s) and 2 seconds
