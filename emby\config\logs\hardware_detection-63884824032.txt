{"ApplicationVersion": "4.8.11.0", "VersionType": "Release", "ReleaseCode": "abe2b0ac784c4ed894756b1f12c63150", "Environment": {"OperatingSystem": "Linux", "OperatingSystemName": "Unix", "OperatingSystemVersion": "Linux version 6.6.87.1-microsoft-standard-WSL2 (root@af282157c79e) (gcc (GCC) 11.2.0, GNU ld (GNU Binutils) 2.37) #1 SMP PREEMPT_DYNAMIC Mon Apr 21 17", "FrameworkVersion": ".NET 6.0.36", "OsArchitecture": "x64", "ProcessArchitecture": "x64", "PathSeparator": ":", "IsWindowsService": false}, "VideoDecoders": {}, "VideoEncoders": {"1: x264": {"SupportedPresets": ["superfast", "veryfast", "faster", "fast", "medium", "slow", "slower", "veryslow"], "MinConstantRateFactor": 0, "MaxConstantRateFactor": 51, "FrameworkCodec": "libx264", "IsHardwareCodec": false, "SupportsParameters": true, "CompatibleDecoderFrameworks": [], "VideoMediaType": "h264", "ProfilesWithLevel": [{"Profile": "AvcProfileBaseline", "Level": "AvcLevel62"}, {"Profile": "AvcProfileMain", "Level": "AvcLevel62"}, {"Profile": "AvcProfileHigh", "Level": "AvcLevel62"}, {"Profile": "AvcProfileHigh10", "Level": "AvcLevel62"}, {"Profile": "AvcProfileHigh422", "Level": "AvcLevel62"}, {"Profile": "AvcProfileHigh444", "Level": "AvcLevel62"}], "SupportedProfiles": ["AvcProfileBaseline", "AvcProfileMain", "AvcProfileHigh", "AvcProfileHigh10", "AvcProfileHigh422", "AvcProfileHigh444"], "SupportedLevels": ["AvcLevel1b", "AvcLevel1", "AvcLevel11", "AvcLevel12", "AvcLevel13", "AvcLevel2", "AvcLevel21", "AvcLevel22", "AvcLevel3", "AvcLevel31", "AvcLevel32", "AvcLevel4", "AvcLevel41", "AvcLevel42", "AvcLevel5", "AvcLevel51", "AvcLevel52", "AvcLevel6", "AvcLevel61", "AvcLevel62"], "Direction": "Encoder", "SupportedColorFormats": ["yuv420p", "yuvj420p", "yuv422p", "yuvj422p", "yuv444p", "yuvj444p", "nv12", "nv16", "nv21", "yuv420p10", "yuv422p10", "yuv444p10", "nv20", "gray", "gray10"], "CommandGenerator": {}, "CodecDeviceInfo": {"Capabilities": {"SupportsHwUpload": false, "SupportsHwDownload": false, "SupportsStandaloneDeviceInit": false, "Supports10BitProcessing": false, "SupportsNativeToneMapping": false}, "Adapter": 0, "Name": "Software Codec", "Desription": "Software Codec", "DeviceId": 0, "HardwareContextFramework": "None", "VendorId": 0}, "CodecKind": "Video", "MediaTypeName": "H.264 (AVC)", "MaxBitRate": "781 Mbit/s", "SupportedColorFormatStrings": ["YUV420P", "YUVJ420P", "YUV422P", "YUVJ422P", "YUV444P", "YUVJ444P", "NV12", "NV16", "NV21", "YUV420P10", "YUV422P10", "YUV444P10", "NV20", "GRAY8", "GRAY10"], "ProfileAndLevelInformation": [{"Profile": {"ShortName": "AvcProfileBaseline", "Description": "Baseline Profile", "Details": "Primarily for low-cost applications that require additional data loss robustness, this profile is used in some videoconferencing and mobile applications. This profile includes all features that are supported in the Constrained Baseline Profile, plus three additional features that can be used for loss robustness (or for other purposes such as low-delay multi-point video stream compositing). The importance of this profile has faded somewhat since the definition of the Constrained Baseline Profile in 2009. All Constrained Baseline Profile bitstreams are also considered to be Baseline Profile bitstreams, as these two profiles share the same profile identifier code value.", "Id": "AvcProfiles.AvcProfileBaseline", "BitDepths": [8]}, "Level": {"ShortName": "AvcLevel62", "Description": "Level 6.2", "Ordinal": 62, "MaxBitRate": "781 Mbit/s", "MaxBitRateDisplay": "781 Mbit/s", "Id": "AvcLevels.AvcLevel62", "ResolutionRates": ["8192x4320@120", "7680x4320@129", "3840x2160@300"], "ResolutionRateStrings": ["8192x4320@120", "7680x4320@129", "3840x2160@300"], "ResolutionRatesDisplay": "8192x4320@120, 7680x4320@129, 3840x2160@300"}}, {"Profile": {"ShortName": "AvcProfileMain", "Description": "Main Profile", "Details": "This profile is used for standard-definition digital TV broadcasts that use the MPEG-4 format as defined in the DVB standard. It is not, however, used for high-definition television broadcasts, as the importance of this profile faded when the High Profile was developed in 2004 for that application.", "Id": "AvcProfiles.AvcProfileMain", "BitDepths": [8]}, "Level": {"ShortName": "AvcLevel62", "Description": "Level 6.2", "Ordinal": 62, "MaxBitRate": "781 Mbit/s", "MaxBitRateDisplay": "781 Mbit/s", "Id": "AvcLevels.AvcLevel62", "ResolutionRates": ["8192x4320@120", "7680x4320@129", "3840x2160@300"], "ResolutionRateStrings": ["8192x4320@120", "7680x4320@129", "3840x2160@300"], "ResolutionRatesDisplay": "8192x4320@120, 7680x4320@129, 3840x2160@300"}}, {"Profile": {"ShortName": "AvcProfileHigh", "Description": "High Profile", "Details": "The primary profile for broadcast and disc storage applications, particularly for high-definition television applications (for example, this is the profile adopted by the Blu-ray Disc storage format and the DVB HDTV broadcast service).", "Id": "AvcProfiles.AvcProfileHigh", "BitDepths": [8]}, "Level": {"ShortName": "AvcLevel62", "Description": "Level 6.2", "Ordinal": 62, "MaxBitRate": "781 Mbit/s", "MaxBitRateDisplay": "781 Mbit/s", "Id": "AvcLevels.AvcLevel62", "ResolutionRates": ["8192x4320@120", "7680x4320@129", "3840x2160@300"], "ResolutionRateStrings": ["8192x4320@120", "7680x4320@129", "3840x2160@300"], "ResolutionRatesDisplay": "8192x4320@120, 7680x4320@129, 3840x2160@300"}}, {"Profile": {"ShortName": "AvcProfileHigh10", "Description": "High 10 Profile", "Details": "Going beyond typical mainstream consumer product capabilities, this profile builds on top of the High Profile, adding support for up to 10 bits per sample of decoded picture precision.", "Id": "AvcProfiles.AvcProfileHigh10", "BitDepths": [10]}, "Level": {"ShortName": "AvcLevel62", "Description": "Level 6.2", "Ordinal": 62, "MaxBitRate": "781 Mbit/s", "MaxBitRateDisplay": "781 Mbit/s", "Id": "AvcLevels.AvcLevel62", "ResolutionRates": ["8192x4320@120", "7680x4320@129", "3840x2160@300"], "ResolutionRateStrings": ["8192x4320@120", "7680x4320@129", "3840x2160@300"], "ResolutionRatesDisplay": "8192x4320@120, 7680x4320@129, 3840x2160@300"}}, {"Profile": {"ShortName": "AvcProfileHigh422", "Description": "High 4:2:2 Profile", "Details": "Primarily targeting professional applications that use interlaced video, this profile builds on top of the High 10 Profile, adding support for the 4:2:2 chroma subsampling format while using up to 10 bits per sample of decoded picture precision.", "Id": "AvcProfiles.AvcProfileHigh422", "BitDepths": [10]}, "Level": {"ShortName": "AvcLevel62", "Description": "Level 6.2", "Ordinal": 62, "MaxBitRate": "781 Mbit/s", "MaxBitRateDisplay": "781 Mbit/s", "Id": "AvcLevels.AvcLevel62", "ResolutionRates": ["8192x4320@120", "7680x4320@129", "3840x2160@300"], "ResolutionRateStrings": ["8192x4320@120", "7680x4320@129", "3840x2160@300"], "ResolutionRatesDisplay": "8192x4320@120, 7680x4320@129, 3840x2160@300"}}, {"Profile": {"ShortName": "AvcProfileHigh444", "Description": "High 4:4:4 Predictive Profile", "Details": "This profile builds on top of the High 4:2:2 Profile, supporting up to 4:4:4 chroma sampling, up to 14 bits per sample, and additionally supporting efficient lossless region coding and the coding of each picture as three separate color planes.", "Id": "AvcProfiles.AvcProfileHigh444", "BitDepths": [10, 12, 14]}, "Level": {"ShortName": "AvcLevel62", "Description": "Level 6.2", "Ordinal": 62, "MaxBitRate": "781 Mbit/s", "MaxBitRateDisplay": "781 Mbit/s", "Id": "AvcLevels.AvcLevel62", "ResolutionRates": ["8192x4320@120", "7680x4320@129", "3840x2160@300"], "ResolutionRateStrings": ["8192x4320@120", "7680x4320@129", "3840x2160@300"], "ResolutionRatesDisplay": "8192x4320@120, 7680x4320@129, 3840x2160@300"}}], "Id": "V-E-libx264", "Name": "x264", "SecondaryFramework": "None", "IsEnabledByDefault": false, "DefaultPriority": 0}, "2: x265": {"SupportedPresets": ["ultrafast", "superfast", "veryfast", "faster", "fast", "medium", "slow", "slower", "veryslow", "placebo"], "MinConstantRateFactor": 0, "MaxConstantRateFactor": 51, "FrameworkCodec": "libx265", "IsHardwareCodec": false, "SupportsParameters": true, "CompatibleDecoderFrameworks": [], "VideoMediaType": "hevc", "ProfilesWithLevel": [{"Profile": "HevcProfileMain", "Level": "HevcLevel62"}, {"Profile": "HevcProfileMain10", "Level": "HevcLevel62"}], "SupportedProfiles": ["HevcProfileMain", "HevcProfileMain10"], "SupportedLevels": ["HevcLevel1", "HevcLevel2", "HevcLevel21", "HevcLevel3", "HevcLevel31", "HevcLevel4", "HevcLevel41", "HevcLevel5", "HevcLevel51", "HevcLevel52", "HevcLevel6", "HevcLevel61", "HevcLevel62"], "Direction": "Encoder", "SupportedColorFormats": ["yuv420p", "yuvj420p", "yuv422p", "yuvj422p", "yuv444p", "yuvj444p", "gbrp", "yuv420p10", "yuv422p10", "yuv444p10", "gbrp10", "yuv420p12", "yuv422p12", "yuv444p12", "gbrp12", "gray", "gray10", "gray12"], "CommandGenerator": {}, "CodecDeviceInfo": {"Capabilities": {"SupportsHwUpload": false, "SupportsHwDownload": false, "SupportsStandaloneDeviceInit": false, "Supports10BitProcessing": false, "SupportsNativeToneMapping": false}, "Adapter": 0, "Name": "Software Codec", "Desription": "Software Codec", "DeviceId": 0, "HardwareContextFramework": "None", "VendorId": 0}, "CodecKind": "Video", "MediaTypeName": "H.265 (HEVC)", "MaxBitRate": "234 Mbit/s", "SupportedColorFormatStrings": ["YUV420P", "YUVJ420P", "YUV422P", "YUVJ422P", "YUV444P", "YUVJ444P", "GBRP", "YUV420P10", "YUV422P10", "YUV444P10", "GBRP10", "YUV420P12", "YUV422P12", "YUV444P12", "GBRP12", "GRAY8", "GRAY10", "GRAY12"], "ProfileAndLevelInformation": [{"Profile": {"ShortName": "HevcProfileMain", "Description": "Main Profile", "Details": "The Main profile allows for a bit depth of 8-bits per sample with 4:2:0 chroma sampling, which is the most common type of video used with consumer devices.", "Id": "HevcProfiles.HevcProfileMain", "BitDepths": [8]}, "Level": {"ShortName": "HevcLevel62", "Description": "Level 6.2", "Ordinal": 62, "MaxBitRate": "234 Mbit/s", "MaxBitRateDisplay": "234 Mbit/s", "Id": "HevcLevels.HevcLevel62", "ResolutionRates": ["8192x8192@60", "8192x4320@120", "7680x4320@128", "3840x2160@300"], "ResolutionRateStrings": ["8192x8192@60", "8192x4320@120", "7680x4320@128", "3840x2160@300"], "ResolutionRatesDisplay": "8192x8192@60, 8192x4320@120, 7680x4320@128, 3840x2160@300"}}, {"Profile": {"ShortName": "HevcProfileMain10", "Description": "Main 10 Profile", "Details": "The Main 10 profile allows for a bit depth of 8-bits to 10-bits per sample with 4:2:0 chroma sampling. allows for improved video quality since it can support video with a higher bit depth than what is supported by the Main profile.[140] Additionally, in the Main 10 profile 8-bit video can be coded with a higher bit depth of 10-bits, which allows improved coding efficiency compared to the Main profile.", "Id": "HevcProfiles.HevcProfileMain10", "BitDepths": [8, 10]}, "Level": {"ShortName": "HevcLevel62", "Description": "Level 6.2", "Ordinal": 62, "MaxBitRate": "234 Mbit/s", "MaxBitRateDisplay": "234 Mbit/s", "Id": "HevcLevels.HevcLevel62", "ResolutionRates": ["8192x8192@60", "8192x4320@120", "7680x4320@128", "3840x2160@300"], "ResolutionRateStrings": ["8192x8192@60", "8192x4320@120", "7680x4320@128", "3840x2160@300"], "ResolutionRatesDisplay": "8192x8192@60, 8192x4320@120, 7680x4320@128, 3840x2160@300"}}], "Id": "V-E-libx265", "Name": "x265", "SecondaryFramework": "None", "IsEnabledByDefault": false, "DefaultPriority": 0}, "3: MPEG-4 part 2": {"FrameworkCodec": "mpeg4", "IsHardwareCodec": false, "SupportsParameters": false, "CompatibleDecoderFrameworks": [], "VideoMediaType": "mpeg4", "ProfilesWithLevel": [], "SupportedProfiles": [], "SupportedLevels": [], "Direction": "Encoder", "SupportedColorFormats": [], "CommandGenerator": {}, "CodecDeviceInfo": {"Capabilities": {"SupportsHwUpload": false, "SupportsHwDownload": false, "SupportsStandaloneDeviceInit": false, "Supports10BitProcessing": false, "SupportsNativeToneMapping": false}, "Adapter": 0, "Name": "Software Codec", "Desription": "Software Codec", "DeviceId": 0, "HardwareContextFramework": "None", "VendorId": 0}, "CodecKind": "Video", "MediaTypeName": "MPEG-4", "SupportedColorFormatStrings": [], "ProfileAndLevelInformation": [], "Id": "V-E-mpeg4", "Name": "MPEG-4 part 2", "SecondaryFramework": "None", "IsEnabledByDefault": false, "DefaultPriority": 0}, "4: MPEG-4 part 2 (MS Variant 3)": {"VideoMediaType": "msmpeg4v3", "FrameworkCodec": "msmpeg4", "SupportsParameters": false, "IsHardwareCodec": false, "CompatibleDecoderFrameworks": [], "ProfilesWithLevel": [], "SupportedProfiles": [], "SupportedLevels": [], "Direction": "Encoder", "SupportedColorFormats": [], "CommandGenerator": {}, "CodecDeviceInfo": {"Capabilities": {"SupportsHwUpload": false, "SupportsHwDownload": false, "SupportsStandaloneDeviceInit": false, "Supports10BitProcessing": false, "SupportsNativeToneMapping": false}, "Adapter": 0, "Name": "Software Codec", "Desription": "Software Codec", "DeviceId": 0, "HardwareContextFramework": "None", "VendorId": 0}, "CodecKind": "Video", "MediaTypeName": "MPEG-4 part 2 Microsoft variant version 3", "SupportedColorFormatStrings": [], "ProfileAndLevelInformation": [], "Id": "V-E-msmpeg4", "Name": "MPEG-4 part 2 (MS Variant 3)", "SecondaryFramework": "None", "IsEnabledByDefault": false, "DefaultPriority": 0}, "5: libvpx VP8": {"MinConstantRateFactor": 4, "MaxConstantRateFactor": 63, "FrameworkCodec": "libvpx", "IsHardwareCodec": false, "SupportsParameters": false, "CompatibleDecoderFrameworks": [], "VideoMediaType": "vp8", "ProfilesWithLevel": [{"Profile": "Vp8Profile0", "Level": "Vp8levelDefault"}], "SupportedProfiles": ["Vp8Profile0"], "SupportedLevels": ["Vp8levelDefault"], "Direction": "Encoder", "SupportedColorFormats": [], "CommandGenerator": {}, "CodecDeviceInfo": {"Capabilities": {"SupportsHwUpload": false, "SupportsHwDownload": false, "SupportsStandaloneDeviceInit": false, "Supports10BitProcessing": false, "SupportsNativeToneMapping": false}, "Adapter": 0, "Name": "Software Codec", "Desription": "Software Codec", "DeviceId": 0, "HardwareContextFramework": "None", "VendorId": 0}, "CodecKind": "Video", "MediaTypeName": "VP8", "SupportedColorFormatStrings": [], "ProfileAndLevelInformation": [{"Profile": {"ShortName": "Vp8Profile0", "Description": "Profile 0", "Details": "For non-zero values the encoder increasingly optimizes for reduced complexity playback on low powered devices at the expense of encode quality. For example using 1 tells the encoder only to use only bi-linear sub pixel filtering and a simplified loop filter. In general most users will want to set a value of 0 or ignore this parameter unless they are encoding high resolution content and require playback on very low power devices.", "Id": "Vp8Profiles.Vp8Profile0", "BitDepths": [8]}, "Level": {"ShortName": "Vp8levelDefault", "Description": "Default Level", "Ordinal": 0, "Id": "Vp8Levels.Vp8levelDefault", "ResolutionRates": [], "ResolutionRateStrings": [], "ResolutionRatesDisplay": ""}}], "Id": "V-E-libvpx", "Name": "libvpx VP8", "SecondaryFramework": "None", "IsEnabledByDefault": false, "DefaultPriority": 0}}, "EncodingOptions": {"EncodingThreadCount": -1, "ExtractionThreadCount": 1, "DownMixAudioBoost": 2, "EnableThrottling": false, "ThrottleBufferSize": 120, "ThrottleHysteresis": 8, "ThrottlingMethod": "BySegmentRequest", "H264Crf": 23, "EnableHardwareEncoding": true, "EnableSubtitleExtraction": true, "CodecConfigurations": [], "HardwareAccelerationMode": 1, "EnableHardwareToneMapping": false, "EnableSoftwareToneMapping": false}, "DetectionOutputs": {"Vaapi": {"CodecProviderName": "<PERSON><PERSON><PERSON>", "StandardError": "ffdetect version 5.1-emby_2023_06_25 Copyright (c) 2018-2022 softworkz for Emby LLC\n  built with gcc 10.3.0 (crosstool-NG 1.25.0)\n  configuration: --cc=x86_64-emby-linux-gnu-gcc --prefix=/home/<USER>/Buildbot/x64/ffmpeg-x64/staging --disable-alsa --disable-doc --disable-ffplay --disable-gnutls --disable-libpulse --disable-librtmp --disable-libxcb --disable-openssl --disable-vdpau --disable-vulkan --disable-xlib --enable-chromaprint --enable-fontconfig --enable-gpl --enable-iconv --enable-libaribb24 --enable-libass --enable-libdav1d --enable-libfreetype --enable-libfribidi --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libvpx --enable-libwebp --enable-libx264 --enable-libx265 --enable-libzvbi --enable-mbedtls --enable-pic --enable-version3 --enable-libtesseract --enable-cuda-llvm --enable-cuvid --enable-libdrm --enable-libmfx --enable-nvdec --enable-nvenc --enable-vaapi --enable-opencl --enable-cross-compile --cross-prefix=x86_64-emby-linux-gnu- --arch=x86_64 --target-os=linux --enable-shared --disable-static --pkg-config=pkg-config --pkg-config-flags=--static --extra-libs='-lm -lstdc++ -lsharpyuv -pthread' --disable-debug\n  libavutil      57. 28.100 / 57. 28.100\nBegin GetVaAdapterInfo\nFound 3 device entries\nBegin get_nodes\nFound -1 drm entries\nEnd GetVaAdapterInfo\n\n", "Result": {"ProgramVersion": {"Version": "5.1-emby_2023_06_25", "Copyright": "Copyright (c) 2018-2022 softworkz for Emby Llc", "Compiler": "gcc 10.3.0 (crosstool-NG 1.25.0)", "Configuration": "--cc=x86_64-emby-linux-gnu-gcc --prefix=/home/<USER>/Buildbot/x64/ffmpeg-x64/staging --disable-alsa --disable-doc --disable-ffplay --disable-gnutls --disable-libpulse --disable-librtmp --disable-libxcb --disable-openssl --disable-vdpau --disable-vulkan --disable-xlib --enable-chromaprint --enable-fontconfig --enable-gpl --enable-iconv --enable-libaribb24 --enable-libass --enable-libdav1d --enable-libfreetype --enable-libfribidi --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libvpx --enable-libwebp --enable-libx264 --enable-libx265 --enable-libzvbi --enable-mbedtls --enable-pic --enable-version3 --enable-libtesseract --enable-cuda-llvm --enable-cuvid --enable-libdrm --enable-libmfx --enable-nvdec --enable-nvenc --enable-vaapi --enable-opencl --enable-cross-compile --cross-prefix=x86_64-emby-linux-gnu- --arch=x86_64 --target-os=linux --enable-shared --disable-static --pkg-config=pkg-config --pkg-config-flags=--static --extra-libs='-lm -lstdc++ -lsharpyuv -pthread' --disable-debug"}, "Devices": [{"DeviceIndex": 0, "DeviceInfo": {"VendorName": "Microsoft Corporation", "DeviceName": "Basic Render Driver", "VendorId": 5140, "DeviceId": 142, "SubsytemVendorId": 0, "SubsytemDeviceId": 0, "DevPath": "/sys/bus/pci/devices/cfcd:00:00.0", "IsEnabled": 0, "IsBootVga": 0, "Error": {"Number": -1, "Message": "Failed to open the drm device (null)"}}}], "Log": [{"Level": 40, "Category": 0, "Message": "Found 3 device entries"}]}, "ExitCode": 0}, "QuickSyncCodecProvider": {"CodecProviderName": "QuickSyncCodecProvider", "StandardError": "ffdetect version 5.1-emby_2023_06_25 Copyright (c) 2018-2022 softworkz for Emby LLC\n  built with gcc 10.3.0 (crosstool-NG 1.25.0)\n  configuration: --cc=x86_64-emby-linux-gnu-gcc --prefix=/home/<USER>/Buildbot/x64/ffmpeg-x64/staging --disable-alsa --disable-doc --disable-ffplay --disable-gnutls --disable-libpulse --disable-librtmp --disable-libxcb --disable-openssl --disable-vdpau --disable-vulkan --disable-xlib --enable-chromaprint --enable-fontconfig --enable-gpl --enable-iconv --enable-libaribb24 --enable-libass --enable-libdav1d --enable-libfreetype --enable-libfribidi --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libvpx --enable-libwebp --enable-libx264 --enable-libx265 --enable-libzvbi --enable-mbedtls --enable-pic --enable-version3 --enable-libtesseract --enable-cuda-llvm --enable-cuvid --enable-libdrm --enable-libmfx --enable-nvdec --enable-nvenc --enable-vaapi --enable-opencl --enable-cross-compile --cross-prefix=x86_64-emby-linux-gnu- --arch=x86_64 --target-os=linux --enable-shared --disable-static --pkg-config=pkg-config --pkg-config-flags=--static --extra-libs='-lm -lstdc++ -lsharpyuv -pthread' --disable-debug\n  libavutil      57. 28.100 / 57. 28.100\nBegin GetVaAdapterInfo\nFound 3 device entries\nBegin get_nodes\nFound -1 drm entries\nEnd GetVaAdapterInfo\nBegin GetVaAdapterInfo\nFound 3 device entries\nBegin get_nodes\nFound -1 drm entries\nEnd GetVaAdapterInfo\nBegin GetVaAdapterInfo\nFound 3 device entries\nBegin get_nodes\nFound -1 drm entries\nEnd GetVaAdapterInfo\nBegin GetVaAdapterInfo\nFound 3 device entries\nBegin get_nodes\nFound -1 drm entries\nEnd GetVaAdapterInfo\n\n", "Result": {"ProgramVersion": {"Version": "5.1-emby_2023_06_25", "Copyright": "Copyright (c) 2018-2022 softworkz for Emby Llc", "Compiler": "gcc 10.3.0 (crosstool-NG 1.25.0)", "Configuration": "--cc=x86_64-emby-linux-gnu-gcc --prefix=/home/<USER>/Buildbot/x64/ffmpeg-x64/staging --disable-alsa --disable-doc --disable-ffplay --disable-gnutls --disable-libpulse --disable-librtmp --disable-libxcb --disable-openssl --disable-vdpau --disable-vulkan --disable-xlib --enable-chromaprint --enable-fontconfig --enable-gpl --enable-iconv --enable-libaribb24 --enable-libass --enable-libdav1d --enable-libfreetype --enable-libfribidi --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libvpx --enable-libwebp --enable-libx264 --enable-libx265 --enable-libzvbi --enable-mbedtls --enable-pic --enable-version3 --enable-libtesseract --enable-cuda-llvm --enable-cuvid --enable-libdrm --enable-libmfx --enable-nvdec --enable-nvenc --enable-vaapi --enable-opencl --enable-cross-compile --cross-prefix=x86_64-emby-linux-gnu- --arch=x86_64 --target-os=linux --enable-shared --disable-static --pkg-config=pkg-config --pkg-config-flags=--static --extra-libs='-lm -lstdc++ -lsharpyuv -pthread' --disable-debug"}, "Devices": [{"DeviceIndex": 0, "DeviceInfo": {"VendorName": "Microsoft Corporation", "DeviceName": "Basic Render Driver", "VendorId": 5140, "DeviceId": 142, "SubsytemVendorId": 0, "SubsytemDeviceId": 0, "DevPath": "/sys/bus/pci/devices/cfcd:00:00.0", "IsEnabled": 0, "IsBootVga": 0}, "Error": {"Number": -1, "Message": "Failed to open the drm device (null)"}}, {"DeviceIndex": 1, "DeviceInfo": {}, "Error": {"Number": -1, "Message": "No VA device found at index 1"}}, {"DeviceIndex": 2, "DeviceInfo": {}, "Error": {"Number": -1, "Message": "No VA device found at index 2"}}, {"DeviceIndex": 3, "DeviceInfo": {}, "Error": {"Number": -1, "Message": "No VA device found at index 3"}}], "Log": [{"Level": 40, "Category": 0, "Message": "Found 3 device entries"}, {"Level": 40, "Category": 0, "Message": "Found 3 device entries"}, {"Level": 40, "Category": 0, "Message": "Found 3 device entries"}, {"Level": 40, "Category": 0, "Message": "Found 3 device entries"}]}, "ExitCode": 0}, "NvidiaCodecProvider": {"CodecProviderName": "NvidiaCodecProvider", "StandardError": "ffdetect version 5.1-emby_2023_06_25 Copyright (c) 2018-2022 softworkz for Emby LLC\n  built with gcc 10.3.0 (crosstool-NG 1.25.0)\n  configuration: --cc=x86_64-emby-linux-gnu-gcc --prefix=/home/<USER>/Buildbot/x64/ffmpeg-x64/staging --disable-alsa --disable-doc --disable-ffplay --disable-gnutls --disable-libpulse --disable-librtmp --disable-libxcb --disable-openssl --disable-vdpau --disable-vulkan --disable-xlib --enable-chromaprint --enable-fontconfig --enable-gpl --enable-iconv --enable-libaribb24 --enable-libass --enable-libdav1d --enable-libfreetype --enable-libfribidi --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libvpx --enable-libwebp --enable-libx264 --enable-libx265 --enable-libzvbi --enable-mbedtls --enable-pic --enable-version3 --enable-libtesseract --enable-cuda-llvm --enable-cuvid --enable-libdrm --enable-libmfx --enable-nvdec --enable-nvenc --enable-vaapi --enable-opencl --enable-cross-compile --cross-prefix=x86_64-emby-linux-gnu- --arch=x86_64 --target-os=linux --enable-shared --disable-static --pkg-config=pkg-config --pkg-config-flags=--static --extra-libs='-lm -lstdc++ -lsharpyuv -pthread' --disable-debug\n  libavutil      57. 28.100 / 57. 28.100\nCannot load libcuda.so.1\nError loading CUDA functions\n\n", "Result": {"ProgramVersion": {"Version": "5.1-emby_2023_06_25", "Copyright": "Copyright (c) 2018-2022 softworkz for Emby Llc", "Compiler": "gcc 10.3.0 (crosstool-NG 1.25.0)", "Configuration": "--cc=x86_64-emby-linux-gnu-gcc --prefix=/home/<USER>/Buildbot/x64/ffmpeg-x64/staging --disable-alsa --disable-doc --disable-ffplay --disable-gnutls --disable-libpulse --disable-librtmp --disable-libxcb --disable-openssl --disable-vdpau --disable-vulkan --disable-xlib --enable-chromaprint --enable-fontconfig --enable-gpl --enable-iconv --enable-libaribb24 --enable-libass --enable-libdav1d --enable-libfreetype --enable-libfribidi --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libvpx --enable-libwebp --enable-libx264 --enable-libx265 --enable-libzvbi --enable-mbedtls --enable-pic --enable-version3 --enable-libtesseract --enable-cuda-llvm --enable-cuvid --enable-libdrm --enable-libmfx --enable-nvdec --enable-nvenc --enable-vaapi --enable-opencl --enable-cross-compile --cross-prefix=x86_64-emby-linux-gnu- --arch=x86_64 --target-os=linux --enable-shared --disable-static --pkg-config=pkg-config --pkg-config-flags=--static --extra-libs='-lm -lstdc++ -lsharpyuv -pthread' --disable-debug"}, "Error": {"Number": -1, "Message": "Operation not permitted"}, "Log": [{"Level": 16, "Category": 0, "Message": "Cannot load libcuda.so.1"}, {"Level": 16, "Category": 0, "Message": "Error loading CUDA functions"}]}, "ExitCode": 1}}, "FfmpegCapabilities": {"Version": "5.1-emby_2023_06_25", "FullVersionInfo": "ffmpeg version 5.1-emby_2023_06_25 Copyright (c) 2000-2022 the FFmpeg developers and softworkz for Emby LLC\nbuilt with gcc 10.3.0 (crosstool-NG 1.25.0)\nconfiguration: --cc=x86_64-emby-linux-gnu-gcc --prefix=/home/<USER>/Buildbot/x64/ffmpeg-x64/staging --disable-alsa --disable-doc --disable-ffplay --disable-gnutls --disable-libpulse --disable-librtmp --disable-libxcb --disable-openssl --disable-vdpau --disable-vulkan --disable-xlib --enable-chromaprint --enable-fontconfig --enable-gpl --enable-iconv --enable-libaribb24 --enable-libass --enable-libdav1d --enable-libfreetype --enable-libfribidi --enable-libmp3lame --enable-libopus --enable-libtheora --enable-libvorbis --enable-libvpx --enable-libwebp --enable-libx264 --enable-libx265 --enable-libzvbi --enable-mbedtls --enable-pic --enable-version3 --enable-libtesseract --enable-cuda-llvm --enable-cuvid --enable-libdrm --enable-libmfx --enable-nvdec --enable-nvenc --enable-vaapi --enable-opencl --enable-cross-compile --cross-prefix=x86_64-emby-linux-gnu- --arch=x86_64 --target-os=linux --enable-shared --disable-static --pkg-config=pkg-config --pkg-config-flags=--static --extra-libs='-lm -lstdc++ -lsharpyuv -pthread' --disable-debug\nlibavutil      57. 28.100 / 57. 28.100\nlibavcodec     59. 37.100 / 59. 37.100\nlibavformat    59. 27.100 / 59. 27.100\nlibavdevice    59.  7.100 / 59.  7.100\nlibavfilter     8. 44.100 /  8. 44.100\nlibswscale      6.  7.100 /  6.  7.100\nlibswresample   4.  7.100 /  4.  7.100\nlibpostproc    56.  6.100 / 56.  6.100\nEXIT\n\n", "IsEmbyCustom": true, "SupportsSkipInterval": true, "SupportsSkipList": true, "SupportsTempSegmentWriting": true, "SupportsLogTimeStamps": true, "SupportsWebVttReferenceStream": true, "SupportsFilterGraphReport": true, "SupportsSegmentMinFrame": true, "PositionTimeStartsFromZero": true, "SupportsChromaprint": true, "VideoDecoders": {"012v": {"Key": "012v", "Decription": "Uncompressed 4:2:2 10-bit", "Extra": "V....D"}, "4xm": {"Key": "4xm", "Decription": "4X Movie", "Extra": "V....D"}, "8bps": {"Key": "8bps", "Decription": "QuickTime 8BPS video", "Extra": "V....D"}, "aasc": {"Key": "aasc", "Decription": "Autodesk RLE", "Extra": "V....D"}, "agm": {"Key": "agm", "Decription": "Amuse Graphics Movie", "Extra": "V....D"}, "aic": {"Key": "aic", "Decription": "Apple Intermediate Codec", "Extra": "VF...D"}, "alias_pix": {"Key": "alias_pix", "Decription": "Alias/Wavefront PIX image", "Extra": "V....D"}, "amv": {"Key": "amv", "Decription": "AMV Video", "Extra": "V....D"}, "anm": {"Key": "anm", "Decription": "Deluxe Paint Animation", "Extra": "V....D"}, "ansi": {"Key": "ansi", "Decription": "ASCII/ANSI art", "Extra": "V....D"}, "apng": {"Key": "apng", "Decription": "APNG (Animated Portable Network Graphics) image", "Extra": "VF...D"}, "arbc": {"Key": "arbc", "Decription": "Gryphon's <PERSON><PERSON>", "Extra": "V....D"}, "argo": {"Key": "argo", "Decription": "Argonaut Games Video", "Extra": "V....D"}, "asv1": {"Key": "asv1", "Decription": "ASUS V1", "Extra": "V....D"}, "asv2": {"Key": "asv2", "Decription": "ASUS V2", "Extra": "V....D"}, "aura": {"Key": "aura", "Decription": "Auravision AURA", "Extra": "V....D"}, "aura2": {"Key": "aura2", "Decription": "Auravision Aura 2", "Extra": "V....D"}, "libdav1d": {"Key": "libdav1d", "Decription": "dav1d AV1 decoder by VideoLAN (codec av1)", "Extra": "V....."}, "av1": {"Key": "av1", "Decription": "Alliance for Open Media AV1", "Extra": "V....D"}, "av1_cuvid": {"Key": "av1_cuvid", "Decription": "Nvidia CUVID AV1 decoder (codec av1)", "Extra": "V....."}, "av1_qsv": {"Key": "av1_qsv", "Decription": "AV1 video (Intel Quick Sync Video acceleration) (codec av1)", "Extra": "V....D"}, "avrn": {"Key": "avrn", "Decription": "Avid AVI Codec", "Extra": "V....D"}, "avrp": {"Key": "avrp", "Decription": "Avid 1:1 10-bit RGB Packer", "Extra": "V....D"}, "avs": {"Key": "avs", "Decription": "AVS (Audio Video Standard) video", "Extra": "V....D"}, "avui": {"Key": "avui", "Decription": "<PERSON><PERSON>", "Extra": "V....D"}, "ayuv": {"Key": "ayuv", "Decription": "Uncompressed packed MS 4:4:4:4", "Extra": "V....D"}, "bethsoftvid": {"Key": "beths<PERSON>vid", "Decription": "Bethesda VID video", "Extra": "V....D"}, "bfi": {"Key": "bfi", "Decription": "Brute Force & Ignorance", "Extra": "V....D"}, "binkvideo": {"Key": "binkvideo", "Decription": "Bink video", "Extra": "V....D"}, "bintext": {"Key": "bintext", "Decription": "Binary text", "Extra": "V....D"}, "bitpacked": {"Key": "bitpacked", "Decription": "Bitpacked", "Extra": "VF...."}, "bmp": {"Key": "bmp", "Decription": "BMP (Windows and OS/2 bitmap)", "Extra": "V....D"}, "bmv_video": {"Key": "bmv_video", "Decription": "Discworld II BMV video", "Extra": "V....D"}, "brender_pix": {"Key": "brender_pix", "Decription": "BRender PIX image", "Extra": "V....D"}, "c93": {"Key": "c93", "Decription": "Interplay C93", "Extra": "V....D"}, "cavs": {"Key": "cavs", "Decription": "Chinese AVS (Audio Video Standard) (AVS1-P2, <PERSON><PERSON><PERSON> profile)", "Extra": "V....D"}, "cdgraphics": {"Key": "cdgraphics", "Decription": "CD Graphics video", "Extra": "V....D"}, "cdtoons": {"Key": "cdtoons", "Decription": "CDToons video", "Extra": "V....D"}, "cdxl": {"Key": "cdxl", "Decription": "Commodore CDXL video", "Extra": "V....D"}, "cfhd": {"Key": "cfhd", "Decription": "GoPro CineForm HD", "Extra": "VF...D"}, "cinepak": {"Key": "cinepak", "Decription": "Cinepak", "Extra": "V....D"}, "clearvideo": {"Key": "clearvideo", "Decription": "Iterated Systems ClearVideo", "Extra": "V....D"}, "cljr": {"Key": "cljr", "Decription": "Cirrus Logic AccuPak", "Extra": "V....D"}, "cllc": {"Key": "cllc", "Decription": "Canopus Lossless Codec", "Extra": "VF...D"}, "eacmv": {"Key": "eacmv", "Decription": "Electronic Arts CMV video (codec cmv)", "Extra": "V....D"}, "cpia": {"Key": "cpia", "Decription": "CPiA video format", "Extra": "V....D"}, "cri": {"Key": "cri", "Decription": "Cintel RAW", "Extra": "VF...D"}, "camstudio": {"Key": "camstudio", "Decription": "CamStudio (codec cscd)", "Extra": "V....D"}, "cyuv": {"Key": "cyuv", "Decription": "Creative YUV (CYUV)", "Extra": "V....D"}, "dds": {"Key": "dds", "Decription": "DirectDraw Surface image decoder", "Extra": "V.S..D"}, "dfa": {"Key": "dfa", "Decription": "Chronomaster DFA", "Extra": "V....D"}, "dirac": {"Key": "dirac", "Decription": "BBC Dirac VC-2", "Extra": "V.S..D"}, "dnxhd": {"Key": "dnxhd", "Decription": "VC3/DNxHD", "Extra": "VFS..D"}, "dpx": {"Key": "dpx", "Decription": "DPX (Digital Picture Exchange) image", "Extra": "V....D"}, "dsicinvideo": {"Key": "dsicinvideo", "Decription": "Delphine Software International CIN video", "Extra": "V....D"}, "dvvideo": {"Key": "dvvideo", "Decription": "DV (Digital Video)", "Extra": "VFS..D"}, "dxa": {"Key": "dxa", "Decription": "Feeble Files/ScummVM DXA", "Extra": "V....D"}, "dxtory": {"Key": "dxtory", "Decription": "Dxtory", "Extra": "VF...D"}, "dxv": {"Key": "dxv", "Decription": "Resolume DXV", "Extra": "VFS..D"}, "escape124": {"Key": "escape124", "Decription": "Escape 124", "Extra": "V....D"}, "escape130": {"Key": "escape130", "Decription": "Escape 130", "Extra": "V....D"}, "exr": {"Key": "exr", "Decription": "OpenEXR image", "Extra": "VFS..D"}, "ffv1": {"Key": "ffv1", "Decription": "FFmpeg video codec #1", "Extra": "VFS..D"}, "ffvhuff": {"Key": "ffvhuff", "Decription": "Huffyuv FFmpeg variant", "Extra": "VF..BD"}, "fic": {"Key": "fic", "Decription": "<PERSON><PERSON>is <PERSON>", "Extra": "V.S..D"}, "fits": {"Key": "fits", "Decription": "Flexible Image Transport System", "Extra": "V....D"}, "flashsv": {"Key": "flashsv", "Decription": "Flash Screen Video v1", "Extra": "V....D"}, "flashsv2": {"Key": "flashsv2", "Decription": "Flash Screen Video v2", "Extra": "V....D"}, "flic": {"Key": "flic", "Decription": "Autodesk Animator Flic video", "Extra": "V....D"}, "flv": {"Key": "flv", "Decription": "FLV / Sorenson Spark / Sorenson H.263 (Flash Video) (codec flv1)", "Extra": "V...BD"}, "fmvc": {"Key": "fmvc", "Decription": "FM Screen Capture Codec", "Extra": "V....D"}, "fraps": {"Key": "fraps", "Decription": "<PERSON><PERSON><PERSON>", "Extra": "VF...D"}, "frwu": {"Key": "frwu", "Decription": "Forward Uncompressed", "Extra": "V....D"}, "g2m": {"Key": "g2m", "Decription": "Go2Meeting", "Extra": "V....D"}, "gdv": {"Key": "gdv", "Decription": "Gremlin Digital Video", "Extra": "V....D"}, "gem": {"Key": "gem", "Decription": "GEM Raster image", "Extra": "V....D"}, "gif": {"Key": "gif", "Decription": "GIF (Graphics Interchange Format)", "Extra": "V....D"}, "h261": {"Key": "h261", "Decription": "H.261", "Extra": "V....D"}, "h263": {"Key": "h263", "Decription": "H.263 / H.263-1996, H.263+ / H.263-1998 / H.263 version 2", "Extra": "V...BD"}, "h263_v4l2m2m": {"Key": "h263_v4l2m2m", "Decription": "V4L2 mem2mem H.263 decoder wrapper (codec h263)", "Extra": "V....."}, "h263i": {"Key": "h263i", "Decription": "Intel H.263", "Extra": "V...BD"}, "h263p": {"Key": "h263p", "Decription": "H.263 / H.263-1996, H.263+ / H.263-1998 / H.263 version 2", "Extra": "V...BD"}, "h264": {"Key": "h264", "Decription": "H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10", "Extra": "VFS..D"}, "h264_v4l2m2m": {"Key": "h264_v4l2m2m", "Decription": "V4L2 mem2mem H.264 decoder wrapper (codec h264)", "Extra": "V....."}, "h264_qsv": {"Key": "h264_qsv", "Decription": "H264 video (Intel Quick Sync Video acceleration) (codec h264)", "Extra": "V....D"}, "h264_cuvid": {"Key": "h264_cuvid", "Decription": "Nvidia CUVID H264 decoder (codec h264)", "Extra": "V....."}, "hap": {"Key": "hap", "Decription": "Vidvox Hap", "Extra": "VFS..D"}, "hevc": {"Key": "hevc", "Decription": "HEVC (High Efficiency Video Coding)", "Extra": "VFS..D"}, "hevc_qsv": {"Key": "hevc_qsv", "Decription": "HEVC video (Intel Quick Sync Video acceleration) (codec hevc)", "Extra": "V....D"}, "hevc_v4l2m2m": {"Key": "hevc_v4l2m2m", "Decription": "V4L2 mem2mem HEVC decoder wrapper (codec hevc)", "Extra": "V....."}, "hevc_cuvid": {"Key": "hevc_cuvid", "Decription": "Nvidia CUVID HEVC decoder (codec hevc)", "Extra": "V....."}, "hnm4video": {"Key": "hnm4video", "Decription": "HNM 4 video", "Extra": "V....D"}, "hq_hqa": {"Key": "hq_hqa", "Decription": "Canopus HQ/HQA", "Extra": "V....D"}, "hqx": {"Key": "hqx", "Decription": "Canopus HQX", "Extra": "VFS..D"}, "huffyuv": {"Key": "<PERSON><PERSON><PERSON><PERSON>", "Decription": "Huffyuv / HuffYUV", "Extra": "VF..BD"}, "hymt": {"Key": "hymt", "Decription": "HuffYUV MT", "Extra": "VF..BD"}, "idcinvideo": {"Key": "idcinvideo", "Decription": "id Quake II CIN video (codec idcin)", "Extra": "V....D"}, "idf": {"Key": "idf", "Decription": "iCEDraw text", "Extra": "V....D"}, "iff": {"Key": "iff", "Decription": "IFF ACBM/ANIM/DEEP/ILBM/PBM/RGB8/RGBN (codec iff_ilbm)", "Extra": "V....D"}, "imm4": {"Key": "imm4", "Decription": "Infinity IMM4", "Extra": "V....D"}, "imm5": {"Key": "imm5", "Decription": "Infinity IMM5", "Extra": "V....."}, "indeo2": {"Key": "indeo2", "Decription": "Intel Indeo 2", "Extra": "V....D"}, "indeo3": {"Key": "indeo3", "Decription": "Intel Indeo 3", "Extra": "V....D"}, "indeo4": {"Key": "indeo4", "Decription": "Intel Indeo Video Interactive 4", "Extra": "V....D"}, "indeo5": {"Key": "indeo5", "Decription": "Intel Indeo Video Interactive 5", "Extra": "V....D"}, "interplayvideo": {"Key": "interplayvideo", "Decription": "Interplay MVE video", "Extra": "V....D"}, "ipu": {"Key": "ipu", "Decription": "IPU Video", "Extra": "V....D"}, "jpeg2000": {"Key": "jpeg2000", "Decription": "JPEG 2000", "Extra": "VFS..D"}, "jpegls": {"Key": "jpegls", "Decription": "JPEG-LS", "Extra": "V....D"}, "jv": {"Key": "jv", "Decription": "Bitmap Brothers JV video", "Extra": "V....D"}, "kgv1": {"Key": "kgv1", "Decription": "Kega Game Video", "Extra": "V....D"}, "kmvc": {"Key": "kmvc", "Decription": "<PERSON>'s video codec", "Extra": "V....D"}, "lagarith": {"Key": "la<PERSON><PERSON>", "Decription": "Lagarith lossless", "Extra": "VF...D"}, "loco": {"Key": "loco", "Decription": "LOCO", "Extra": "V....D"}, "lscr": {"Key": "lscr", "Decription": "LEAD Screen Capture", "Extra": "V....D"}, "m101": {"Key": "m101", "Decription": "Matrox Uncompressed SD", "Extra": "V....D"}, "eamad": {"Key": "<PERSON><PERSON><PERSON>", "Decription": "Electronic Arts Madcow Video (codec mad)", "Extra": "V....D"}, "magicyuv": {"Key": "magicyuv", "Decription": "MagicYUV video", "Extra": "VFS..D"}, "mdec": {"Key": "mdec", "Decription": "Sony PlayStation MDEC (Motion DECoder)", "Extra": "VF...D"}, "mimic": {"Key": "mimic", "Decription": "Mimic", "Extra": "VF...D"}, "mjpeg": {"Key": "mjpeg", "Decription": "MJPEG (Motion JPEG)", "Extra": "V....D"}, "mjpeg_cuvid": {"Key": "mjpeg_cuvid", "Decription": "Nvidia CUVID MJPEG decoder (codec mjpeg)", "Extra": "V....."}, "mjpeg_qsv": {"Key": "mjpeg_qsv", "Decription": "MJPEG video (Intel Quick Sync Video acceleration) (codec mjpeg)", "Extra": "V....D"}, "mjpegb": {"Key": "mjpegb", "Decription": "Apple MJPEG-B", "Extra": "V....D"}, "mmvideo": {"Key": "mmvideo", "Decription": "American Laser Games MM Video", "Extra": "V....D"}, "mobiclip": {"Key": "mobiclip", "Decription": "MobiClip Video", "Extra": "V....D"}, "motionpixels": {"Key": "motionpixels", "Decription": "Motion Pixels video", "Extra": "V....D"}, "mpeg1video": {"Key": "mpeg1video", "Decription": "MPEG-1 video", "Extra": "V.S.BD"}, "mpeg1_v4l2m2m": {"Key": "mpeg1_v4l2m2m", "Decription": "V4L2 mem2mem MPEG1 decoder wrapper (codec mpeg1video)", "Extra": "V....."}, "mpeg1_cuvid": {"Key": "mpeg1_cuvid", "Decription": "Nvidia CUVID MPEG1VIDEO decoder (codec mpeg1video)", "Extra": "V....."}, "mpeg2video": {"Key": "mpeg2video", "Decription": "MPEG-2 video", "Extra": "V.S.BD"}, "mpegvideo": {"Key": "mpegvideo", "Decription": "MPEG-1 video (codec mpeg2video)", "Extra": "V.S.BD"}, "mpeg2_v4l2m2m": {"Key": "mpeg2_v4l2m2m", "Decription": "V4L2 mem2mem MPEG2 decoder wrapper (codec mpeg2video)", "Extra": "V....."}, "mpeg2_qsv": {"Key": "mpeg2_qsv", "Decription": "MPEG2VIDEO video (Intel Quick Sync Video acceleration) (codec mpeg2video)", "Extra": "V....D"}, "mpeg2_cuvid": {"Key": "mpeg2_cuvid", "Decription": "Nvidia CUVID MPEG2VIDEO decoder (codec mpeg2video)", "Extra": "V....."}, "mpeg4": {"Key": "mpeg4", "Decription": "MPEG-4 part 2", "Extra": "VF..BD"}, "mpeg4_v4l2m2m": {"Key": "mpeg4_v4l2m2m", "Decription": "V4L2 mem2mem MPEG4 decoder wrapper (codec mpeg4)", "Extra": "V....."}, "mpeg4_cuvid": {"Key": "mpeg4_cuvid", "Decription": "Nvidia CUVID MPEG4 decoder (codec mpeg4)", "Extra": "V....."}, "msa1": {"Key": "msa1", "Decription": "MS ATC Screen", "Extra": "V....D"}, "mscc": {"Key": "mscc", "Decription": "Mandsoft Screen Capture Codec", "Extra": "V....D"}, "msmpeg4v1": {"Key": "msmpeg4v1", "Decription": "MPEG-4 part 2 Microsoft variant version 1", "Extra": "V...BD"}, "msmpeg4v2": {"Key": "msmpeg4v2", "Decription": "MPEG-4 part 2 Microsoft variant version 2", "Extra": "V...BD"}, "msmpeg4": {"Key": "msmpeg4", "Decription": "MPEG-4 part 2 Microsoft variant version 3 (codec msmpeg4v3)", "Extra": "V...BD"}, "msp2": {"Key": "msp2", "Decription": "Microsoft Paint (MSP) version 2", "Extra": "V....D"}, "msrle": {"Key": "msrle", "Decription": "Microsoft RLE", "Extra": "V....D"}, "mss1": {"Key": "mss1", "Decription": "MS Screen 1", "Extra": "V....D"}, "mss2": {"Key": "mss2", "Decription": "MS Windows Media Video V9 Screen", "Extra": "V....D"}, "msvideo1": {"Key": "msvideo1", "Decription": "Microsoft Video 1", "Extra": "V....D"}, "mszh": {"Key": "mszh", "Decription": "LCL (LossLess Codec Library) MSZH", "Extra": "VF...D"}, "mts2": {"Key": "mts2", "Decription": "MS Expression Encoder Screen", "Extra": "V....D"}, "mv30": {"Key": "mv30", "Decription": "MidiVid 3.0", "Extra": "V....D"}, "mvc1": {"Key": "mvc1", "Decription": "Silicon Graphics Motion Video Compressor 1", "Extra": "V....D"}, "mvc2": {"Key": "mvc2", "Decription": "Silicon Graphics Motion Video Compressor 2", "Extra": "V....D"}, "mvdv": {"Key": "mvdv", "Decription": "MidiVid VQ", "Extra": "V....D"}, "mvha": {"Key": "mvha", "Decription": "MidiVid Archive Codec", "Extra": "V....D"}, "mwsc": {"Key": "mwsc", "Decription": "MatchWare Screen Capture Codec", "Extra": "V....D"}, "mxpeg": {"Key": "mxpeg", "Decription": "Mobotix MxPEG video", "Extra": "V....D"}, "notchlc": {"Key": "notchlc", "Decription": "NotchLC", "Extra": "VF...D"}, "nuv": {"Key": "nuv", "Decription": "NuppelVideo/RTJPEG", "Extra": "V....D"}, "paf_video": {"Key": "paf_video", "Decription": "Amazing Studio Packed Animation File Video", "Extra": "V....D"}, "pam": {"Key": "pam", "Decription": "PAM (Portable AnyMap) image", "Extra": "V....D"}, "pbm": {"Key": "pbm", "Decription": "PBM (Portable BitMap) image", "Extra": "V....D"}, "pcx": {"Key": "pcx", "Decription": "PC Paintbrush PCX image", "Extra": "V....D"}, "pfm": {"Key": "pfm", "Decription": "PFM (Portable FloatMap) image", "Extra": "V....D"}, "pgm": {"Key": "pgm", "Decription": "PGM (Portable GrayMap) image", "Extra": "V....D"}, "pgmyuv": {"Key": "pgmyuv", "Decription": "PGMYUV (Portable GrayMap YUV) image", "Extra": "V....D"}, "pgx": {"Key": "pgx", "Decription": "PGX (JPEG2000 Test Format)", "Extra": "V....D"}, "phm": {"Key": "phm", "Decription": "PHM (Portable HalfFloatMap) image", "Extra": "V....D"}, "photocd": {"Key": "photocd", "Decription": "Kodak Photo CD", "Extra": "VF...D"}, "pictor": {"Key": "pictor", "Decription": "Pictor/PC Paint", "Extra": "V....D"}, "pixlet": {"Key": "pixlet", "Decription": "Apple Pixlet", "Extra": "VF...D"}, "png": {"Key": "png", "Decription": "PNG (Portable Network Graphics) image", "Extra": "VF...D"}, "ppm": {"Key": "ppm", "Decription": "PPM (Portable PixelMap) image", "Extra": "V....D"}, "prores": {"Key": "prores", "Decription": "Apple ProRes (iCodec Pro)", "Extra": "VFS..D"}, "prosumer": {"Key": "prosumer", "Decription": "Brooktree ProSumer Video", "Extra": "V....D"}, "psd": {"Key": "psd", "Decription": "Photoshop PSD file", "Extra": "VF...D"}, "ptx": {"Key": "ptx", "Decription": "V.Flash PTX image", "Extra": "V....D"}, "qdraw": {"Key": "qdraw", "Decription": "Apple QuickDraw", "Extra": "V....D"}, "qoi": {"Key": "qoi", "Decription": "QOI (Quite OK Image format) image", "Extra": "VF...D"}, "qpeg": {"Key": "qpeg", "Decription": "Q-team QPEG", "Extra": "V....D"}, "qtrle": {"Key": "qtrle", "Decription": "QuickTime Animation (RLE) video", "Extra": "V....D"}, "r10k": {"Key": "r10k", "Decription": "AJA Kona 10-bit RGB Codec", "Extra": "V....D"}, "r210": {"Key": "r210", "Decription": "Uncompressed RGB 10-bit", "Extra": "V....D"}, "rasc": {"Key": "rasc", "Decription": "RemotelyAnywhere Screen Capture", "Extra": "V....D"}, "rawvideo": {"Key": "rawvideo", "Decription": "raw video", "Extra": "V....."}, "rl2": {"Key": "rl2", "Decription": "RL2 video", "Extra": "V....D"}, "roqvideo": {"Key": "roqvideo", "Decription": "id RoQ video (codec roq)", "Extra": "V....D"}, "rpza": {"Key": "rpza", "Decription": "QuickTime video (RPZA)", "Extra": "V....D"}, "rscc": {"Key": "rscc", "Decription": "innoHeim/Rsupport Screen Capture Codec", "Extra": "V....D"}, "rv10": {"Key": "rv10", "Decription": "RealVideo 1.0", "Extra": "V....D"}, "rv20": {"Key": "rv20", "Decription": "RealVideo 2.0", "Extra": "V....D"}, "rv30": {"Key": "rv30", "Decription": "RealVideo 3.0", "Extra": "VF...D"}, "rv40": {"Key": "rv40", "Decription": "RealVideo 4.0", "Extra": "VF...D"}, "sanm": {"Key": "sanm", "Decription": "LucasArts SANM/Smush video", "Extra": "V....D"}, "scpr": {"Key": "scpr", "Decription": "ScreenPressor", "Extra": "V....D"}, "screenpresso": {"Key": "screenpresso", "Decription": "Screenpresso", "Extra": "V....D"}, "sga": {"Key": "sga", "Decription": "Digital Pictures SGA Video", "Extra": "V....D"}, "sgi": {"Key": "sgi", "Decription": "SGI image", "Extra": "V....D"}, "sgirle": {"Key": "sgirle", "Decription": "Silicon Graphics RLE 8-bit video", "Extra": "V....D"}, "sheervideo": {"Key": "sheervideo", "Decription": "BitJazz SheerVideo", "Extra": "VF...D"}, "simbiosis_imx": {"Key": "simbiosis_imx", "Decription": "Simbiosis Interactive IMX Video", "Extra": "V....D"}, "smackvid": {"Key": "<PERSON><PERSON>", "Decription": "Smacker video (codec smackvideo)", "Extra": "V....D"}, "smc": {"Key": "smc", "Decription": "QuickTime Graphics (SMC)", "Extra": "V....D"}, "smvjpeg": {"Key": "smvjpeg", "Decription": "SMV JPEG", "Extra": "V....D"}, "snow": {"Key": "snow", "Decription": "Snow", "Extra": "V....D"}, "sp5x": {"Key": "sp5x", "Decription": "Sunplus JPEG (SP5X)", "Extra": "V....D"}, "speedhq": {"Key": "speedhq", "Decription": "NewTek SpeedHQ", "Extra": "V....D"}, "srgc": {"Key": "srgc", "Decription": "Screen Recorder Gold Codec", "Extra": "V....D"}, "sunrast": {"Key": "<PERSON><PERSON>t", "Decription": "Sun Rasterfile image", "Extra": "V....D"}, "svq1": {"Key": "svq1", "Decription": "Sorenson Vector Quantizer 1 / Sorenson Video 1 / SVQ1", "Extra": "V....D"}, "svq3": {"Key": "svq3", "Decription": "<PERSON>renson Vector Quantizer 3 / Sorenson Video 3 / SVQ3", "Extra": "V...BD"}, "targa": {"Key": "targa", "Decription": "Truevision Targa image", "Extra": "V....D"}, "targa_y216": {"Key": "targa_y216", "Decription": "Pinnacle TARGA CineWave YUV16", "Extra": "V....D"}, "tdsc": {"Key": "tdsc", "Decription": "TDSC", "Extra": "V....D"}, "eatgq": {"Key": "eatgq", "Decription": "Electronic Arts TGQ video (codec tgq)", "Extra": "V....D"}, "eatgv": {"Key": "eatgv", "Decription": "Electronic Arts TGV video (codec tgv)", "Extra": "V....D"}, "theora": {"Key": "theora", "Decription": "Theora", "Extra": "VF..BD"}, "thp": {"Key": "thp", "Decription": "Nintendo Gamecube THP video", "Extra": "V....D"}, "tiertexseqvideo": {"Key": "tiertexseqvideo", "Decription": "Tiertex Limited SEQ video", "Extra": "V....D"}, "tiff": {"Key": "tiff", "Decription": "TIFF image", "Extra": "VF...D"}, "tmv": {"Key": "tmv", "Decription": "8088flex TMV", "Extra": "V....D"}, "eatqi": {"Key": "eatqi", "Decription": "Electronic Arts TQI Video (codec tqi)", "Extra": "V....D"}, "truemotion1": {"Key": "truemotion1", "Decription": "Duck TrueMotion 1.0", "Extra": "V....D"}, "truemotion2": {"Key": "truemotion2", "Decription": "Duck TrueMotion 2.0", "Extra": "V....D"}, "truemotion2rt": {"Key": "truemotion2rt", "Decription": "Duck TrueMotion 2.0 Real Time", "Extra": "V....D"}, "camtasia": {"Key": "camtasia", "Decription": "TechSmith Screen Capture Codec (codec tscc)", "Extra": "V....D"}, "tscc2": {"Key": "tscc2", "Decription": "TechSmith Screen Codec 2", "Extra": "V....D"}, "txd": {"Key": "txd", "Decription": "Renderware TXD (TeXture Dictionary) image", "Extra": "V....D"}, "ultimotion": {"Key": "ultimotion", "Decription": "IBM UltiMotion (codec ulti)", "Extra": "V....D"}, "utvideo": {"Key": "utvideo", "Decription": "Ut Video", "Extra": "VF...D"}, "v210": {"Key": "v210", "Decription": "Uncompressed 4:2:2 10-bit", "Extra": "VFS..D"}, "v210x": {"Key": "v210x", "Decription": "Uncompressed 4:2:2 10-bit", "Extra": "V....D"}, "v308": {"Key": "v308", "Decription": "Uncompressed packed 4:4:4", "Extra": "V....D"}, "v408": {"Key": "v408", "Decription": "Uncompressed packed QT 4:4:4:4", "Extra": "V....D"}, "v410": {"Key": "v410", "Decription": "Uncompressed 4:4:4 10-bit", "Extra": "VFS..D"}, "vb": {"Key": "vb", "Decription": "Beam Software VB", "Extra": "V....D"}, "vble": {"Key": "vble", "Decription": "VBLE Lossless Codec", "Extra": "VF...D"}, "vbn": {"Key": "vbn", "Decription": "Vizrt Binary Image", "Extra": "V.S..D"}, "vc1": {"Key": "vc1", "Decription": "SMPTE VC-1", "Extra": "V....D"}, "vc1_qsv": {"Key": "vc1_qsv", "Decription": "VC1 video (Intel Quick Sync Video acceleration) (codec vc1)", "Extra": "V....D"}, "vc1_v4l2m2m": {"Key": "vc1_v4l2m2m", "Decription": "V4L2 mem2mem VC1 decoder wrapper (codec vc1)", "Extra": "V....."}, "vc1_cuvid": {"Key": "vc1_cuvid", "Decription": "Nvidia CUVID VC1 decoder (codec vc1)", "Extra": "V....."}, "vc1image": {"Key": "vc1image", "Decription": "Windows Media Video 9 Image v2", "Extra": "V....D"}, "vcr1": {"Key": "vcr1", "Decription": "ATI VCR1", "Extra": "V....D"}, "xl": {"Key": "xl", "Decription": "Miro VideoXL (codec vixl)", "Extra": "V....D"}, "vmdvideo": {"Key": "vmdvideo", "Decription": "Sierra VMD video", "Extra": "V....D"}, "vmnc": {"Key": "vmnc", "Decription": "VMware Screen Codec / VMware Video", "Extra": "V....D"}, "vp3": {"Key": "vp3", "Decription": "On2 VP3", "Extra": "VF..BD"}, "vp4": {"Key": "vp4", "Decription": "On2 VP4", "Extra": "VF..BD"}, "vp5": {"Key": "vp5", "Decription": "On2 VP5", "Extra": "V....D"}, "vp6": {"Key": "vp6", "Decription": "On2 VP6", "Extra": "V....D"}, "vp6a": {"Key": "vp6a", "Decription": "On2 VP6 (Flash version, with alpha channel)", "Extra": "V.S..D"}, "vp6f": {"Key": "vp6f", "Decription": "On2 VP6 (Flash version)", "Extra": "V....D"}, "vp7": {"Key": "vp7", "Decription": "On2 VP7", "Extra": "V....D"}, "vp8": {"Key": "vp8", "Decription": "On2 VP8", "Extra": "VFS..D"}, "vp8_v4l2m2m": {"Key": "vp8_v4l2m2m", "Decription": "V4L2 mem2mem VP8 decoder wrapper (codec vp8)", "Extra": "V....."}, "libvpx": {"Key": "libvpx", "Decription": "libvpx VP8 (codec vp8)", "Extra": "V....D"}, "vp8_cuvid": {"Key": "vp8_cuvid", "Decription": "Nvidia CUVID VP8 decoder (codec vp8)", "Extra": "V....."}, "vp8_qsv": {"Key": "vp8_qsv", "Decription": "VP8 video (Intel Quick Sync Video acceleration) (codec vp8)", "Extra": "V....D"}, "vp9": {"Key": "vp9", "Decription": "Google VP9", "Extra": "VFS..D"}, "vp9_v4l2m2m": {"Key": "vp9_v4l2m2m", "Decription": "V4L2 mem2mem VP9 decoder wrapper (codec vp9)", "Extra": "V....."}, "libvpx-vp9": {"Key": "libvpx-vp9", "Decription": "libvpx VP9 (codec vp9)", "Extra": "V....."}, "vp9_cuvid": {"Key": "vp9_cuvid", "Decription": "Nvidia CUVID VP9 decoder (codec vp9)", "Extra": "V....."}, "vp9_qsv": {"Key": "vp9_qsv", "Decription": "VP9 video (Intel Quick Sync Video acceleration) (codec vp9)", "Extra": "V....D"}, "wcmv": {"Key": "wcmv", "Decription": "WinCAM Motion Video", "Extra": "V....D"}, "webp": {"Key": "webp", "Decription": "WebP image", "Extra": "VF...D"}, "wmv1": {"Key": "wmv1", "Decription": "Windows Media Video 7", "Extra": "V...BD"}, "wmv2": {"Key": "wmv2", "Decription": "Windows Media Video 8", "Extra": "V...BD"}, "wmv3": {"Key": "wmv3", "Decription": "Windows Media Video 9", "Extra": "V....D"}, "wmv3image": {"Key": "wmv3image", "Decription": "Windows Media Video 9 Image", "Extra": "V....D"}, "wnv1": {"Key": "wnv1", "Decription": "Winnov WNV1", "Extra": "V....D"}, "wrapped_avframe": {"Key": "wrapped_avframe", "Decription": "AVPacket to AVFrame passthrough", "Extra": "V....."}, "vqavideo": {"Key": "vqavideo", "Decription": "Westwood Studios VQA (Vector Quantized Animation) video (codec ws_vqa)", "Extra": "V....D"}, "xan_wc3": {"Key": "xan_wc3", "Decription": "Wing Commander III / Xan", "Extra": "V....D"}, "xan_wc4": {"Key": "xan_wc4", "Decription": "Wing Commander IV / Xxan", "Extra": "V....D"}, "xbin": {"Key": "xbin", "Decription": "eXtended BINary text", "Extra": "V....D"}, "xbm": {"Key": "xbm", "Decription": "XBM (X BitMap) image", "Extra": "V....D"}, "xface": {"Key": "xface", "Decription": "X-face image", "Extra": "V....D"}, "xpm": {"Key": "xpm", "Decription": "XPM (X PixMap) image", "Extra": "V....D"}, "xwd": {"Key": "xwd", "Decription": "XWD (X Window Dump) image", "Extra": "V....D"}, "y41p": {"Key": "y41p", "Decription": "Uncompressed YUV 4:1:1 12-bit", "Extra": "V....D"}, "ylc": {"Key": "ylc", "Decription": "YUY2 Lossless Codec", "Extra": "VF...D"}, "yop": {"Key": "yop", "Decription": "Psygnosis YOP Video", "Extra": "V....."}, "yuv4": {"Key": "yuv4", "Decription": "Uncompressed packed 4:2:0", "Extra": "V....D"}, "zerocodec": {"Key": "zerocodec", "Decription": "ZeroCodec Lossless Video", "Extra": "V....D"}, "zlib": {"Key": "zlib", "Decription": "LCL (LossLess Codec Library) ZLIB", "Extra": "VF...D"}, "zmbv": {"Key": "zmbv", "Decription": "Zip Motion Blocks Video", "Extra": "V....D"}}, "AudioDecoders": {"8svx_exp": {"Key": "8svx_exp", "Decription": "8SVX exponential", "Extra": "A....D"}, "8svx_fib": {"Key": "8svx_fib", "Decription": "8SV<PERSON> fi<PERSON>ci", "Extra": "A....D"}, "aac": {"Key": "aac", "Decription": "AAC (Advanced Audio Coding)", "Extra": "A....D"}, "aac_fixed": {"Key": "aac_fixed", "Decription": "AAC (Advanced Audio Coding) (codec aac)", "Extra": "A....D"}, "aac_latm": {"Key": "aac_latm", "Decription": "AAC LATM (Advanced Audio Coding LATM syntax)", "Extra": "A....D"}, "ac3": {"Key": "ac3", "Decription": "ATSC A/52A (AC-3)", "Extra": "A....D"}, "ac3_fixed": {"Key": "ac3_fixed", "Decription": "ATSC A/52A (AC-3) (codec ac3)", "Extra": "A....D"}, "ac4": {"Key": "ac4", "Decription": "AC-4", "Extra": "A....D"}, "acelp.kelvin": {"Key": "acelp.kelvin", "Decription": "Sipro ACELP.KELVIN", "Extra": "A....D"}, "adpcm_4xm": {"Key": "adpcm_4xm", "Decription": "ADPCM 4X Movie", "Extra": "A....D"}, "adpcm_adx": {"Key": "adpcm_adx", "Decription": "SEGA CRI ADX ADPCM", "Extra": "A....D"}, "adpcm_afc": {"Key": "adpcm_afc", "Decription": "ADPCM Nintendo Gamecube AFC", "Extra": "A....D"}, "adpcm_agm": {"Key": "adpcm_agm", "Decription": "ADPCM AmuseGraphics Movie", "Extra": "A....D"}, "adpcm_aica": {"Key": "adpcm_aica", "Decription": "ADPCM Yamaha AICA", "Extra": "A....D"}, "adpcm_argo": {"Key": "adpcm_argo", "Decription": "ADPCM Argonaut Games", "Extra": "A....D"}, "adpcm_ct": {"Key": "adpcm_ct", "Decription": "ADPCM Creative Technology", "Extra": "A....D"}, "adpcm_dtk": {"Key": "adpcm_dtk", "Decription": "ADPCM Nintendo Gamecube DTK", "Extra": "A....D"}, "adpcm_ea": {"Key": "adpcm_ea", "Decription": "ADPCM Electronic Arts", "Extra": "A....D"}, "adpcm_ea_maxis_xa": {"Key": "adpcm_ea_maxis_xa", "Decription": "ADPCM Electronic Arts Maxis CDROM XA", "Extra": "A....D"}, "adpcm_ea_r1": {"Key": "adpcm_ea_r1", "Decription": "ADPCM Electronic Arts R1", "Extra": "A....D"}, "adpcm_ea_r2": {"Key": "adpcm_ea_r2", "Decription": "ADPCM Electronic Arts R2", "Extra": "A....D"}, "adpcm_ea_r3": {"Key": "adpcm_ea_r3", "Decription": "ADPCM Electronic Arts R3", "Extra": "A....D"}, "adpcm_ea_xas": {"Key": "adpcm_ea_xas", "Decription": "ADPCM Electronic Arts XAS", "Extra": "A....D"}, "g722": {"Key": "g722", "Decription": "G.722 ADPCM (codec adpcm_g722)", "Extra": "A....D"}, "g726": {"Key": "g726", "Decription": "G.726 ADPCM (codec adpcm_g726)", "Extra": "A....D"}, "g726le": {"Key": "g726le", "Decription": "G.726 ADPCM little-endian (codec adpcm_g726le)", "Extra": "A....D"}, "adpcm_ima_acorn": {"Key": "adpcm_ima_acorn", "Decription": "ADPCM IMA Acorn Replay", "Extra": "A....D"}, "adpcm_ima_alp": {"Key": "adpcm_ima_alp", "Decription": "ADPCM IMA High Voltage Software ALP", "Extra": "A....D"}, "adpcm_ima_amv": {"Key": "adpcm_ima_amv", "Decription": "ADPCM IMA AMV", "Extra": "A....D"}, "adpcm_ima_apc": {"Key": "adpcm_ima_apc", "Decription": "ADPCM IMA CRYO APC", "Extra": "A....D"}, "adpcm_ima_apm": {"Key": "adpcm_ima_apm", "Decription": "ADPCM IMA Ubisoft APM", "Extra": "A....D"}, "adpcm_ima_cunning": {"Key": "adpcm_ima_cunning", "Decription": "ADPCM IMA Cunning Developments", "Extra": "A....D"}, "adpcm_ima_dat4": {"Key": "adpcm_ima_dat4", "Decription": "ADPCM IMA Eurocom DAT4", "Extra": "A....D"}, "adpcm_ima_dk3": {"Key": "adpcm_ima_dk3", "Decription": "ADPCM IMA Duck DK3", "Extra": "A....D"}, "adpcm_ima_dk4": {"Key": "adpcm_ima_dk4", "Decription": "ADPCM IMA Duck DK4", "Extra": "A....D"}, "adpcm_ima_ea_eacs": {"Key": "adpcm_ima_ea_eacs", "Decription": "ADPCM IMA Electronic Arts EACS", "Extra": "A....D"}, "adpcm_ima_ea_sead": {"Key": "adpcm_ima_ea_sead", "Decription": "ADPCM IMA Electronic Arts SEAD", "Extra": "A....D"}, "adpcm_ima_iss": {"Key": "adpcm_ima_iss", "Decription": "ADPCM IMA Funcom ISS", "Extra": "A....D"}, "adpcm_ima_moflex": {"Key": "adpcm_ima_moflex", "Decription": "ADPCM IMA MobiClip MOFLEX", "Extra": "A....D"}, "adpcm_ima_mtf": {"Key": "adpcm_ima_mtf", "Decription": "ADPCM IMA Capcom's MT Framework", "Extra": "A....D"}, "adpcm_ima_oki": {"Key": "adpcm_ima_oki", "Decription": "ADPCM IMA Dialogic OKI", "Extra": "A....D"}, "adpcm_ima_qt": {"Key": "adpcm_ima_qt", "Decription": "ADPCM IMA QuickTime", "Extra": "A....D"}, "adpcm_ima_rad": {"Key": "adpcm_ima_rad", "Decription": "ADPCM IMA Radical", "Extra": "A....D"}, "adpcm_ima_smjpeg": {"Key": "adpcm_ima_smjpeg", "Decription": "ADPCM IMA Loki SDL MJPEG", "Extra": "A....D"}, "adpcm_ima_ssi": {"Key": "adpcm_ima_ssi", "Decription": "ADPCM IMA Simon & Schuster Interactive", "Extra": "A....D"}, "adpcm_ima_wav": {"Key": "adpcm_ima_wav", "Decription": "ADPCM IMA WAV", "Extra": "A....D"}, "adpcm_ima_ws": {"Key": "adpcm_ima_ws", "Decription": "ADPCM IMA Westwood", "Extra": "A....D"}, "adpcm_ms": {"Key": "adpcm_ms", "Decription": "ADPCM Microsoft", "Extra": "A....D"}, "adpcm_mtaf": {"Key": "adpcm_mtaf", "Decription": "ADPCM MTAF", "Extra": "A....D"}, "adpcm_psx": {"Key": "adpcm_psx", "Decription": "ADPCM Playstation", "Extra": "A....D"}, "adpcm_sbpro_2": {"Key": "adpcm_sbpro_2", "Decription": "ADPCM Sound Blaster Pro 2-bit", "Extra": "A....D"}, "adpcm_sbpro_3": {"Key": "adpcm_sbpro_3", "Decription": "ADPCM Sound Blaster Pro 2.6-bit", "Extra": "A....D"}, "adpcm_sbpro_4": {"Key": "adpcm_sbpro_4", "Decription": "ADPCM Sound Blaster Pro 4-bit", "Extra": "A....D"}, "adpcm_swf": {"Key": "adpcm_swf", "Decription": "ADPCM Shockwave Flash", "Extra": "A....D"}, "adpcm_thp": {"Key": "adpcm_thp", "Decription": "ADPCM Nintendo THP", "Extra": "A....D"}, "adpcm_thp_le": {"Key": "adpcm_thp_le", "Decription": "ADPCM Nintendo THP (little-endian)", "Extra": "A....D"}, "adpcm_vima": {"Key": "adpcm_vima", "Decription": "LucasArts VIMA audio", "Extra": "A....D"}, "adpcm_xa": {"Key": "adpcm_xa", "Decription": "ADPCM CDROM XA", "Extra": "A....D"}, "adpcm_yamaha": {"Key": "adpcm_yamaha", "Decription": "ADPCM Yamaha", "Extra": "A....D"}, "adpcm_zork": {"Key": "adpcm_zork", "Decription": "ADPCM Zork", "Extra": "A....D"}, "alac": {"Key": "alac", "Decription": "ALAC (Apple Lossless Audio Codec)", "Extra": "AF...D"}, "amrnb": {"Key": "amrnb", "Decription": "AMR-NB (Adaptive Multi-Rate NarrowBand) (codec amr_nb)", "Extra": "A....D"}, "amrwb": {"Key": "amrwb", "Decription": "AMR-WB (Adaptive Multi-Rate WideBand) (codec amr_wb)", "Extra": "A....D"}, "ape": {"Key": "ape", "Decription": "Monkey's Audio", "Extra": "A....D"}, "aptx": {"Key": "aptx", "Decription": "aptX (Audio Processing Technology for Bluetooth)", "Extra": "A....D"}, "aptx_hd": {"Key": "aptx_hd", "Decription": "aptX HD (Audio Processing Technology for Bluetooth)", "Extra": "A....D"}, "atrac1": {"Key": "atrac1", "Decription": "ATRAC1 (Adaptive TRansform Acoustic Coding)", "Extra": "A....D"}, "atrac3": {"Key": "atrac3", "Decription": "ATRAC3 (Adaptive TRansform Acoustic Coding 3)", "Extra": "A....D"}, "atrac3al": {"Key": "atrac3al", "Decription": "ATRAC3 AL (Adaptive TRansform Acoustic Coding 3 Advanced Lossless)", "Extra": "A....D"}, "atrac3plus": {"Key": "atrac3plus", "Decription": "ATRAC3+ (Adaptive TRansform Acoustic Coding 3+) (codec atrac3p)", "Extra": "A....D"}, "atrac3plusal": {"Key": "atrac3plusal", "Decription": "ATRAC3+ AL (Adaptive TRansform Acoustic Coding 3+ Advanced Lossless) (codec atrac3pal)", "Extra": "A....D"}, "atrac9": {"Key": "atrac9", "Decription": "ATRAC9 (Adaptive TRansform Acoustic Coding 9)", "Extra": "A....D"}, "on2avc": {"Key": "on2avc", "Decription": "On2 Audio for Video Codec (codec avc)", "Extra": "A....D"}, "binkaudio_dct": {"Key": "binkaudio_dct", "Decription": "Bink Audio (DCT)", "Extra": "A....D"}, "binkaudio_rdft": {"Key": "binkaudio_rdft", "Decription": "Bink Audio (RDFT)", "Extra": "A....D"}, "bmv_audio": {"Key": "bmv_audio", "Decription": "Discworld II BMV audio", "Extra": "A....D"}, "comfortnoise": {"Key": "comfortnoise", "Decription": "RFC 3389 comfort noise generator", "Extra": "A....D"}, "cook": {"Key": "cook", "Decription": "<PERSON> / Cooker / Gecko (RealAudio G2)", "Extra": "A....D"}, "derf_dpcm": {"Key": "derf_dpcm", "Decription": "DPCM Xilam DERF", "Extra": "A....D"}, "dfpwm": {"Key": "dfpwm", "Decription": "DFPWM1a audio", "Extra": "A....D"}, "dolby_e": {"Key": "dolby_e", "Decription": "Dolby E", "Extra": "A....D"}, "dsd_lsbf": {"Key": "dsd_lsbf", "Decription": "DSD (Direct Stream Digital), least significant bit first", "Extra": "A.S..D"}, "dsd_lsbf_planar": {"Key": "dsd_lsbf_planar", "Decription": "DSD (Direct Stream Digital), least significant bit first, planar", "Extra": "A.S..D"}, "dsd_msbf": {"Key": "dsd_msbf", "Decription": "DSD (Direct Stream Digital), most significant bit first", "Extra": "A.S..D"}, "dsd_msbf_planar": {"Key": "dsd_msbf_planar", "Decription": "DSD (Direct Stream Digital), most significant bit first, planar", "Extra": "A.S..D"}, "dsicinaudio": {"Key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Decription": "Delphine Software International CIN audio", "Extra": "A....D"}, "dss_sp": {"Key": "dss_sp", "Decription": "Digital Speech Standard - Standard Play mode (DSS SP)", "Extra": "A....D"}, "dst": {"Key": "dst", "Decription": "DST (Digital Stream Transfer)", "Extra": "A....D"}, "dca": {"Key": "dca", "Decription": "DCA (DTS Coherent Acoustics) (codec dts)", "Extra": "A....D"}, "dvaudio": {"Key": "dvaudio", "Decription": "Ulead DV Audio", "Extra": "A....D"}, "eac3": {"Key": "eac3", "Decription": "ATSC A/52B (AC-3, E-AC-3)", "Extra": "A....D"}, "evrc": {"Key": "evrc", "Decription": "EVRC (Enhanced Variable Rate Codec)", "Extra": "A....D"}, "fastaudio": {"Key": "fastaud<PERSON>", "Decription": "MobiClip <PERSON>udio", "Extra": "A....D"}, "flac": {"Key": "flac", "Decription": "FLAC (Free Lossless Audio Codec)", "Extra": "AF...D"}, "g723_1": {"Key": "g723_1", "Decription": "G.723.1", "Extra": "A....D"}, "g729": {"Key": "g729", "Decription": "G.729", "Extra": "A....D"}, "gremlin_dpcm": {"Key": "gremlin_dpcm", "Decription": "DPCM Gremlin", "Extra": "A....D"}, "gsm": {"Key": "gsm", "Decription": "GSM", "Extra": "A....D"}, "gsm_ms": {"Key": "gsm_ms", "Decription": "GSM Microsoft variant", "Extra": "A....D"}, "hca": {"Key": "hca", "Decription": "CRI HCA", "Extra": "A....D"}, "hcom": {"Key": "hcom", "Decription": "HCOM Audio", "Extra": "A....D"}, "iac": {"Key": "iac", "Decription": "IAC (Indeo Audio Coder)", "Extra": "A....D"}, "ilbc": {"Key": "ilbc", "Decription": "iLBC (Internet Low Bitrate Codec)", "Extra": "A....D"}, "imc": {"Key": "imc", "Decription": "IMC (Intel Music Coder)", "Extra": "A....D"}, "interplay_dpcm": {"Key": "interplay_dpcm", "Decription": "DPCM Interplay", "Extra": "A....D"}, "interplayacm": {"Key": "interplayacm", "Decription": "Interplay ACM", "Extra": "A....D"}, "mace3": {"Key": "mace3", "Decription": "MACE (Macintosh Audio Compression/Expansion) 3:1", "Extra": "A....D"}, "mace6": {"Key": "mace6", "Decription": "MACE (Macintosh Audio Compression/Expansion) 6:1", "Extra": "A....D"}, "metasound": {"Key": "metasound", "Decription": "Voxware MetaSound", "Extra": "A....D"}, "mlp": {"Key": "mlp", "Decription": "MLP (Meridian Lossless Packing)", "Extra": "A....D"}, "mp1": {"Key": "mp1", "Decription": "MP1 (MPEG audio layer 1)", "Extra": "A....D"}, "mp1float": {"Key": "mp1float", "Decription": "MP1 (MPEG audio layer 1) (codec mp1)", "Extra": "A....D"}, "mp2": {"Key": "mp2", "Decription": "MP2 (MPEG audio layer 2)", "Extra": "A....D"}, "mp2float": {"Key": "mp2float", "Decription": "MP2 (MPEG audio layer 2) (codec mp2)", "Extra": "A....D"}, "mp3float": {"Key": "mp3float", "Decription": "MP3 (MPEG audio layer 3) (codec mp3)", "Extra": "A....D"}, "mp3": {"Key": "mp3", "Decription": "MP3 (MPEG audio layer 3)", "Extra": "A....D"}, "mp3adufloat": {"Key": "mp3adufloat", "Decription": "ADU (Application Data Unit) MP3 (MPEG audio layer 3) (codec mp3adu)", "Extra": "A....D"}, "mp3adu": {"Key": "mp3adu", "Decription": "ADU (Application Data Unit) MP3 (MPEG audio layer 3)", "Extra": "A....D"}, "mp3on4float": {"Key": "mp3on4float", "Decription": "MP3onMP4 (codec mp3on4)", "Extra": "A....D"}, "mp3on4": {"Key": "mp3on4", "Decription": "MP3onMP4", "Extra": "A....D"}, "als": {"Key": "als", "Decription": "MPEG-4 Audio Lossless Coding (ALS) (codec mp4als)", "Extra": "A....D"}, "msnsiren": {"Key": "msnsiren", "Decription": "MSN Siren", "Extra": "A....D"}, "mpc7": {"Key": "mpc7", "Decription": "Musepack SV7 (codec musepack7)", "Extra": "A....D"}, "mpc8": {"Key": "mpc8", "Decription": "Musepack SV8 (codec musepack8)", "Extra": "A....D"}, "nellymoser": {"Key": "<PERSON><PERSON><PERSON><PERSON>", "Decription": "<PERSON><PERSON><PERSON><PERSON>", "Extra": "A....D"}, "opus": {"Key": "opus", "Decription": "Opus", "Extra": "A....D"}, "libopus": {"Key": "libopus", "Decription": "libopus Opus (codec opus)", "Extra": "A....D"}, "paf_audio": {"Key": "paf_audio", "Decription": "Amazing Studio Packed Animation File Audio", "Extra": "A....D"}, "pcm_alaw": {"Key": "pcm_alaw", "Decription": "PCM A-law / G.711 A-law", "Extra": "A....D"}, "pcm_bluray": {"Key": "pcm_bluray", "Decription": "PCM signed 16|20|24-bit big-endian for Blu-ray media", "Extra": "A....D"}, "pcm_dvd": {"Key": "pcm_dvd", "Decription": "PCM signed 16|20|24-bit big-endian for DVD media", "Extra": "A....D"}, "pcm_f16le": {"Key": "pcm_f16le", "Decription": "PCM 16.8 floating point little-endian", "Extra": "A....D"}, "pcm_f24le": {"Key": "pcm_f24le", "Decription": "PCM 24.0 floating point little-endian", "Extra": "A....D"}, "pcm_f32be": {"Key": "pcm_f32be", "Decription": "PCM 32-bit floating point big-endian", "Extra": "A....D"}, "pcm_f32le": {"Key": "pcm_f32le", "Decription": "PCM 32-bit floating point little-endian", "Extra": "A....D"}, "pcm_f64be": {"Key": "pcm_f64be", "Decription": "PCM 64-bit floating point big-endian", "Extra": "A....D"}, "pcm_f64le": {"Key": "pcm_f64le", "Decription": "PCM 64-bit floating point little-endian", "Extra": "A....D"}, "pcm_lxf": {"Key": "pcm_lxf", "Decription": "PCM signed 20-bit little-endian planar", "Extra": "A....D"}, "pcm_mulaw": {"Key": "pcm_mulaw", "Decription": "PCM mu-law / G.711 mu-law", "Extra": "A....D"}, "pcm_s16be": {"Key": "pcm_s16be", "Decription": "PCM signed 16-bit big-endian", "Extra": "A....D"}, "pcm_s16be_planar": {"Key": "pcm_s16be_planar", "Decription": "PCM signed 16-bit big-endian planar", "Extra": "A....D"}, "pcm_s16le": {"Key": "pcm_s16le", "Decription": "PCM signed 16-bit little-endian", "Extra": "A....D"}, "pcm_s16le_planar": {"Key": "pcm_s16le_planar", "Decription": "PCM signed 16-bit little-endian planar", "Extra": "A....D"}, "pcm_s24be": {"Key": "pcm_s24be", "Decription": "PCM signed 24-bit big-endian", "Extra": "A....D"}, "pcm_s24daud": {"Key": "pcm_s24daud", "Decription": "PCM D-Cinema audio signed 24-bit", "Extra": "A....D"}, "pcm_s24le": {"Key": "pcm_s24le", "Decription": "PCM signed 24-bit little-endian", "Extra": "A....D"}, "pcm_s24le_planar": {"Key": "pcm_s24le_planar", "Decription": "PCM signed 24-bit little-endian planar", "Extra": "A....D"}, "pcm_s32be": {"Key": "pcm_s32be", "Decription": "PCM signed 32-bit big-endian", "Extra": "A....D"}, "pcm_s32le": {"Key": "pcm_s32le", "Decription": "PCM signed 32-bit little-endian", "Extra": "A....D"}, "pcm_s32le_planar": {"Key": "pcm_s32le_planar", "Decription": "PCM signed 32-bit little-endian planar", "Extra": "A....D"}, "pcm_s64be": {"Key": "pcm_s64be", "Decription": "PCM signed 64-bit big-endian", "Extra": "A....D"}, "pcm_s64le": {"Key": "pcm_s64le", "Decription": "PCM signed 64-bit little-endian", "Extra": "A....D"}, "pcm_s8": {"Key": "pcm_s8", "Decription": "PCM signed 8-bit", "Extra": "A....D"}, "pcm_s8_planar": {"Key": "pcm_s8_planar", "Decription": "PCM signed 8-bit planar", "Extra": "A....D"}, "pcm_sga": {"Key": "pcm_sga", "Decription": "PCM SGA", "Extra": "A....D"}, "pcm_u16be": {"Key": "pcm_u16be", "Decription": "PCM unsigned 16-bit big-endian", "Extra": "A....D"}, "pcm_u16le": {"Key": "pcm_u16le", "Decription": "PCM unsigned 16-bit little-endian", "Extra": "A....D"}, "pcm_u24be": {"Key": "pcm_u24be", "Decription": "PCM unsigned 24-bit big-endian", "Extra": "A....D"}, "pcm_u24le": {"Key": "pcm_u24le", "Decription": "PCM unsigned 24-bit little-endian", "Extra": "A....D"}, "pcm_u32be": {"Key": "pcm_u32be", "Decription": "PCM unsigned 32-bit big-endian", "Extra": "A....D"}, "pcm_u32le": {"Key": "pcm_u32le", "Decription": "PCM unsigned 32-bit little-endian", "Extra": "A....D"}, "pcm_u8": {"Key": "pcm_u8", "Decription": "PCM unsigned 8-bit", "Extra": "A....D"}, "pcm_vidc": {"Key": "pcm_vidc", "Decription": "PCM Archimedes VIDC", "Extra": "A....D"}, "qcelp": {"Key": "qcelp", "Decription": "QCELP / PureVoice", "Extra": "A....D"}, "qdm2": {"Key": "qdm2", "Decription": "QDesign Music Codec 2", "Extra": "A....D"}, "qdmc": {"Key": "qdmc", "Decription": "QDesign Music Codec 1", "Extra": "A....D"}, "real_144": {"Key": "real_144", "Decription": "RealAudio 1.0 (14.4K) (codec ra_144)", "Extra": "A....D"}, "real_288": {"Key": "real_288", "Decription": "RealAudio 2.0 (28.8K) (codec ra_288)", "Extra": "A....D"}, "ralf": {"Key": "ralf", "Decription": "RealAudio Lossless", "Extra": "A....D"}, "roq_dpcm": {"Key": "roq_dpcm", "Decription": "DPCM id RoQ", "Extra": "A....D"}, "s302m": {"Key": "s302m", "Decription": "SMPTE 302M", "Extra": "A....D"}, "sbc": {"Key": "sbc", "Decription": "SBC (low-complexity subband codec)", "Extra": "A....D"}, "sdx2_dpcm": {"Key": "sdx2_dpcm", "Decription": "DPCM Squareroot-Delta-Exact", "Extra": "A....D"}, "shorten": {"Key": "shorten", "Decription": "<PERSON>en", "Extra": "A....D"}, "sipr": {"Key": "sipr", "Decription": "RealAudio SIPR / ACELP.NET", "Extra": "A....D"}, "siren": {"Key": "siren", "Decription": "<PERSON><PERSON>", "Extra": "A....D"}, "smackaud": {"Key": "<PERSON><PERSON>", "Decription": "Smacker audio (codec smackaudio)", "Extra": "A....D"}, "sol_dpcm": {"Key": "sol_dpcm", "Decription": "DPCM Sol", "Extra": "A....D"}, "sonic": {"Key": "sonic", "Decription": "Sonic", "Extra": "A..X.D"}, "speex": {"Key": "speex", "Decription": "Speex", "Extra": "A....D"}, "tak": {"Key": "tak", "Decription": "TAK (Tom's lossless Audio Kompressor)", "Extra": "AF...D"}, "truehd": {"Key": "truehd", "Decription": "TrueHD", "Extra": "A....D"}, "truespeech": {"Key": "true<PERSON><PERSON><PERSON>", "Decription": "DSP Group TrueSpeech", "Extra": "A....D"}, "tta": {"Key": "tta", "Decription": "TTA (True Audio)", "Extra": "AF...D"}, "twinvq": {"Key": "twinvq", "Decription": "VQF TwinVQ", "Extra": "A....D"}, "vmdaudio": {"Key": "vmdaudio", "Decription": "Sierra VMD audio", "Extra": "A....D"}, "vorbis": {"Key": "vorbis", "Decription": "Vorbis", "Extra": "A....D"}, "libvorbis": {"Key": "libvorbis", "Decription": "libvorbis (codec vorbis)", "Extra": "A....."}, "wavesynth": {"Key": "wavesynth", "Decription": "Wave synthesis pseudo-codec", "Extra": "A....D"}, "wavpack": {"Key": "wavpack", "Decription": "WavPack", "Extra": "AFS..D"}, "ws_snd1": {"Key": "ws_snd1", "Decription": "Westwood Audio (SND1) (codec westwood_snd1)", "Extra": "A....D"}, "wmalossless": {"Key": "wmalossless", "Decription": "Windows Media Audio Lossless", "Extra": "A....D"}, "wmapro": {"Key": "wmapro", "Decription": "Windows Media Audio 9 Professional", "Extra": "A....D"}, "wmav1": {"Key": "wmav1", "Decription": "Windows Media Audio 1", "Extra": "A....D"}, "wmav2": {"Key": "wmav2", "Decription": "Windows Media Audio 2", "Extra": "A....D"}, "wmavoice": {"Key": "wmavoice", "Decription": "Windows Media Audio Voice", "Extra": "A....D"}, "xan_dpcm": {"Key": "xan_dpcm", "Decription": "DPCM <PERSON>", "Extra": "A....D"}, "xma1": {"Key": "xma1", "Decription": "Xbox Media Audio 1", "Extra": "A....D"}, "xma2": {"Key": "xma2", "Decription": "Xbox Media Audio 2", "Extra": "A....D"}}, "SubtitleDecoders": {"libaribb24": {"Key": "libaribb24", "Decription": "libaribb24 ARIB STD-B24 caption decoder (codec arib_caption)", "Extra": "S....."}, "ssa": {"Key": "ssa", "Decription": "ASS (Advanced SubStation Alpha) subtitle (codec ass)", "Extra": "S....."}, "ass": {"Key": "ass", "Decription": "ASS (Advanced SubStation Alpha) subtitle", "Extra": "S....."}, "dvbsub": {"Key": "dvbsub", "Decription": "DVB subtitles (codec dvb_subtitle)", "Extra": "S....."}, "libzvbi_teletextdec": {"Key": "libzvbi_teletextdec", "Decription": "Libzvbi DVB teletext decoder (codec dvb_teletext)", "Extra": "S....."}, "dvdsub": {"Key": "dvdsub", "Decription": "DVD subtitles (codec dvd_subtitle)", "Extra": "S....."}, "cc_dec": {"Key": "cc_dec", "Decription": "Closed Caption (EIA-608 / CEA-708) (codec eia_608)", "Extra": "S....."}, "pgssub": {"Key": "pgssub", "Decription": "HDMV Presentation Graphic Stream subtitles (codec hdmv_pgs_subtitle)", "Extra": "S....."}, "jacosub": {"Key": "j<PERSON><PERSON><PERSON>", "Decription": "JACOsub subtitle", "Extra": "S....."}, "microdvd": {"Key": "microdvd", "Decription": "MicroDVD subtitle", "Extra": "S....."}, "mov_text": {"Key": "mov_text", "Decription": "3GPP Timed Text subtitle", "Extra": "S....."}, "mpl2": {"Key": "mpl2", "Decription": "MPL2 subtitle", "Extra": "S....."}, "pjs": {"Key": "pjs", "Decription": "PJS subtitle", "Extra": "S....."}, "realtext": {"Key": "realtext", "Decription": "RealText subtitle", "Extra": "S....."}, "sami": {"Key": "sami", "Decription": "SAMI subtitle", "Extra": "S....."}, "stl": {"Key": "stl", "Decription": "Spruce subtitle format", "Extra": "S....."}, "srt": {"Key": "srt", "Decription": "SubRip subtitle (codec subrip)", "Extra": "S....."}, "subrip": {"Key": "subrip", "Decription": "SubRip subtitle", "Extra": "S....."}, "subviewer": {"Key": "subviewer", "Decription": "SubViewer subtitle", "Extra": "S....."}, "subviewer1": {"Key": "subviewer1", "Decription": "SubViewer1 subtitle", "Extra": "S....."}, "text": {"Key": "text", "Decription": "Raw text subtitle", "Extra": "S....."}, "vplayer": {"Key": "vplayer", "Decription": "VPlayer subtitle", "Extra": "S....."}, "webvtt": {"Key": "webvtt", "Decription": "WebVTT subtitle", "Extra": "S....."}, "xsub": {"Key": "xsub", "Decription": "XSUB", "Extra": "S....."}}, "VideoEncoders": {"a64multi": {"Key": "a64multi", "Decription": "Multicolor charset for Commodore 64 (codec a64_multi)", "Extra": "V....D"}, "a64multi5": {"Key": "a64multi5", "Decription": "Multicolor charset for Commodore 64, extended with 5th color (colram) (codec a64_multi5)", "Extra": "V....D"}, "alias_pix": {"Key": "alias_pix", "Decription": "Alias/Wavefront PIX image", "Extra": "V....."}, "amv": {"Key": "amv", "Decription": "AMV Video", "Extra": "V....."}, "apng": {"Key": "apng", "Decription": "APNG (Animated Portable Network Graphics) image", "Extra": "V....D"}, "asv1": {"Key": "asv1", "Decription": "ASUS V1", "Extra": "V....."}, "asv2": {"Key": "asv2", "Decription": "ASUS V2", "Extra": "V....."}, "avrp": {"Key": "avrp", "Decription": "Avid 1:1 10-bit RGB Packer", "Extra": "V....D"}, "avui": {"Key": "avui", "Decription": "<PERSON><PERSON>", "Extra": "V..X.D"}, "ayuv": {"Key": "ayuv", "Decription": "Uncompressed packed MS 4:4:4:4", "Extra": "V....D"}, "bitpacked": {"Key": "bitpacked", "Decription": "Bitpacked", "Extra": "VF...D"}, "bmp": {"Key": "bmp", "Decription": "BMP (Windows and OS/2 bitmap)", "Extra": "V....D"}, "cfhd": {"Key": "cfhd", "Decription": "GoPro CineForm HD", "Extra": "VF...."}, "cinepak": {"Key": "cinepak", "Decription": "Cinepak", "Extra": "V....."}, "cljr": {"Key": "cljr", "Decription": "Cirrus Logic AccuPak", "Extra": "V....D"}, "vc2": {"Key": "vc2", "Decription": "SMPTE VC-2 (codec dirac)", "Extra": "V.S..D"}, "dnxhd": {"Key": "dnxhd", "Decription": "VC3/DNxHD", "Extra": "VFS..D"}, "dpx": {"Key": "dpx", "Decription": "DPX (Digital Picture Exchange) image", "Extra": "V....D"}, "dvvideo": {"Key": "dvvideo", "Decription": "DV (Digital Video)", "Extra": "VFS..D"}, "exr": {"Key": "exr", "Decription": "OpenEXR image", "Extra": "VF...D"}, "ffv1": {"Key": "ffv1", "Decription": "FFmpeg video codec #1", "Extra": "V.S..."}, "ffvhuff": {"Key": "ffvhuff", "Decription": "Huffyuv FFmpeg variant", "Extra": "VF...."}, "fits": {"Key": "fits", "Decription": "Flexible Image Transport System", "Extra": "V....D"}, "flashsv": {"Key": "flashsv", "Decription": "Flash Screen Video", "Extra": "V....."}, "flashsv2": {"Key": "flashsv2", "Decription": "Flash Screen Video Version 2", "Extra": "V....."}, "flv": {"Key": "flv", "Decription": "FLV / Sorenson Spark / Sorenson H.263 (Flash Video) (codec flv1)", "Extra": "V....."}, "gif": {"Key": "gif", "Decription": "GIF (Graphics Interchange Format)", "Extra": "V....."}, "h261": {"Key": "h261", "Decription": "H.261", "Extra": "V....."}, "h263": {"Key": "h263", "Decription": "H.263 / H.263-1996", "Extra": "V....."}, "h263_v4l2m2m": {"Key": "h263_v4l2m2m", "Decription": "V4L2 mem2mem H.263 encoder wrapper (codec h263)", "Extra": "V....."}, "h263p": {"Key": "h263p", "Decription": "H.263+ / H.263-1998 / H.263 version 2", "Extra": "V.S..."}, "libx264": {"Key": "libx264", "Decription": "libx264 H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10 (codec h264)", "Extra": "V....D"}, "libx264rgb": {"Key": "libx264rgb", "Decription": "libx264 H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10 RGB (codec h264)", "Extra": "V....D"}, "h264_nvenc": {"Key": "h264_nvenc", "Decription": "NVIDIA NVENC H.264 encoder (codec h264)", "Extra": "V....D"}, "h264_qsv": {"Key": "h264_qsv", "Decription": "H.264 / AVC / MPEG-4 AVC / MPEG-4 part 10 (Intel Quick Sync Video acceleration) (codec h264)", "Extra": "V....."}, "h264_v4l2m2m": {"Key": "h264_v4l2m2m", "Decription": "V4L2 mem2mem H.264 encoder wrapper (codec h264)", "Extra": "V....."}, "h264_vaapi": {"Key": "h264_vaapi", "Decription": "H.264/AVC (VAAPI) (codec h264)", "Extra": "V....D"}, "libx265": {"Key": "libx265", "Decription": "libx265 H.265 / HEVC (codec hevc)", "Extra": "V....D"}, "hevc_nvenc": {"Key": "hevc_nvenc", "Decription": "NVIDIA NVENC hevc encoder (codec hevc)", "Extra": "V....D"}, "hevc_qsv": {"Key": "hevc_qsv", "Decription": "HEVC (Intel Quick Sync Video acceleration) (codec hevc)", "Extra": "V....."}, "hevc_v4l2m2m": {"Key": "hevc_v4l2m2m", "Decription": "V4L2 mem2mem HEVC encoder wrapper (codec hevc)", "Extra": "V....."}, "hevc_vaapi": {"Key": "hevc_vaapi", "Decription": "H.265/HEVC (VAAPI) (codec hevc)", "Extra": "V....D"}, "huffyuv": {"Key": "<PERSON><PERSON><PERSON><PERSON>", "Decription": "Huffyuv / HuffYUV", "Extra": "VF...."}, "jpeg2000": {"Key": "jpeg2000", "Decription": "JPEG 2000", "Extra": "V....."}, "jpegls": {"Key": "jpegls", "Decription": "JPEG-LS", "Extra": "VF...D"}, "ljpeg": {"Key": "ljpeg", "Decription": "Lossless JPEG", "Extra": "VF...."}, "magicyuv": {"Key": "magicyuv", "Decription": "MagicYUV video", "Extra": "VF...."}, "mjpeg": {"Key": "mjpeg", "Decription": "MJPEG (Motion JPEG)", "Extra": "VFS..."}, "mjpeg_qsv": {"Key": "mjpeg_qsv", "Decription": "MJPEG (Intel Quick Sync Video acceleration) (codec mjpeg)", "Extra": "V....."}, "mjpeg_vaapi": {"Key": "mjpeg_vaapi", "Decription": "MJPEG (VAAPI) (codec mjpeg)", "Extra": "V....D"}, "mpeg1video": {"Key": "mpeg1video", "Decription": "MPEG-1 video", "Extra": "V.S..."}, "mpeg2video": {"Key": "mpeg2video", "Decription": "MPEG-2 video", "Extra": "V.S..."}, "mpeg2_qsv": {"Key": "mpeg2_qsv", "Decription": "MPEG-2 video (Intel Quick Sync Video acceleration) (codec mpeg2video)", "Extra": "V....."}, "mpeg2_vaapi": {"Key": "mpeg2_vaapi", "Decription": "MPEG-2 (VAAPI) (codec mpeg2video)", "Extra": "V....D"}, "mpeg4": {"Key": "mpeg4", "Decription": "MPEG-4 part 2", "Extra": "V.S..."}, "mpeg4_v4l2m2m": {"Key": "mpeg4_v4l2m2m", "Decription": "V4L2 mem2mem MPEG4 encoder wrapper (codec mpeg4)", "Extra": "V....."}, "msmpeg4v2": {"Key": "msmpeg4v2", "Decription": "MPEG-4 part 2 Microsoft variant version 2", "Extra": "V....."}, "msmpeg4": {"Key": "msmpeg4", "Decription": "MPEG-4 part 2 Microsoft variant version 3 (codec msmpeg4v3)", "Extra": "V....."}, "msvideo1": {"Key": "msvideo1", "Decription": "Microsoft Video-1", "Extra": "V....."}, "pam": {"Key": "pam", "Decription": "PAM (Portable AnyMap) image", "Extra": "V....D"}, "pbm": {"Key": "pbm", "Decription": "PBM (Portable BitMap) image", "Extra": "V....D"}, "pcx": {"Key": "pcx", "Decription": "PC Paintbrush PCX image", "Extra": "V....."}, "pfm": {"Key": "pfm", "Decription": "PFM (Portable FloatMap) image", "Extra": "V....D"}, "pgm": {"Key": "pgm", "Decription": "PGM (Portable GrayMap) image", "Extra": "V....D"}, "pgmyuv": {"Key": "pgmyuv", "Decription": "PGMYUV (Portable GrayMap YUV) image", "Extra": "V....D"}, "phm": {"Key": "phm", "Decription": "PHM (Portable HalfFloatMap) image", "Extra": "V....D"}, "png": {"Key": "png", "Decription": "PNG (Portable Network Graphics) image", "Extra": "VF...."}, "ppm": {"Key": "ppm", "Decription": "PPM (Portable PixelMap) image", "Extra": "V....D"}, "prores": {"Key": "prores", "Decription": "Apple ProRes", "Extra": "VF...."}, "prores_aw": {"Key": "prores_aw", "Decription": "Apple ProRes (codec prores)", "Extra": "VF...."}, "prores_ks": {"Key": "prores_ks", "Decription": "Apple ProRes (iCodec Pro) (codec prores)", "Extra": "VFS..."}, "qoi": {"Key": "qoi", "Decription": "QOI (Quite OK Image format) image", "Extra": "VF...."}, "qtrle": {"Key": "qtrle", "Decription": "QuickTime Animation (RLE) video", "Extra": "V....."}, "r10k": {"Key": "r10k", "Decription": "AJA Kona 10-bit RGB Codec", "Extra": "V....D"}, "r210": {"Key": "r210", "Decription": "Uncompressed RGB 10-bit", "Extra": "V....D"}, "rawvideo": {"Key": "rawvideo", "Decription": "raw video", "Extra": "VF...D"}, "roqvideo": {"Key": "roqvideo", "Decription": "id RoQ video (codec roq)", "Extra": "V....."}, "rpza": {"Key": "rpza", "Decription": "QuickTime video (RPZA)", "Extra": "V....."}, "rv10": {"Key": "rv10", "Decription": "RealVideo 1.0", "Extra": "V....."}, "rv20": {"Key": "rv20", "Decription": "RealVideo 2.0", "Extra": "V....."}, "sgi": {"Key": "sgi", "Decription": "SGI image", "Extra": "V....."}, "smc": {"Key": "smc", "Decription": "QuickTime Graphics (SMC)", "Extra": "V....."}, "snow": {"Key": "snow", "Decription": "Snow", "Extra": "V....."}, "speedhq": {"Key": "speedhq", "Decription": "NewTek SpeedHQ", "Extra": "V....."}, "sunrast": {"Key": "<PERSON><PERSON>t", "Decription": "Sun Rasterfile image", "Extra": "V....."}, "svq1": {"Key": "svq1", "Decription": "Sorenson Vector Quantizer 1 / Sorenson Video 1 / SVQ1", "Extra": "V....."}, "targa": {"Key": "targa", "Decription": "Truevision Targa image", "Extra": "V....."}, "libtheora": {"Key": "libtheora", "Decription": "libtheora Theora (codec theora)", "Extra": "V....D"}, "tiff": {"Key": "tiff", "Decription": "TIFF image", "Extra": "VF...."}, "utvideo": {"Key": "utvideo", "Decription": "Ut Video", "Extra": "VF...."}, "v210": {"Key": "v210", "Decription": "Uncompressed 4:2:2 10-bit", "Extra": "VF...D"}, "v308": {"Key": "v308", "Decription": "Uncompressed packed 4:4:4", "Extra": "V....D"}, "v408": {"Key": "v408", "Decription": "Uncompressed packed QT 4:4:4:4", "Extra": "V....D"}, "v410": {"Key": "v410", "Decription": "Uncompressed 4:4:4 10-bit", "Extra": "V....D"}, "vbn": {"Key": "vbn", "Decription": "Vizrt Binary Image", "Extra": "V.S..D"}, "libvpx": {"Key": "libvpx", "Decription": "libvpx VP8 (codec vp8)", "Extra": "V....D"}, "vp8_v4l2m2m": {"Key": "vp8_v4l2m2m", "Decription": "V4L2 mem2mem VP8 encoder wrapper (codec vp8)", "Extra": "V....."}, "vp8_vaapi": {"Key": "vp8_vaapi", "Decription": "VP8 (VAAPI) (codec vp8)", "Extra": "V....D"}, "libvpx-vp9": {"Key": "libvpx-vp9", "Decription": "libvpx VP9 (codec vp9)", "Extra": "V....D"}, "vp9_vaapi": {"Key": "vp9_vaapi", "Decription": "VP9 (VAAPI) (codec vp9)", "Extra": "V....D"}, "vp9_qsv": {"Key": "vp9_qsv", "Decription": "VP9 video (Intel Quick Sync Video acceleration) (codec vp9)", "Extra": "V....."}, "libwebp_anim": {"Key": "libwebp_anim", "Decription": "libwebp WebP image (codec webp)", "Extra": "V....D"}, "libwebp": {"Key": "libwebp", "Decription": "libwebp WebP image (codec webp)", "Extra": "V....D"}, "wmv1": {"Key": "wmv1", "Decription": "Windows Media Video 7", "Extra": "V....."}, "wmv2": {"Key": "wmv2", "Decription": "Windows Media Video 8", "Extra": "V....."}, "wrapped_avframe": {"Key": "wrapped_avframe", "Decription": "AVFrame to AVPacket passthrough", "Extra": "V....."}, "xbm": {"Key": "xbm", "Decription": "XBM (X BitMap) image", "Extra": "V....."}, "xface": {"Key": "xface", "Decription": "X-face image", "Extra": "V....D"}, "xwd": {"Key": "xwd", "Decription": "XWD (X Window Dump) image", "Extra": "V....D"}, "y41p": {"Key": "y41p", "Decription": "Uncompressed YUV 4:1:1 12-bit", "Extra": "V....D"}, "yuv4": {"Key": "yuv4", "Decription": "Uncompressed packed 4:2:0", "Extra": "V....D"}, "zlib": {"Key": "zlib", "Decription": "LCL (LossLess Codec Library) ZLIB", "Extra": "VF...."}, "zmbv": {"Key": "zmbv", "Decription": "Zip Motion Blocks Video", "Extra": "V....D"}}, "AudioEncoders": {"aac": {"Key": "aac", "Decription": "AAC (Advanced Audio Coding)", "Extra": "A....."}, "ac3": {"Key": "ac3", "Decription": "ATSC A/52A (AC-3)", "Extra": "A....D"}, "ac3_fixed": {"Key": "ac3_fixed", "Decription": "ATSC A/52A (AC-3) (codec ac3)", "Extra": "A....D"}, "adpcm_adx": {"Key": "adpcm_adx", "Decription": "SEGA CRI ADX ADPCM", "Extra": "A....D"}, "adpcm_argo": {"Key": "adpcm_argo", "Decription": "ADPCM Argonaut Games", "Extra": "A....D"}, "g722": {"Key": "g722", "Decription": "G.722 ADPCM (codec adpcm_g722)", "Extra": "A....D"}, "g726": {"Key": "g726", "Decription": "G.726 ADPCM (codec adpcm_g726)", "Extra": "A....D"}, "g726le": {"Key": "g726le", "Decription": "G.726 little endian ADPCM (\"right-justified\") (codec adpcm_g726le)", "Extra": "A....D"}, "adpcm_ima_alp": {"Key": "adpcm_ima_alp", "Decription": "ADPCM IMA High Voltage Software ALP", "Extra": "A....D"}, "adpcm_ima_amv": {"Key": "adpcm_ima_amv", "Decription": "ADPCM IMA AMV", "Extra": "A....D"}, "adpcm_ima_apm": {"Key": "adpcm_ima_apm", "Decription": "ADPCM IMA Ubisoft APM", "Extra": "A....D"}, "adpcm_ima_qt": {"Key": "adpcm_ima_qt", "Decription": "ADPCM IMA QuickTime", "Extra": "A....D"}, "adpcm_ima_ssi": {"Key": "adpcm_ima_ssi", "Decription": "ADPCM IMA Simon & Schuster Interactive", "Extra": "A....D"}, "adpcm_ima_wav": {"Key": "adpcm_ima_wav", "Decription": "ADPCM IMA WAV", "Extra": "A....D"}, "adpcm_ima_ws": {"Key": "adpcm_ima_ws", "Decription": "ADPCM IMA Westwood", "Extra": "A....D"}, "adpcm_ms": {"Key": "adpcm_ms", "Decription": "ADPCM Microsoft", "Extra": "A....D"}, "adpcm_swf": {"Key": "adpcm_swf", "Decription": "ADPCM Shockwave Flash", "Extra": "A....D"}, "adpcm_yamaha": {"Key": "adpcm_yamaha", "Decription": "ADPCM Yamaha", "Extra": "A....D"}, "alac": {"Key": "alac", "Decription": "ALAC (Apple Lossless Audio Codec)", "Extra": "A....."}, "aptx": {"Key": "aptx", "Decription": "aptX (Audio Processing Technology for Bluetooth)", "Extra": "A....D"}, "aptx_hd": {"Key": "aptx_hd", "Decription": "aptX HD (Audio Processing Technology for Bluetooth)", "Extra": "A....D"}, "comfortnoise": {"Key": "comfortnoise", "Decription": "RFC 3389 comfort noise generator", "Extra": "A....D"}, "dfpwm": {"Key": "dfpwm", "Decription": "DFPWM1a audio", "Extra": "A....D"}, "dca": {"Key": "dca", "Decription": "DCA (DTS Coherent Acoustics) (codec dts)", "Extra": "A..X.D"}, "eac3": {"Key": "eac3", "Decription": "ATSC A/52 E-AC-3", "Extra": "A....D"}, "flac": {"Key": "flac", "Decription": "FLAC (Free Lossless Audio Codec)", "Extra": "A....D"}, "g723_1": {"Key": "g723_1", "Decription": "G.723.1", "Extra": "A....D"}, "mlp": {"Key": "mlp", "Decription": "MLP (Meridian Lossless Packing)", "Extra": "A..X.."}, "mp2": {"Key": "mp2", "Decription": "MP2 (MPEG audio layer 2)", "Extra": "A....."}, "mp2fixed": {"Key": "mp2fixed", "Decription": "MP2 fixed point (MPEG audio layer 2) (codec mp2)", "Extra": "A....."}, "libmp3lame": {"Key": "libmp3lame", "Decription": "libmp3lame MP3 (MPEG audio layer 3) (codec mp3)", "Extra": "A....D"}, "nellymoser": {"Key": "<PERSON><PERSON><PERSON><PERSON>", "Decription": "<PERSON><PERSON><PERSON><PERSON>", "Extra": "A....D"}, "opus": {"Key": "opus", "Decription": "Opus", "Extra": "A..X.."}, "libopus": {"Key": "libopus", "Decription": "libopus Opus (codec opus)", "Extra": "A....."}, "pcm_alaw": {"Key": "pcm_alaw", "Decription": "PCM A-law / G.711 A-law", "Extra": "A....D"}, "pcm_bluray": {"Key": "pcm_bluray", "Decription": "PCM signed 16|20|24-bit big-endian for Blu-ray media", "Extra": "A....D"}, "pcm_dvd": {"Key": "pcm_dvd", "Decription": "PCM signed 16|20|24-bit big-endian for DVD media", "Extra": "A....D"}, "pcm_f32be": {"Key": "pcm_f32be", "Decription": "PCM 32-bit floating point big-endian", "Extra": "A....D"}, "pcm_f32le": {"Key": "pcm_f32le", "Decription": "PCM 32-bit floating point little-endian", "Extra": "A....D"}, "pcm_f64be": {"Key": "pcm_f64be", "Decription": "PCM 64-bit floating point big-endian", "Extra": "A....D"}, "pcm_f64le": {"Key": "pcm_f64le", "Decription": "PCM 64-bit floating point little-endian", "Extra": "A....D"}, "pcm_mulaw": {"Key": "pcm_mulaw", "Decription": "PCM mu-law / G.711 mu-law", "Extra": "A....D"}, "pcm_s16be": {"Key": "pcm_s16be", "Decription": "PCM signed 16-bit big-endian", "Extra": "A....D"}, "pcm_s16be_planar": {"Key": "pcm_s16be_planar", "Decription": "PCM signed 16-bit big-endian planar", "Extra": "A....D"}, "pcm_s16le": {"Key": "pcm_s16le", "Decription": "PCM signed 16-bit little-endian", "Extra": "A....D"}, "pcm_s16le_planar": {"Key": "pcm_s16le_planar", "Decription": "PCM signed 16-bit little-endian planar", "Extra": "A....D"}, "pcm_s24be": {"Key": "pcm_s24be", "Decription": "PCM signed 24-bit big-endian", "Extra": "A....D"}, "pcm_s24daud": {"Key": "pcm_s24daud", "Decription": "PCM D-Cinema audio signed 24-bit", "Extra": "A....D"}, "pcm_s24le": {"Key": "pcm_s24le", "Decription": "PCM signed 24-bit little-endian", "Extra": "A....D"}, "pcm_s24le_planar": {"Key": "pcm_s24le_planar", "Decription": "PCM signed 24-bit little-endian planar", "Extra": "A....D"}, "pcm_s32be": {"Key": "pcm_s32be", "Decription": "PCM signed 32-bit big-endian", "Extra": "A....D"}, "pcm_s32le": {"Key": "pcm_s32le", "Decription": "PCM signed 32-bit little-endian", "Extra": "A....D"}, "pcm_s32le_planar": {"Key": "pcm_s32le_planar", "Decription": "PCM signed 32-bit little-endian planar", "Extra": "A....D"}, "pcm_s64be": {"Key": "pcm_s64be", "Decription": "PCM signed 64-bit big-endian", "Extra": "A....D"}, "pcm_s64le": {"Key": "pcm_s64le", "Decription": "PCM signed 64-bit little-endian", "Extra": "A....D"}, "pcm_s8": {"Key": "pcm_s8", "Decription": "PCM signed 8-bit", "Extra": "A....D"}, "pcm_s8_planar": {"Key": "pcm_s8_planar", "Decription": "PCM signed 8-bit planar", "Extra": "A....D"}, "pcm_u16be": {"Key": "pcm_u16be", "Decription": "PCM unsigned 16-bit big-endian", "Extra": "A....D"}, "pcm_u16le": {"Key": "pcm_u16le", "Decription": "PCM unsigned 16-bit little-endian", "Extra": "A....D"}, "pcm_u24be": {"Key": "pcm_u24be", "Decription": "PCM unsigned 24-bit big-endian", "Extra": "A....D"}, "pcm_u24le": {"Key": "pcm_u24le", "Decription": "PCM unsigned 24-bit little-endian", "Extra": "A....D"}, "pcm_u32be": {"Key": "pcm_u32be", "Decription": "PCM unsigned 32-bit big-endian", "Extra": "A....D"}, "pcm_u32le": {"Key": "pcm_u32le", "Decription": "PCM unsigned 32-bit little-endian", "Extra": "A....D"}, "pcm_u8": {"Key": "pcm_u8", "Decription": "PCM unsigned 8-bit", "Extra": "A....D"}, "pcm_vidc": {"Key": "pcm_vidc", "Decription": "PCM Archimedes VIDC", "Extra": "A....D"}, "real_144": {"Key": "real_144", "Decription": "RealAudio 1.0 (14.4K) (codec ra_144)", "Extra": "A....D"}, "roq_dpcm": {"Key": "roq_dpcm", "Decription": "id RoQ DPCM", "Extra": "A....D"}, "s302m": {"Key": "s302m", "Decription": "SMPTE 302M", "Extra": "A..X.D"}, "sbc": {"Key": "sbc", "Decription": "SBC (low-complexity subband codec)", "Extra": "A....D"}, "sonic": {"Key": "sonic", "Decription": "Sonic", "Extra": "A..X.."}, "sonicls": {"Key": "sonicls", "Decription": "Sonic lossless", "Extra": "A..X.."}, "truehd": {"Key": "truehd", "Decription": "TrueHD", "Extra": "A..X.."}, "tta": {"Key": "tta", "Decription": "TTA (True Audio)", "Extra": "A....."}, "vorbis": {"Key": "vorbis", "Decription": "Vorbis", "Extra": "A..X.."}, "libvorbis": {"Key": "libvorbis", "Decription": "libvorbis (codec vorbis)", "Extra": "A....D"}, "wavpack": {"Key": "wavpack", "Decription": "WavPack", "Extra": "A....."}, "wmav1": {"Key": "wmav1", "Decription": "Windows Media Audio 1", "Extra": "A....."}, "wmav2": {"Key": "wmav2", "Decription": "Windows Media Audio 2", "Extra": "A....."}}, "SubtitleEncoders": {"ssa": {"Key": "ssa", "Decription": "ASS (Advanced SubStation Alpha) subtitle (codec ass)", "Extra": "S....."}, "ass": {"Key": "ass", "Decription": "ASS (Advanced SubStation Alpha) subtitle", "Extra": "S....."}, "dvbsub": {"Key": "dvbsub", "Decription": "DVB subtitles (codec dvb_subtitle)", "Extra": "S....."}, "dvdsub": {"Key": "dvdsub", "Decription": "DVD subtitles (codec dvd_subtitle)", "Extra": "S....."}, "mov_text": {"Key": "mov_text", "Decription": "3GPP Timed Text subtitle", "Extra": "S....."}, "srt": {"Key": "srt", "Decription": "SubRip subtitle (codec subrip)", "Extra": "S....."}, "subrip": {"Key": "subrip", "Decription": "SubRip subtitle", "Extra": "S....."}, "text": {"Key": "text", "Decription": "Raw text subtitle", "Extra": "S....."}, "ttml": {"Key": "ttml", "Decription": "TTML subtitle", "Extra": "S....."}, "webvtt": {"Key": "webvtt", "Decription": "WebVTT subtitle", "Extra": "S....."}, "xsub": {"Key": "xsub", "Decription": "DivX subtitles (XSUB)", "Extra": "S....."}}, "VideoFilters": {"addroi": {"Key": "addroi", "Decription": "Add region of interest to frame.", "Extra": "V->V ..."}, "alphaextract": {"Key": "alphaextract", "Decription": "Extract an alpha channel as a grayscale image component.", "Extra": "V->V ..."}, "alphamerge": {"Key": "alphamerge", "Decription": "Copy the luma value of the second input into the alpha channel of the first input.", "Extra": "VV->V T.."}, "amplify": {"Key": "amplify", "Decription": "Amplify changes between successive video frames.", "Extra": "V->V TSC"}, "ass": {"Key": "ass", "Decription": "Render ASS subtitles onto input video using the libass library.", "Extra": "V->V ..."}, "atadenoise": {"Key": "atadenoise", "Decription": "Apply an Adaptive Temporal Averaging Denoiser.", "Extra": "V->V TSC"}, "avgblur": {"Key": "avgb<PERSON>r", "Decription": "Apply Average Blur filter.", "Extra": "V->V T.C"}, "avgblur_opencl": {"Key": "avgblur_opencl", "Decription": "Apply average blur filter", "Extra": "V->V ..."}, "bbox": {"Key": "bbox", "Decription": "Compute bounding box for each frame.", "Extra": "V->V T.C"}, "bench": {"Key": "bench", "Decription": "Benchmark part of a filtergraph.", "Extra": "V->V ..."}, "bilateral": {"Key": "bilateral", "Decription": "Apply Bilateral filter.", "Extra": "V->V TSC"}, "bitplanenoise": {"Key": "bitplanenoise", "Decription": "Measure bit plane noise.", "Extra": "V->V T.."}, "blackdetect": {"Key": "blackdetect", "Decription": "Detect video intervals that are (almost) black.", "Extra": "V->V .S."}, "blackframe": {"Key": "blackframe", "Decription": "Detect frames that are (almost) black.", "Extra": "V->V ..."}, "blend": {"Key": "blend", "Decription": "Blend two video frames into each other.", "Extra": "VV->V TSC"}, "blockdetect": {"Key": "blockdetect", "Decription": "Blockdetect filter.", "Extra": "V->V ..."}, "blurdetect": {"Key": "blurdetect", "Decription": "Blurdetect filter.", "Extra": "V->V ..."}, "boxblur": {"Key": "boxblur", "Decription": "Blur the input.", "Extra": "V->V T.."}, "boxblur_opencl": {"Key": "boxblur_opencl", "Decription": "Apply boxblur filter to input video", "Extra": "V->V ..."}, "bwdif": {"Key": "bwdif", "Decription": "Deinterlace the input image.", "Extra": "V->V TS."}, "cas": {"Key": "cas", "Decription": "Contrast Adaptive Sharpen.", "Extra": "V->V TSC"}, "chromahold": {"Key": "chromahold", "Decription": "Turns a certain color range into gray.", "Extra": "V->V TSC"}, "chromakey": {"Key": "chromakey", "Decription": "Turns a certain color into transparency. Operates on YUV colors.", "Extra": "V->V TSC"}, "chromakey_cuda": {"Key": "chromakey_cuda", "Decription": "GPU accelerated chromakey filter", "Extra": "V->V ..."}, "chromanr": {"Key": "chromanr", "Decription": "Reduce chrominance noise.", "Extra": "V->V TSC"}, "chromashift": {"Key": "chromashift", "Decription": "Shift chroma.", "Extra": "V->V TSC"}, "ciescope": {"Key": "ciescope", "Decription": "Video CIE scope.", "Extra": "V->V ..."}, "codecview": {"Key": "codecview", "Decription": "Visualize information about some codecs.", "Extra": "V->V T.."}, "colorbalance": {"Key": "colorbalance", "Decription": "Adjust the color balance.", "Extra": "V->V TSC"}, "colorchannelmixer": {"Key": "colorchannelmixer", "Decription": "Adjust colors by mixing color channels.", "Extra": "V->V TSC"}, "colorcontrast": {"Key": "colorcontrast", "Decription": "Adjust color contrast between RGB components.", "Extra": "V->V TSC"}, "colorcorrect": {"Key": "colorcorrect", "Decription": "Adjust color white balance selectively for blacks and whites.", "Extra": "V->V TSC"}, "colorize": {"Key": "colorize", "Decription": "Overlay a solid color on the video stream.", "Extra": "V->V TSC"}, "colorkey": {"Key": "colorkey", "Decription": "Turns a certain color into transparency. Operates on RGB colors.", "Extra": "V->V TSC"}, "colorkey_opencl": {"Key": "colorkey_opencl", "Decription": "Turns a certain color into transparency. Operates on RGB colors.", "Extra": "V->V ..."}, "colorhold": {"Key": "colorhold", "Decription": "Turns a certain color range into gray. Operates on RGB colors.", "Extra": "V->V TSC"}, "colorlevels": {"Key": "colorlevels", "Decription": "Adjust the color levels.", "Extra": "V->V TSC"}, "colormap": {"Key": "colormap", "Decription": "Apply custom Color Maps to video stream.", "Extra": "VVV->V TSC"}, "colormatrix": {"Key": "colormat<PERSON>", "Decription": "Convert color matrix.", "Extra": "V->V TS."}, "colorspace": {"Key": "colorspace", "Decription": "Convert between colorspaces.", "Extra": "V->V TS."}, "colortemperature": {"Key": "colortemperature", "Decription": "Adjust color temperature of video.", "Extra": "V->V TSC"}, "convolution": {"Key": "convolution", "Decription": "Apply convolution filter.", "Extra": "V->V TSC"}, "convolution_opencl": {"Key": "convolution_opencl", "Decription": "Apply convolution mask to input video", "Extra": "V->V ..."}, "convolve": {"Key": "convolve", "Decription": "Convolve first video stream with second video stream.", "Extra": "VV->V TS."}, "copy": {"Key": "copy", "Decription": "Copy the input video unchanged to the output.", "Extra": "V->V ..."}, "cover_rect": {"Key": "cover_rect", "Decription": "Find and cover a user specified object.", "Extra": "V->V ..."}, "crop": {"Key": "crop", "Decription": "Crop the input video.", "Extra": "V->V ..C"}, "cropdetect": {"Key": "cropdetect", "Decription": "Auto-detect crop size.", "Extra": "V->V T.."}, "cue": {"Key": "cue", "Decription": "Delay filtering to match a cue.", "Extra": "V->V ..."}, "curves": {"Key": "curves", "Decription": "Adjust components curves.", "Extra": "V->V TSC"}, "datascope": {"Key": "datascope", "Decription": "Video data analysis.", "Extra": "V->V .SC"}, "dblur": {"Key": "dblur", "Decription": "Apply Directional Blur filter.", "Extra": "V->V T.C"}, "dctdnoiz": {"Key": "dctdnoiz", "Decription": "Denoise frames using 2D DCT.", "Extra": "V->V TS."}, "deband": {"Key": "deband", "Decription": "Debands video.", "Extra": "V->V TSC"}, "deblock": {"Key": "deblock", "Decription": "Deblock video.", "Extra": "V->V T.C"}, "deconvolve": {"Key": "deconvolve", "Decription": "Deconvolve first video stream with second video stream.", "Extra": "VV->V TS."}, "dedot": {"Key": "dedot", "Decription": "Reduce cross-luminance and cross-color.", "Extra": "V->V TS."}, "deflate": {"Key": "deflate", "Decription": "Apply deflate effect.", "Extra": "V->V TSC"}, "deflicker": {"Key": "deflicker", "Decription": "Remove temporal frame luminance variations.", "Extra": "V->V ..."}, "deinterlace_qsv": {"Key": "deinterlace_qsv", "Decription": "QuickSync video deinterlacing", "Extra": "V->V ..."}, "deinterlace_vaapi": {"Key": "deinterlace_vaapi", "Decription": "Deinterlacing of VAAPI surfaces", "Extra": "V->V ..."}, "dejudder": {"Key": "dejudder", "Decription": "Remove judder produced by pullup.", "Extra": "V->V ..."}, "delogo": {"Key": "<PERSON><PERSON>", "Decription": "Remove logo from input video.", "Extra": "V->V T.."}, "denoise_vaapi": {"Key": "denoise_vaapi", "Decription": "VAAPI VPP for de-noise", "Extra": "V->V ..."}, "derain": {"Key": "derain", "Decription": "Apply derain filter to the input.", "Extra": "V->V T.."}, "deshake": {"Key": "deshake", "Decription": "Stabilize shaky video.", "Extra": "V->V ..."}, "deshake_opencl": {"Key": "deshake_opencl", "Decription": "Feature-point based video stabilization filter", "Extra": "V->V ..."}, "despill": {"Key": "<PERSON>pill", "Decription": "Despill video.", "Extra": "V->V TSC"}, "detelecine": {"Key": "detelecine", "Decription": "Apply an inverse telecine pattern.", "Extra": "V->V ..."}, "dilation": {"Key": "dilation", "Decription": "Apply dilation effect.", "Extra": "V->V TSC"}, "dilation_opencl": {"Key": "dilation_opencl", "Decription": "Apply dilation effect", "Extra": "V->V ..."}, "displace": {"Key": "displace", "Decription": "Displace pixels.", "Extra": "VVV->V T.."}, "dnn_classify": {"Key": "dnn_classify", "Decription": "Apply DNN classify filter to the input.", "Extra": "V->V ..."}, "dnn_detect": {"Key": "dnn_detect", "Decription": "Apply DNN detect filter to the input.", "Extra": "V->V ..."}, "dnn_processing": {"Key": "dnn_processing", "Decription": "Apply DNN processing filter to the input.", "Extra": "V->V ..."}, "doubleweave": {"Key": "doubleweave", "Decription": "Weave input video fields into double number of frames.", "Extra": "V->V .S."}, "drawbox": {"Key": "drawbox", "Decription": "Draw a colored box on the input video.", "Extra": "V->V T.C"}, "drawgraph": {"Key": "drawgraph", "Decription": "Draw a graph using input video metadata.", "Extra": "V->V ..."}, "drawgrid": {"Key": "drawgrid", "Decription": "Draw a colored grid on the input video.", "Extra": "V->V T.C"}, "drawtext": {"Key": "drawtext", "Decription": "Draw text on top of video frames using libfreetype library.", "Extra": "V->V T.C"}, "edgedetect": {"Key": "edgedetect", "Decription": "Detect and draw edge.", "Extra": "V->V T.."}, "elbg": {"Key": "elbg", "Decription": "Apply posterize effect, using the ELBG algorithm.", "Extra": "V->V ..."}, "entropy": {"Key": "entropy", "Decription": "Measure video frames entropy.", "Extra": "V->V T.."}, "epx": {"Key": "epx", "Decription": "Scale the input using EPX algorithm.", "Extra": "V->V .S."}, "eq": {"Key": "eq", "Decription": "Adjust brightness, contrast, gamma, and saturation.", "Extra": "V->V T.C"}, "erosion": {"Key": "erosion", "Decription": "Apply erosion effect.", "Extra": "V->V TSC"}, "erosion_opencl": {"Key": "erosion_opencl", "Decription": "Apply erosion effect", "Extra": "V->V ..."}, "estdif": {"Key": "estdif", "Decription": "Apply Edge Slope Tracing deinterlace.", "Extra": "V->V TSC"}, "exposure": {"Key": "exposure", "Decription": "Adjust exposure of the video stream.", "Extra": "V->V TSC"}, "extractplanes": {"Key": "extractplanes", "Decription": "Extract planes as grayscale frames.", "Extra": "V->N ..."}, "fade": {"Key": "fade", "Decription": "Fade in/out input video.", "Extra": "V->V TS."}, "feedback": {"Key": "feedback", "Decription": "Apply feedback video filter.", "Extra": "VV->VV ..C"}, "fftdnoiz": {"Key": "fftdnoiz", "Decription": "Denoise frames using 3D FFT.", "Extra": "V->V TSC"}, "fftfilt": {"Key": "fftfilt", "Decription": "Apply arbitrary expressions to pixels in frequency domain.", "Extra": "V->V TS."}, "field": {"Key": "field", "Decription": "Extract a field from the input video.", "Extra": "V->V ..."}, "fieldhint": {"Key": "<PERSON><PERSON><PERSON>", "Decription": "Field matching using hints.", "Extra": "V->V ..."}, "fieldorder": {"Key": "fieldorder", "Decription": "Set the field order.", "Extra": "V->V T.."}, "fillborders": {"Key": "fillborders", "Decription": "Fill borders of the input video.", "Extra": "V->V T.C"}, "find_rect": {"Key": "find_rect", "Decription": "Find a user specified object.", "Extra": "V->V ..."}, "floodfill": {"Key": "floodfill", "Decription": "Fill area with same color with another color.", "Extra": "V->V T.."}, "format": {"Key": "format", "Decription": "Convert the input video to one of the specified pixel formats.", "Extra": "V->V ..."}, "fps": {"Key": "fps", "Decription": "Force constant framerate.", "Extra": "V->V ..."}, "framepack": {"Key": "framepack", "Decription": "Generate a frame packed stereoscopic video.", "Extra": "VV->V ..."}, "framerate": {"Key": "framerate", "Decription": "Upsamples or downsamples progressive source between specified frame rates.", "Extra": "V->V .S."}, "framestep": {"Key": "framestep", "Decription": "Select one frame every N frames.", "Extra": "V->V T.."}, "freezedetect": {"Key": "freezedetect", "Decription": "Detects frozen video input.", "Extra": "V->V ..."}, "freezeframes": {"Key": "freezeframes", "Decription": "Freeze video frames.", "Extra": "VV->V ..."}, "fspp": {"Key": "fspp", "Decription": "Apply Fast Simple Post-processing filter.", "Extra": "V->V T.."}, "gblur": {"Key": "gblur", "Decription": "Apply Gaussian Blur filter.", "Extra": "V->V TSC"}, "geq": {"Key": "geq", "Decription": "Apply generic equation to each pixel.", "Extra": "V->V TS."}, "gradfun": {"Key": "gradfun", "Decription": "Debands video quickly using gradients.", "Extra": "V->V T.."}, "graphmonitor": {"Key": "graphmonitor", "Decription": "Show various filtergraph stats.", "Extra": "V->V ..."}, "grayworld": {"Key": "grayworld", "Decription": "Adjust white balance using LAB gray world algorithm", "Extra": "V->V TS."}, "greyedge": {"Key": "greyedge", "Decription": "Estimates scene illumination by grey edge assumption.", "Extra": "V->V TS."}, "haldclut": {"Key": "haldclut", "Decription": "Adjust colors using a Hald CLUT.", "Extra": "VV->V TSC"}, "hflip": {"Key": "hflip", "Decription": "<PERSON>tally flip the input video.", "Extra": "V->V TS."}, "histeq": {"Key": "histeq", "Decription": "Apply global color histogram equalization.", "Extra": "V->V T.."}, "histogram": {"Key": "histogram", "Decription": "Compute and draw a histogram.", "Extra": "V->V ..."}, "hqdn3d": {"Key": "hqdn3d", "Decription": "Apply a High Quality 3D Denoiser.", "Extra": "V->V TSC"}, "hqx": {"Key": "hqx", "Decription": "Scale the input by 2, 3 or 4 using the hq*x magnification algorithm.", "Extra": "V->V .S."}, "hsvhold": {"Key": "hsvhold", "Decription": "Turns a certain HSV range into gray.", "Extra": "V->V TSC"}, "hsvkey": {"Key": "hsvkey", "Decription": "Turns a certain HSV range into transparency. Operates on YUV colors.", "Extra": "V->V TSC"}, "hue": {"Key": "hue", "Decription": "Adjust the hue and saturation of the input video.", "Extra": "V->V T.C"}, "huesaturation": {"Key": "huesaturation", "Decription": "Apply hue-saturation-intensity adjustments.", "Extra": "V->V TSC"}, "hwdownload": {"Key": "hwdownload", "Decription": "Download a hardware frame to a normal frame", "Extra": "V->V ..."}, "hwmap": {"Key": "hwmap", "Decription": "Map hardware frames", "Extra": "V->V ..."}, "hwupload": {"Key": "hwupload", "Decription": "Upload a normal frame to a hardware frame", "Extra": "V->V ..."}, "hwupload_cuda": {"Key": "hwupload_cuda", "Decription": "Upload a system memory frame to a CUDA device.", "Extra": "V->V ..."}, "hysteresis": {"Key": "hysteresis", "Decription": "Grow first stream into second stream by connecting components.", "Extra": "VV->V T.."}, "identity": {"Key": "identity", "Decription": "Calculate the Identity between two video streams.", "Extra": "VV->V TS."}, "idet": {"Key": "idet", "Decription": "Interlace detect Filter.", "Extra": "V->V ..."}, "il": {"Key": "il", "Decription": "Deinterleave or interleave fields.", "Extra": "V->V T.C"}, "inflate": {"Key": "inflate", "Decription": "Apply inflate effect.", "Extra": "V->V TSC"}, "interlace": {"Key": "interlace", "Decription": "Convert progressive video into interlaced.", "Extra": "V->V ..."}, "kerndeint": {"Key": "kern<PERSON><PERSON>", "Decription": "Apply kernel deinterlacing to the input.", "Extra": "V->V ..."}, "kirsch": {"Key": "kirsch", "Decription": "Apply kirsch operator.", "Extra": "V->V TSC"}, "lagfun": {"Key": "lagfun", "Decription": "Slowly update darker pixels.", "Extra": "V->V TSC"}, "latency": {"Key": "latency", "Decription": "Report video filtering latency.", "Extra": "V->V T.."}, "lenscorrection": {"Key": "lenscorrection", "Decription": "Rectify the image by correcting for lens distortion.", "Extra": "V->V TSC"}, "limiter": {"Key": "limiter", "Decription": "Limit pixels components to the specified range.", "Extra": "V->V TSC"}, "loop": {"Key": "loop", "Decription": "Loop video frames.", "Extra": "V->V ..."}, "lumakey": {"Key": "lumakey", "Decription": "Turns a certain luma into transparency.", "Extra": "V->V TSC"}, "lut": {"Key": "lut", "Decription": "Compute and apply a lookup table to the RGB/YUV input video.", "Extra": "V->V TSC"}, "lut1d": {"Key": "lut1d", "Decription": "Adjust colors using a 1D LUT.", "Extra": "V->V TSC"}, "lut2": {"Key": "lut2", "Decription": "Compute and apply a lookup table from two video inputs.", "Extra": "VV->V TSC"}, "lut3d": {"Key": "lut3d", "Decription": "Adjust colors using a 3D LUT.", "Extra": "V->V TSC"}, "lutrgb": {"Key": "lutrgb", "Decription": "Compute and apply a lookup table to the RGB input video.", "Extra": "V->V TSC"}, "lutyuv": {"Key": "lutyuv", "Decription": "Compute and apply a lookup table to the YUV input video.", "Extra": "V->V TSC"}, "maskedclamp": {"Key": "<PERSON>clamp", "Decription": "Clamp first stream with second stream and third stream.", "Extra": "VVV->V TSC"}, "maskedmax": {"Key": "masked<PERSON>", "Decription": "Apply filtering with maximum difference of two streams.", "Extra": "VVV->V TSC"}, "maskedmerge": {"Key": "maskedmerge", "Decription": "Merge first stream with second stream using third stream as mask.", "Extra": "VVV->V TSC"}, "maskedmin": {"Key": "<PERSON><PERSON>", "Decription": "Apply filtering with minimum difference of two streams.", "Extra": "VVV->V TSC"}, "maskedthreshold": {"Key": "<PERSON><PERSON><PERSON><PERSON>", "Decription": "Pick pixels comparing absolute difference of two streams with threshold.", "Extra": "VV->V TSC"}, "maskfun": {"Key": "maskfun", "Decription": "Create Mask.", "Extra": "V->V TSC"}, "median": {"Key": "median", "Decription": "Apply Median filter.", "Extra": "V->V TSC"}, "mestimate": {"Key": "mestimate", "Decription": "Generate motion vectors.", "Extra": "V->V ..."}, "metadata": {"Key": "metadata", "Decription": "Manipulate video frame metadata.", "Extra": "V->V T.."}, "midequalizer": {"Key": "midequalizer", "Decription": "Apply Midway Equalization.", "Extra": "VV->V T.."}, "minterpolate": {"Key": "minterpolate", "Decription": "Frame rate conversion using Motion Interpolation.", "Extra": "V->V ..."}, "monochrome": {"Key": "monochrome", "Decription": "Convert video to gray using custom color filter.", "Extra": "V->V TSC"}, "morpho": {"Key": "morpho", "Decription": "Apply Morphological filter.", "Extra": "VV->V T.C"}, "mpdecimate": {"Key": "mpdecimate", "Decription": "Remove near-duplicate frames.", "Extra": "V->V ..."}, "msad": {"Key": "msad", "Decription": "Calculate the MSAD between two video streams.", "Extra": "VV->V TS."}, "multiply": {"Key": "multiply", "Decription": "Multiply first video stream with second video stream.", "Extra": "VV->V TSC"}, "negate": {"Key": "negate", "Decription": "Negate input video.", "Extra": "V->V TSC"}, "nlmeans": {"Key": "nlmeans", "Decription": "Non-local means denoiser.", "Extra": "V->V TS."}, "nlmeans_opencl": {"Key": "nlmeans_opencl", "Decription": "Non-local means denoiser through OpenCL", "Extra": "V->V ..."}, "nnedi": {"Key": "nnedi", "Decription": "Apply neural network edge directed interpolation intra-only deinterlacer.", "Extra": "V->V TSC"}, "noformat": {"Key": "noformat", "Decription": "Force libavfilter not to use any of the specified pixel formats for the input to the next filter.", "Extra": "V->V ..."}, "noise": {"Key": "noise", "Decription": "Add noise.", "Extra": "V->V TS."}, "normalize": {"Key": "normalize", "Decription": "Normalize RGB video.", "Extra": "V->V T.C"}, "null": {"Key": "null", "Decription": "Pass the source unchanged to the output.", "Extra": "V->V ..."}, "ocr": {"Key": "ocr", "Decription": "Optical Character Recognition.", "Extra": "V->V ..."}, "oscilloscope": {"Key": "oscilloscope", "Decription": "2D Video Oscilloscope.", "Extra": "V->V T.C"}, "overlay": {"Key": "overlay", "Decription": "Overlay a video source on top of the input.", "Extra": "VV->V TSC"}, "overlay_opencl": {"Key": "overlay_opencl", "Decription": "Overlay one video on top of another", "Extra": "VV->V ..."}, "overlay_qsv": {"Key": "overlay_qsv", "Decription": "Quick Sync Video overlay.", "Extra": "VV->V ..."}, "overlay_vaapi": {"Key": "overlay_vaapi", "Decription": "Overlay one video on top of another", "Extra": "VV->V ..."}, "overlay_cuda": {"Key": "overlay_cuda", "Decription": "Overlay one video on top of another using CUDA", "Extra": "VV->V ..."}, "overlaygraphicsubs": {"Key": "overlaygraphicsubs", "Decription": "Overlay graphical subtitles on top of the input.", "Extra": "VS->V ..."}, "overlaytextsubs": {"Key": "overlaytextsubs", "Decription": "Overlay textual subtitles on top of the input.", "Extra": "VS->V ..."}, "owdenoise": {"Key": "owden<PERSON>", "Decription": "<PERSON><PERSON> using wavelets.", "Extra": "V->V T.."}, "pad": {"Key": "pad", "Decription": "Pad the input video.", "Extra": "V->V ..."}, "pad_opencl": {"Key": "pad_opencl", "Decription": "Pad the input video.", "Extra": "V->V ..."}, "palettegen": {"Key": "palettegen", "Decription": "Find the optimal palette for a given stream.", "Extra": "V->V ..."}, "paletteuse": {"Key": "paletteuse", "Decription": "Use a palette to downsample an input video stream.", "Extra": "VV->V ..."}, "perms": {"Key": "perms", "Decription": "Set permissions for the output video frame.", "Extra": "V->V T.C"}, "perspective": {"Key": "perspective", "Decription": "Correct the perspective of video.", "Extra": "V->V TS."}, "phase": {"Key": "phase", "Decription": "Phase shift fields.", "Extra": "V->V T.C"}, "photosensitivity": {"Key": "photosensitivity", "Decription": "Filter out photosensitive epilepsy seizure-inducing flashes.", "Extra": "V->V ..."}, "pixdesctest": {"Key": "pixdesctest", "Decription": "Test pixel format definitions.", "Extra": "V->V ..."}, "pixelize": {"Key": "pixelize", "Decription": "Pixelize video.", "Extra": "V->V TSC"}, "pixscope": {"Key": "pixscope", "Decription": "Pixel data analysis.", "Extra": "V->V T.C"}, "pp": {"Key": "pp", "Decription": "Filter video using libpostproc.", "Extra": "V->V T.C"}, "pp7": {"Key": "pp7", "Decription": "Apply Postprocessing 7 filter.", "Extra": "V->V T.."}, "prewitt": {"Key": "<PERSON><PERSON><PERSON>", "Decription": "Apply prewitt operator.", "Extra": "V->V TSC"}, "prewitt_opencl": {"Key": "prewitt_opencl", "Decription": "Apply prewitt operator", "Extra": "V->V ..."}, "procamp_vaapi": {"Key": "procamp_vaapi", "Decription": "ProcAmp (color balance) adjustments for hue, saturation, brightness, contrast", "Extra": "V->V ..."}, "pseudocolor": {"Key": "pseudocolor", "Decription": "Make pseudocolored video frames.", "Extra": "V->V TSC"}, "psnr": {"Key": "psnr", "Decription": "Calculate the PSNR between two video streams.", "Extra": "VV->V TS."}, "pullup": {"Key": "pullup", "Decription": "Pullup from field sequence to frames.", "Extra": "V->V ..."}, "qp": {"Key": "qp", "Decription": "Change video quantization parameters.", "Extra": "V->V T.."}, "random": {"Key": "random", "Decription": "Return random frames.", "Extra": "V->V ..."}, "readeia608": {"Key": "readeia608", "Decription": "Read EIA-608 Closed Caption codes from input video and write them to frame metadata.", "Extra": "V->V TSC"}, "readvitc": {"Key": "readvitc", "Decription": "Read vertical interval timecode and write it to frame metadata.", "Extra": "V->V ..."}, "realtime": {"Key": "realtime", "Decription": "Slow down filtering to match realtime.", "Extra": "V->V ..C"}, "remap": {"Key": "remap", "Decription": "Remap pixels.", "Extra": "VVV->V .S."}, "remap_opencl": {"Key": "remap_opencl", "Decription": "Remap pixels using OpenCL.", "Extra": "VVV->V ..."}, "removegrain": {"Key": "removegrain", "Decription": "Remove grain.", "Extra": "V->V TS."}, "removelogo": {"Key": "removelogo", "Decription": "Remove a TV logo based on a mask image.", "Extra": "V->V T.."}, "repeatfields": {"Key": "repeatfields", "Decription": "Hard repeat fields based on MPEG repeat field flag.", "Extra": "V->V ..."}, "reverse": {"Key": "reverse", "Decription": "Reverse a clip.", "Extra": "V->V ..."}, "rgbashift": {"Key": "rgbashift", "Decription": "Shift RGBA.", "Extra": "V->V TSC"}, "roberts": {"Key": "roberts", "Decription": "Apply roberts cross operator.", "Extra": "V->V TSC"}, "roberts_opencl": {"Key": "roberts_opencl", "Decription": "Apply roberts operator", "Extra": "V->V ..."}, "rotate": {"Key": "rotate", "Decription": "Rotate the input image.", "Extra": "V->V TSC"}, "sab": {"Key": "sab", "Decription": "Apply shape adaptive blur.", "Extra": "V->V T.."}, "scale": {"Key": "scale", "Decription": "Scale the input video size and/or convert the image format.", "Extra": "V->V ..C"}, "scale_cuda": {"Key": "scale_cuda", "Decription": "GPU accelerated video resizer", "Extra": "V->V ..."}, "scale_qsv": {"Key": "scale_qsv", "Decription": "QuickSync video scaling and format conversion", "Extra": "V->V ..."}, "scale_vaapi": {"Key": "scale_vaapi", "Decription": "Scale to/from VAAPI surfaces.", "Extra": "V->V ..."}, "scale2ref": {"Key": "scale2ref", "Decription": "Scale the input video size and/or convert the image format to the given reference.", "Extra": "VV->VV ..C"}, "scdet": {"Key": "scdet", "Decription": "Detect video scene change", "Extra": "V->V ..."}, "scharr": {"Key": "scharr", "Decription": "Apply scharr operator.", "Extra": "V->V TSC"}, "scroll": {"Key": "scroll", "Decription": "Scroll input video.", "Extra": "V->V TSC"}, "segment": {"Key": "segment", "Decription": "Segment video stream.", "Extra": "V->N ..."}, "select": {"Key": "select", "Decription": "Select video frames to pass in output.", "Extra": "V->N ..."}, "selectivecolor": {"Key": "selectivecolor", "Decription": "Apply CMYK adjustments to specific color ranges.", "Extra": "V->V TS."}, "sendcmd": {"Key": "sendcmd", "Decription": "Send commands to filters.", "Extra": "V->V ..."}, "separatefields": {"Key": "separatefields", "Decription": "Split input video frames into fields.", "Extra": "V->V ..."}, "setdar": {"Key": "setdar", "Decription": "Set the frame display aspect ratio.", "Extra": "V->V ..."}, "setfield": {"Key": "setfield", "Decription": "Force field for the output video frame.", "Extra": "V->V ..."}, "setparams": {"Key": "set<PERSON><PERSON>", "Decription": "Force field, or color property for the output video frame.", "Extra": "V->V ..."}, "setpts": {"Key": "setpts", "Decription": "Set PTS for the output video frame.", "Extra": "V->V ..."}, "setrange": {"Key": "setrange", "Decription": "Force color range for the output video frame.", "Extra": "V->V ..."}, "setsar": {"Key": "setsar", "Decription": "Set the pixel sample aspect ratio.", "Extra": "V->V ..."}, "settb": {"Key": "settb", "Decription": "Set timebase for the video output link.", "Extra": "V->V ..."}, "sharpness_vaapi": {"Key": "sharpness_vaapi", "Decription": "VAAPI VPP for sharpness", "Extra": "V->V ..."}, "shear": {"Key": "shear", "Decription": "Shear transform the input image.", "Extra": "V->V TSC"}, "showinfo": {"Key": "showinfo", "Decription": "Show textual information for each video frame.", "Extra": "V->V ..."}, "showpalette": {"Key": "showpalette", "Decription": "Display frame palette.", "Extra": "V->V ..."}, "shuffleframes": {"Key": "shuffleframes", "Decription": "Shuffle video frames.", "Extra": "V->V T.."}, "shufflepixels": {"Key": "shufflepixels", "Decription": "Shuffle video pixels.", "Extra": "V->V TS."}, "shuffleplanes": {"Key": "shuffleplanes", "Decription": "Shuffle video planes.", "Extra": "V->V T.."}, "sidedata": {"Key": "<PERSON>ata", "Decription": "Manipulate video frame side data.", "Extra": "V->V T.."}, "signalstats": {"Key": "signalstats", "Decription": "Generate statistics from video analysis.", "Extra": "V->V .S."}, "siti": {"Key": "siti", "Decription": "Calculate spatial information (SI) and temporal information (TI).", "Extra": "V->V ..."}, "smartblur": {"Key": "smartblur", "Decription": "Blur the input video without impacting the outlines.", "Extra": "V->V T.."}, "sobel": {"Key": "sobel", "Decription": "Apply sobel operator.", "Extra": "V->V TSC"}, "sobel_opencl": {"Key": "sobel_opencl", "Decription": "Apply sobel operator", "Extra": "V->V ..."}, "split": {"Key": "split", "Decription": "Pass on the input to N video outputs.", "Extra": "V->N ..."}, "spp": {"Key": "spp", "Decription": "Apply a simple post processing filter.", "Extra": "V->V T.C"}, "sr": {"Key": "sr", "Decription": "Apply DNN-based image super resolution to the input.", "Extra": "V->V ..."}, "ssim": {"Key": "ssim", "Decription": "Calculate the SSIM between two video streams.", "Extra": "VV->V TS."}, "stereo3d": {"Key": "stereo3d", "Decription": "Convert video stereoscopic 3D view.", "Extra": "V->V .S."}, "subtitles": {"Key": "subtitles", "Decription": "Render text subtitles onto input video using the libass library.", "Extra": "V->V ..."}, "super2xsai": {"Key": "super2xsai", "Decription": "Scale the input by 2x using the Super2xSaI pixel art algorithm.", "Extra": "V->V .S."}, "superscale_cuda": {"Key": "superscale_cuda", "Decription": "GPU accelerated video resizer", "Extra": "V->V ..."}, "supertonemap_opencl": {"Key": "supertonemap_opencl", "Decription": "perform HDR to SDR conversion with tonemapping", "Extra": "V->V ..."}, "swaprect": {"Key": "swaprect", "Decription": "Swap 2 rectangular objects in video.", "Extra": "V->V T.C"}, "swapuv": {"Key": "swapuv", "Decription": "Swap U and V components.", "Extra": "V->V T.."}, "tblend": {"Key": "tblend", "Decription": "Blend successive frames.", "Extra": "V->V TSC"}, "telecine": {"Key": "telecine", "Decription": "Apply a telecine pattern.", "Extra": "V->V ..."}, "thistogram": {"Key": "thistogram", "Decription": "Compute and draw a temporal histogram.", "Extra": "V->V ..."}, "threshold": {"Key": "threshold", "Decription": "Threshold first video stream using other video streams.", "Extra": "VVVV->V TSC"}, "thumbnail": {"Key": "thumbnail", "Decription": "Select the most representative frame in a given sequence of consecutive frames.", "Extra": "V->V T.."}, "thumbnail_cuda": {"Key": "thumbnail_cuda", "Decription": "Select the most representative frame in a given sequence of consecutive frames.", "Extra": "V->V ..."}, "tile": {"Key": "tile", "Decription": "Tile several successive frames together.", "Extra": "V->V ..."}, "tinterlace": {"Key": "tinterlace", "Decription": "Perform temporal field interlacing.", "Extra": "V->V ..."}, "tlut2": {"Key": "tlut2", "Decription": "Compute and apply a lookup table from two successive frames.", "Extra": "V->V TSC"}, "tmedian": {"Key": "tmedian", "Decription": "Pick median pixels from successive frames.", "Extra": "V->V TSC"}, "tmidequalizer": {"Key": "tmidequalizer", "Decription": "Apply Temporal Midway Equalization.", "Extra": "V->V T.."}, "tmix": {"Key": "tmix", "Decription": "Mix successive video frames.", "Extra": "V->V TSC"}, "supertonemap": {"Key": "supertonemap", "Decription": "Fast conversion to/from different dynamic ranges.", "Extra": "V->V .S."}, "tonemap": {"Key": "tonemap", "Decription": "Conversion to/from different dynamic ranges.", "Extra": "V->V .S."}, "tonemap_cuda": {"Key": "tonemap_cuda", "Decription": "GPU accelerated HDR-to-SDR tone mapping", "Extra": "V->V ..."}, "tonemap_opencl": {"Key": "tonemap_opencl", "Decription": "Perform HDR to SDR conversion with tonemapping.", "Extra": "V->V ..."}, "tonemap_vaapi": {"Key": "tonemap_vaapi", "Decription": "VAAPI VPP for tone-mapping", "Extra": "V->V ..."}, "tpad": {"Key": "tpad", "Decription": "Temporarily pad video frames.", "Extra": "V->V ..."}, "transpose": {"Key": "transpose", "Decription": "Transpose input video.", "Extra": "V->V .S."}, "transpose_opencl": {"Key": "transpose_opencl", "Decription": "Transpose input video", "Extra": "V->V ..."}, "transpose_vaapi": {"Key": "transpose_vaapi", "Decription": "VAAPI VPP for transpose", "Extra": "V->V ..."}, "trim": {"Key": "trim", "Decription": "Pick one continuous section from the input, drop the rest.", "Extra": "V->V ..."}, "unsharp": {"Key": "unsharp", "Decription": "Sharpen or blur the input video.", "Extra": "V->V TS."}, "unsharp_opencl": {"Key": "unsharp_opencl", "Decription": "Apply unsharp mask to input video", "Extra": "V->V ..."}, "untile": {"Key": "untile", "Decription": "Untile a frame into a sequence of frames.", "Extra": "V->V ..."}, "v360": {"Key": "v360", "Decription": "Convert 360 projection of video.", "Extra": "V->V .SC"}, "vaguedenoiser": {"Key": "vaguedenoiser", "Decription": "Apply a Wavelet based Denoiser.", "Extra": "V->V T.."}, "varblur": {"Key": "<PERSON><PERSON><PERSON><PERSON>", "Decription": "Apply Variable Blur filter.", "Extra": "VV->V TSC"}, "vectorscope": {"Key": "vectorscope", "Decription": "Video vectorscope.", "Extra": "V->V ..C"}, "vflip": {"Key": "vflip", "Decription": "Flip the input video vertically.", "Extra": "V->V T.."}, "vfrdet": {"Key": "vfrdet", "Decription": "Variable frame rate detect filter.", "Extra": "V->V ..."}, "vibrance": {"Key": "vibrance", "Decription": "Boost or alter saturation.", "Extra": "V->V TSC"}, "vif": {"Key": "vif", "Decription": "Calculate the VIF between two video streams.", "Extra": "VV->V TS."}, "vignette": {"Key": "vignette", "Decription": "Make or reverse a vignette effect.", "Extra": "V->V T.."}, "vmafmotion": {"Key": "vmafmotion", "Decription": "Calculate the VMAF Motion score.", "Extra": "V->V ..."}, "vpp_qsv": {"Key": "vpp_qsv", "Decription": "Quick Sync Video VPP.", "Extra": "V->V ..."}, "w3fdif": {"Key": "w3fdif", "Decription": "Apply <PERSON> three field deinterlace.", "Extra": "V->V TSC"}, "waveform": {"Key": "waveform", "Decription": "Video waveform monitor.", "Extra": "V->V .SC"}, "weave": {"Key": "weave", "Decription": "Weave input video fields into frames.", "Extra": "V->V .S."}, "xbr": {"Key": "xbr", "Decription": "Scale the input using xBR algorithm.", "Extra": "V->V .S."}, "xcorrelate": {"Key": "xcorrelate", "Decription": "Cross-correlate first video stream with second video stream.", "Extra": "VV->V TS."}, "xfade": {"Key": "xfade", "Decription": "Cross fade one video with another video.", "Extra": "VV->V .S."}, "xfade_opencl": {"Key": "xfade_opencl", "Decription": "Cross fade one video with another video.", "Extra": "VV->V ..."}, "yadif": {"Key": "yadif", "Decription": "Deinterlace the input image.", "Extra": "V->V TS."}, "yadif_cuda": {"Key": "yadif_cuda", "Decription": "Deinterlace CUDA frames", "Extra": "V->V T.."}, "yaepblur": {"Key": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Decription": "Yet another edge preserving blur filter.", "Extra": "V->V TSC"}, "zoompan": {"Key": "zoompan", "Decription": "Apply Zoom & Pan effect.", "Extra": "V->V ..."}, "nullsink": {"Key": "nullsink", "Decription": "Do absolutely nothing with the input video.", "Extra": "V->| ..."}, "spectrumsynth": {"Key": "spectrumsynth", "Decription": "Convert input spectrum videos to audio output.", "Extra": "VV->A ..."}, "splitcc": {"Key": "splitcc", "Decription": "Extract closed-caption (A53) data from video as subtitle stream.", "Extra": "V->VS ..."}, "fifo": {"Key": "fifo", "Decription": "<PERSON><PERSON><PERSON> input images and send them when they are requested.", "Extra": "V->V ..."}, "buffersink": {"Key": "buffersink", "Decription": "Buffer video frames, and make them available to the end of the filter graph.", "Extra": "V->| ..."}}, "AudioFilters": {"abench": {"Key": "abench", "Decription": "Benchmark part of a filtergraph.", "Extra": "A->A ..."}, "acompressor": {"Key": "acompressor", "Decription": "Audio compressor.", "Extra": "A->A ..C"}, "acontrast": {"Key": "acontrast", "Decription": "Simple audio dynamic range compression/expansion filter.", "Extra": "A->A ..."}, "acopy": {"Key": "acopy", "Decription": "Copy the input audio unchanged to the output.", "Extra": "A->A ..."}, "acue": {"Key": "acue", "Decription": "Delay filtering to match a cue.", "Extra": "A->A ..."}, "acrossfade": {"Key": "acrossfade", "Decription": "Cross fade two input audio streams.", "Extra": "AA->A ..."}, "acrossover": {"Key": "acrossover", "Decription": "Split audio into per-bands streams.", "Extra": "A->N .S."}, "acrusher": {"Key": "acrusher", "Decription": "Reduce audio bit resolution.", "Extra": "A->A T.C"}, "adeclick": {"Key": "adeclick", "Decription": "Remove impulsive noise from input audio.", "Extra": "A->A TS."}, "adeclip": {"Key": "adeclip", "Decription": "Remove clipping from input audio.", "Extra": "A->A TS."}, "adecorrelate": {"Key": "adecorrelate", "Decription": "Apply decorrelation to input audio.", "Extra": "A->A TS."}, "adelay": {"Key": "adelay", "Decription": "Delay one or more audio channels.", "Extra": "A->A T.C"}, "adenorm": {"Key": "adenorm", "Decription": "Remedy denormals by adding extremely low-level noise.", "Extra": "A->A TSC"}, "aderivative": {"Key": "aderivative", "Decription": "Compute derivative of input audio.", "Extra": "A->A T.."}, "adynamicequalizer": {"Key": "adynamicequalizer", "Decription": "Apply Dynamic Equalization of input audio.", "Extra": "A->A TSC"}, "adynamicsmooth": {"Key": "adynamicsmooth", "Decription": "Apply Dynamic Smoothing of input audio.", "Extra": "A->A T.C"}, "aecho": {"Key": "aecho", "Decription": "Add echoing to the audio.", "Extra": "A->A ..."}, "aemphasis": {"Key": "aemphasis", "Decription": "Audio emphasis.", "Extra": "A->A TSC"}, "aeval": {"Key": "aeval", "Decription": "Filter audio signal according to a specified expression.", "Extra": "A->A T.."}, "aexciter": {"Key": "aexciter", "Decription": "Enhance high frequency part of audio.", "Extra": "A->A T.C"}, "afade": {"Key": "afade", "Decription": "Fade in/out input audio.", "Extra": "A->A T.C"}, "afftdn": {"Key": "afftdn", "Decription": "Denoise audio samples using FFT.", "Extra": "A->A TSC"}, "afftfilt": {"Key": "afftfilt", "Decription": "Apply arbitrary expressions to samples in frequency domain.", "Extra": "A->A TS."}, "aformat": {"Key": "aformat", "Decription": "Convert the input audio to one of the specified formats.", "Extra": "A->A ..."}, "afreqshift": {"Key": "afreqshift", "Decription": "Apply frequency shifting to input audio.", "Extra": "A->A TSC"}, "afwtdn": {"Key": "afwtdn", "Decription": "Denoise audio stream using Wavelets.", "Extra": "A->A TSC"}, "agate": {"Key": "agate", "Decription": "Audio gate.", "Extra": "A->A T.C"}, "aiir": {"Key": "aiir", "Decription": "Apply Infinite Impulse Response filter with supplied coefficients.", "Extra": "A->N .S."}, "aintegral": {"Key": "<PERSON><PERSON><PERSON><PERSON>", "Decription": "Compute integral of input audio.", "Extra": "A->A T.."}, "alatency": {"Key": "alatency", "Decription": "Report audio filtering latency.", "Extra": "A->A T.."}, "alimiter": {"Key": "alimiter", "Decription": "Audio lookahead limiter.", "Extra": "A->A T.C"}, "allpass": {"Key": "allpass", "Decription": "Apply a two-pole all-pass filter.", "Extra": "A->A TSC"}, "aloop": {"Key": "al<PERSON>", "Decription": "Loop audio samples.", "Extra": "A->A ..."}, "ametadata": {"Key": "ametadata", "Decription": "Manipulate audio frame metadata.", "Extra": "A->A T.."}, "amultiply": {"Key": "<PERSON><PERSON><PERSON><PERSON>", "Decription": "Multiply two audio streams.", "Extra": "AA->A ..."}, "anequalizer": {"Key": "anequalizer", "Decription": "Apply high-order audio parametric multi band equalizer.", "Extra": "A->N TSC"}, "anlmdn": {"Key": "anlmdn", "Decription": "Reduce broadband noise from stream using Non-Local Means.", "Extra": "A->A TSC"}, "anlmf": {"Key": "anlmf", "Decription": "Apply Normalized Least-Mean-Fourth algorithm to first audio stream.", "Extra": "AA->A TSC"}, "anlms": {"Key": "anlms", "Decription": "Apply Normalized Least-Mean-Squares algorithm to first audio stream.", "Extra": "AA->A TSC"}, "anull": {"Key": "anull", "Decription": "Pass the source unchanged to the output.", "Extra": "A->A ..."}, "apad": {"Key": "apad", "Decription": "Pad audio with silence.", "Extra": "A->A T.."}, "aperms": {"Key": "aperms", "Decription": "Set permissions for the output audio frame.", "Extra": "A->A T.C"}, "aphaser": {"Key": "aphaser", "Decription": "Add a phasing effect to the audio.", "Extra": "A->A ..."}, "aphaseshift": {"Key": "aphaseshift", "Decription": "Apply phase shifting to input audio.", "Extra": "A->A TSC"}, "apsyclip": {"Key": "apsyclip", "Decription": "Audio Psychoacoustic Clipper.", "Extra": "A->A TSC"}, "apulsator": {"Key": "apulsator", "Decription": "Audio pulsator.", "Extra": "A->A ..."}, "arealtime": {"Key": "arealtime", "Decription": "Slow down filtering to match realtime.", "Extra": "A->A ..C"}, "aresample": {"Key": "aresample", "Decription": "Resample audio data.", "Extra": "A->A ..."}, "areverse": {"Key": "areverse", "Decription": "Reverse an audio clip.", "Extra": "A->A ..."}, "arnndn": {"Key": "arn<PERSON>n", "Decription": "Reduce noise from speech using Recurrent Neural Networks.", "Extra": "A->A TSC"}, "asdr": {"Key": "asdr", "Decription": "Measure Audio Signal-to-Distortion Ratio.", "Extra": "AA->A ..."}, "asegment": {"Key": "asegment", "Decription": "Segment audio stream.", "Extra": "A->N ..."}, "aselect": {"Key": "aselect", "Decription": "Select audio frames to pass in output.", "Extra": "A->N ..."}, "asendcmd": {"Key": "asendcmd", "Decription": "Send commands to filters.", "Extra": "A->A ..."}, "asetnsamples": {"Key": "asetnsamples", "Decription": "Set the number of samples for each output audio frames.", "Extra": "A->A ..."}, "asetpts": {"Key": "asetpts", "Decription": "Set PTS for the output audio frame.", "Extra": "A->A ..."}, "asetrate": {"Key": "asetrate", "Decription": "Change the sample rate without altering the data.", "Extra": "A->A ..."}, "asettb": {"Key": "asettb", "Decription": "Set timebase for the audio output link.", "Extra": "A->A ..."}, "ashowinfo": {"Key": "ashowinfo", "Decription": "Show textual information for each audio frame.", "Extra": "A->A ..."}, "asidedata": {"Key": "asidedata", "Decription": "Manipulate audio frame side data.", "Extra": "A->A T.."}, "asoftclip": {"Key": "asoftclip", "Decription": "Audio Soft Clipper.", "Extra": "A->A TSC"}, "aspectralstats": {"Key": "aspectralstats", "Decription": "Show frequency domain statistics about audio frames.", "Extra": "A->A .S."}, "asplit": {"Key": "asplit", "Decription": "Pass on the audio input to N audio outputs.", "Extra": "A->N ..."}, "astats": {"Key": "astats", "Decription": "Show time domain statistics about audio frames.", "Extra": "A->A .S."}, "asubboost": {"Key": "asubboost", "Decription": "Boost subwoofer frequencies.", "Extra": "A->A TSC"}, "asubcut": {"Key": "asubcut", "Decription": "Cut subwoofer frequencies.", "Extra": "A->A TSC"}, "asupercut": {"Key": "asupercut", "Decription": "Cut super frequencies.", "Extra": "A->A TSC"}, "asuperpass": {"Key": "as<PERSON><PERSON><PERSON>", "Decription": "Apply high order Butterworth band-pass filter.", "Extra": "A->A TSC"}, "asuperstop": {"Key": "<PERSON><PERSON><PERSON><PERSON>", "Decription": "Apply high order Butterworth band-stop filter.", "Extra": "A->A TSC"}, "atempo": {"Key": "atempo", "Decription": "Adjust audio tempo.", "Extra": "A->A ..C"}, "atilt": {"Key": "atilt", "Decription": "Apply spectral tilt to audio.", "Extra": "A->A TSC"}, "atrim": {"Key": "atrim", "Decription": "Pick one continuous section from the input, drop the rest.", "Extra": "A->A ..."}, "axcorrelate": {"Key": "axcorrelate", "Decription": "Cross-correlate two audio streams.", "Extra": "AA->A ..."}, "bandpass": {"Key": "bandpass", "Decription": "Apply a two-pole Butterworth band-pass filter.", "Extra": "A->A TSC"}, "bandreject": {"Key": "bandreject", "Decription": "Apply a two-pole Butterworth band-reject filter.", "Extra": "A->A TSC"}, "bass": {"Key": "bass", "Decription": "Boost or cut lower frequencies.", "Extra": "A->A TSC"}, "biquad": {"Key": "biquad", "Decription": "Apply a biquad IIR filter with the given coefficients.", "Extra": "A->A TSC"}, "channelmap": {"Key": "channelmap", "Decription": "Remap audio channels.", "Extra": "A->A ..."}, "channelsplit": {"Key": "channelsplit", "Decription": "Split audio into per-channel streams.", "Extra": "A->N ..."}, "chorus": {"Key": "chorus", "Decription": "Add a chorus effect to the audio.", "Extra": "A->A ..."}, "compand": {"Key": "compand", "Decription": "Compress or expand audio dynamic range.", "Extra": "A->A ..."}, "compensationdelay": {"Key": "compensationdelay", "Decription": "Audio Compensation Delay Line.", "Extra": "A->A T.C"}, "crossfeed": {"Key": "crossfeed", "Decription": "Apply headphone crossfeed filter.", "Extra": "A->A T.C"}, "crystalizer": {"Key": "crystalizer", "Decription": "Simple audio noise sharpening filter.", "Extra": "A->A TSC"}, "dcshift": {"Key": "dcshift", "Decription": "Apply a DC shift to the audio.", "Extra": "A->A T.."}, "deesser": {"Key": "<PERSON><PERSON>er", "Decription": "Apply de-essing to the audio.", "Extra": "A->A T.."}, "dialoguenhance": {"Key": "dialoguenhance", "Decription": "Audio Dialogue Enhancement.", "Extra": "A->A T.C"}, "drmeter": {"Key": "drmeter", "Decription": "Measure audio dynamic range.", "Extra": "A->A ..."}, "dynaudnorm": {"Key": "dynaudnorm", "Decription": "Dynamic Audio Normalizer.", "Extra": "A->A T.C"}, "earwax": {"Key": "earwax", "Decription": "Widen the stereo image.", "Extra": "A->A ..."}, "ebur128": {"Key": "ebur128", "Decription": "EBU R128 scanner.", "Extra": "A->N ..."}, "equalizer": {"Key": "equalizer", "Decription": "Apply two-pole peaking equalization (EQ) filter.", "Extra": "A->A TSC"}, "extrastereo": {"Key": "extrastereo", "Decription": "Increase difference between stereo audio channels.", "Extra": "A->A T.C"}, "firequalizer": {"Key": "firequalizer", "Decription": "Finite Impulse Response Equalizer.", "Extra": "A->A ..C"}, "flanger": {"Key": "flanger", "Decription": "Apply a flanging effect to the audio.", "Extra": "A->A ..."}, "haas": {"Key": "haas", "Decription": "Apply Haas Stereo Enhancer.", "Extra": "A->A ..."}, "hdcd": {"Key": "hdcd", "Decription": "Apply High Definition Compatible Digital (HDCD) decoding.", "Extra": "A->A ..."}, "highpass": {"Key": "highpass", "Decription": "Apply a high-pass filter with 3dB point frequency.", "Extra": "A->A TSC"}, "highshelf": {"Key": "highshelf", "Decription": "Apply a high shelf filter.", "Extra": "A->A TSC"}, "loudnorm": {"Key": "loudnorm", "Decription": "EBU R128 loudness normalization", "Extra": "A->A ..."}, "lowpass": {"Key": "lowpass", "Decription": "Apply a low-pass filter with 3dB point frequency.", "Extra": "A->A TSC"}, "lowshelf": {"Key": "lowshelf", "Decription": "Apply a low shelf filter.", "Extra": "A->A TSC"}, "mcompand": {"Key": "mcompand", "Decription": "Multiband Compress or expand audio dynamic range.", "Extra": "A->A ..."}, "pan": {"Key": "pan", "Decription": "Remix channels with coefficients (panning).", "Extra": "A->A ..."}, "replaygain": {"Key": "replaygain", "Decription": "ReplayGain scanner.", "Extra": "A->A ..."}, "sidechaincompress": {"Key": "sidechaincompress", "Decription": "Sidechain compressor.", "Extra": "AA->A ..C"}, "sidechaingate": {"Key": "sidechaingate", "Decription": "Audio sidechain gate.", "Extra": "AA->A T.C"}, "silencedetect": {"Key": "silencedetect", "Decription": "Detect silence.", "Extra": "A->A ..."}, "silenceremove": {"Key": "<PERSON><PERSON><PERSON>", "Decription": "Remove silence.", "Extra": "A->A ..."}, "speechnorm": {"Key": "speechnorm", "Decription": "Speech Normalizer.", "Extra": "A->A T.C"}, "stereotools": {"Key": "stereotools", "Decription": "Apply various stereo tools.", "Extra": "A->A T.C"}, "stereowiden": {"Key": "stereowiden", "Decription": "Apply stereo widening effect.", "Extra": "A->A T.C"}, "superequalizer": {"Key": "superequalizer", "Decription": "Apply 18 band equalization filter.", "Extra": "A->A ..."}, "surround": {"Key": "surround", "Decription": "Apply audio surround upmix filter.", "Extra": "A->A .S."}, "tiltshelf": {"Key": "tiltshelf", "Decription": "Apply a tilt shelf filter.", "Extra": "A->A TSC"}, "treble": {"Key": "treble", "Decription": "Boost or cut upper frequencies.", "Extra": "A->A TSC"}, "tremolo": {"Key": "tremolo", "Decription": "Apply tremolo effect.", "Extra": "A->A T.."}, "vibrato": {"Key": "vibrato", "Decription": "Apply vibrato effect.", "Extra": "A->A T.."}, "virtualbass": {"Key": "virtualbass", "Decription": "Audio Virtual Bass.", "Extra": "A->A T.C"}, "volume": {"Key": "volume", "Decription": "Change input volume.", "Extra": "A->A T.C"}, "volumedetect": {"Key": "volumedetect", "Decription": "Detect audio volume.", "Extra": "A->A ..."}, "anullsink": {"Key": "<PERSON><PERSON><PERSON><PERSON>", "Decription": "Do absolutely nothing with the input audio.", "Extra": "A->| ..."}, "abitscope": {"Key": "abitscope", "Decription": "Convert input audio to audio bit scope video output.", "Extra": "A->V ..."}, "adrawgraph": {"Key": "adrawgraph", "Decription": "Draw a graph using input audio metadata.", "Extra": "A->V ..."}, "agraphmonitor": {"Key": "agraphmonitor", "Decription": "Show various filtergraph stats.", "Extra": "A->V ..."}, "ahistogram": {"Key": "ahistogram", "Decription": "Convert input audio to histogram video output.", "Extra": "A->V ..."}, "aphasemeter": {"Key": "aphasemeter", "Decription": "Convert input audio to phase meter video output.", "Extra": "A->N ..."}, "avectorscope": {"Key": "avectorscope", "Decription": "Convert input audio to vectorscope video output.", "Extra": "A->V .SC"}, "showcqt": {"Key": "showcqt", "Decription": "Convert input audio to a CQT (Constant/Clamped Q Transform) spectrum video output.", "Extra": "A->V ..."}, "showfreqs": {"Key": "showfreqs", "Decription": "Convert input audio to a frequencies video output.", "Extra": "A->V ..."}, "showspatial": {"Key": "showspatial", "Decription": "Convert input audio to a spatial video output.", "Extra": "A->V .S."}, "showspectrum": {"Key": "showspectrum", "Decription": "Convert input audio to a spectrum video output.", "Extra": "A->V .S."}, "showspectrumpic": {"Key": "showspectrumpic", "Decription": "Convert input audio to a spectrum video output single picture.", "Extra": "A->V .S."}, "showvolume": {"Key": "showvolume", "Decription": "Convert input audio volume to video output.", "Extra": "A->V ..."}, "showwaves": {"Key": "showwaves", "Decription": "Convert input audio to a video output.", "Extra": "A->V ..."}, "showwavespic": {"Key": "showwavespic", "Decription": "Convert input audio to a video output single picture.", "Extra": "A->V ..."}, "afifo": {"Key": "afifo", "Decription": "Buffer input frames and send them when they are requested.", "Extra": "A->A ..."}, "abuffersink": {"Key": "abuffersink", "Decription": "Buffer audio frames, and make them available to the end of the filter graph.", "Extra": "A->| ..."}}, "SubtitleFilters": {"graphicsub2video": {"Key": "graphicsub2video", "Decription": "Convert graphical subtitles to video", "Extra": "S->V ..."}, "textsub2video": {"Key": "textsub2video", "Decription": "Convert textual subtitles to video frames", "Extra": "S->V ..."}, "censor": {"Key": "censor", "Decription": "Censor words in subtitle text", "Extra": "S->S ..."}, "graphicsub2text": {"Key": "graphicsub2text", "Decription": "Convert graphical subtitles to text subtitles via OCR", "Extra": "S->S ..."}, "showspeaker": {"Key": "showspeaker", "Decription": "Prepend speaker names to text subtitles (when available)", "Extra": "S->S ..."}, "snull": {"Key": "snull", "Decription": "Pass the source unchanged to the output.", "Extra": "S->S ..."}, "strim": {"Key": "strim", "Decription": "Pick one continuous section from the input, drop the rest.", "Extra": "S->S ..."}, "stripstyles": {"Key": "stripstyles", "Decription": "Strip subtitle inline styles", "Extra": "S->S ..."}, "subfeed": {"Key": "subfeed", "Decription": "Control subtitle frame timing and flow in a filtergraph", "Extra": "S->S ..."}, "subscale": {"Key": "subscale", "Decription": "Scale graphical subtitles.", "Extra": "S->S ..."}, "text2graphicsub": {"Key": "text2graphicsub", "Decription": "Convert text subtitles to bitmap subtitles.", "Extra": "S->S ..."}, "textmod": {"Key": "textmod", "Decription": "Modify subtitle text in several ways", "Extra": "S->S ..."}, "sbuffersink": {"Key": "sbuffersink", "Decription": "Buffer subtitle frames, and make them available to the end of the filter graph.", "Extra": "S->| ..."}}, "InputProtocols": {"async": {"Key": "async", "Decription": "async"}, "cache": {"Key": "cache", "Decription": "cache"}, "concat": {"Key": "concat", "Decription": "concat"}, "concatf": {"Key": "concatf", "Decription": "concatf"}, "crypto": {"Key": "crypto", "Decription": "crypto"}, "data": {"Key": "data", "Decription": "data"}, "ffrtmpcrypt": {"Key": "ffrtmpcrypt", "Decription": "ffrtmpcrypt"}, "ffrtmphttp": {"Key": "ffrtmphttp", "Decription": "ffrtmphttp"}, "file": {"Key": "file", "Decription": "file"}, "ftp": {"Key": "ftp", "Decription": "ftp"}, "gopher": {"Key": "gopher", "Decription": "gopher"}, "gophers": {"Key": "gophers", "Decription": "gophers"}, "hls": {"Key": "hls", "Decription": "hls"}, "http": {"Key": "http", "Decription": "http"}, "httpproxy": {"Key": "httpproxy", "Decription": "httpproxy"}, "https": {"Key": "https", "Decription": "https"}, "mmsh": {"Key": "mmsh", "Decription": "mmsh"}, "mmst": {"Key": "mmst", "Decription": "mmst"}, "pipe": {"Key": "pipe", "Decription": "pipe"}, "rtmp": {"Key": "rtmp", "Decription": "rtmp"}, "rtmpe": {"Key": "rtmpe", "Decription": "rtmpe"}, "rtmps": {"Key": "rtmps", "Decription": "rtmps"}, "rtmpt": {"Key": "rtmpt", "Decription": "rtmpt"}, "rtmpte": {"Key": "rtmpte", "Decription": "rtmpte"}, "rtmpts": {"Key": "rtmpts", "Decription": "rtmpts"}, "rtp": {"Key": "rtp", "Decription": "rtp"}, "srtp": {"Key": "srtp", "Decription": "srtp"}, "subfile": {"Key": "subfile", "Decription": "subfile"}, "tcp": {"Key": "tcp", "Decription": "tcp"}, "tls": {"Key": "tls", "Decription": "tls"}, "udp": {"Key": "udp", "Decription": "udp"}, "udplite": {"Key": "udplite", "Decription": "udplite"}, "unix": {"Key": "unix", "Decription": "unix"}, "ipfs": {"Key": "ipfs", "Decription": "ipfs"}, "ipns": {"Key": "ipns", "Decription": "ipns"}}, "OutputProtocols": {"crypto": {"Key": "crypto", "Decription": "crypto"}, "ffrtmpcrypt": {"Key": "ffrtmpcrypt", "Decription": "ffrtmpcrypt"}, "ffrtmphttp": {"Key": "ffrtmphttp", "Decription": "ffrtmphttp"}, "file": {"Key": "file", "Decription": "file"}, "ftp": {"Key": "ftp", "Decription": "ftp"}, "gopher": {"Key": "gopher", "Decription": "gopher"}, "gophers": {"Key": "gophers", "Decription": "gophers"}, "http": {"Key": "http", "Decription": "http"}, "httpproxy": {"Key": "httpproxy", "Decription": "httpproxy"}, "https": {"Key": "https", "Decription": "https"}, "icecast": {"Key": "icecast", "Decription": "icecast"}, "md5": {"Key": "md5", "Decription": "md5"}, "pipe": {"Key": "pipe", "Decription": "pipe"}, "prompeg": {"Key": "prompeg", "Decription": "prompeg"}, "rtmp": {"Key": "rtmp", "Decription": "rtmp"}, "rtmpe": {"Key": "rtmpe", "Decription": "rtmpe"}, "rtmps": {"Key": "rtmps", "Decription": "rtmps"}, "rtmpt": {"Key": "rtmpt", "Decription": "rtmpt"}, "rtmpte": {"Key": "rtmpte", "Decription": "rtmpte"}, "rtmpts": {"Key": "rtmpts", "Decription": "rtmpts"}, "rtp": {"Key": "rtp", "Decription": "rtp"}, "srtp": {"Key": "srtp", "Decription": "srtp"}, "tee": {"Key": "tee", "Decription": "tee"}, "tcp": {"Key": "tcp", "Decription": "tcp"}, "tls": {"Key": "tls", "Decription": "tls"}, "udp": {"Key": "udp", "Decription": "udp"}, "udplite": {"Key": "udplite", "Decription": "udplite"}, "unix": {"Key": "unix", "Decription": "unix"}}, "HardwareAccelerations": {"cuda": {"Key": "cuda", "Decription": "cuda"}, "vaapi": {"Key": "vaapi", "Decription": "vaapi"}, "qsv": {"Key": "qsv", "Decription": "qsv"}, "drm": {"Key": "drm", "Decription": "drm"}, "opencl": {"Key": "opencl", "Decription": "opencl"}}, "SupportsThrottling": true, "SupportsFillWallClockDts": true, "SupportsDiscardCorruptPts": true, "SupportsSegmentCompleteLogMessages": true, "SupportsSubtitleFrameProbeOutput": true}}