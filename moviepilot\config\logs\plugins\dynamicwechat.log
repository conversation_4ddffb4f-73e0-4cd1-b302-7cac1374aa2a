【DEBUG】2025-06-03 20:12:49,516 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.forced_change
【DEBUG】2025-06-03 20:12:49,516 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.local_scanning
【DEBUG】2025-06-03 20:12:49,517 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.write_wan2_ip
【DEBUG】2025-06-03 20:12:49,517 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.check
【DEBUG】2025-06-03 20:12:49,518 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.push_qr_code
【DEBUG】2025-06-03 20:12:49,518 - event.py - Subscribed to broadcast event: user.message - app.plugins.dynamicwechat.DynamicWeChat.talk
【DEBUG】2025-06-03 22:23:50,322 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.forced_change
【DEBUG】2025-06-03 22:23:50,323 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.local_scanning
【DEBUG】2025-06-03 22:23:50,323 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.write_wan2_ip
【DEBUG】2025-06-03 22:23:50,324 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.check
【DEBUG】2025-06-03 22:23:50,324 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.push_qr_code
【DEBUG】2025-06-03 22:23:50,325 - event.py - Subscribed to broadcast event: user.message - app.plugins.dynamicwechat.DynamicWeChat.talk
【DEBUG】2025-06-04 10:23:22,397 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.forced_change
【DEBUG】2025-06-04 10:23:22,398 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.local_scanning
【DEBUG】2025-06-04 10:23:22,398 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.write_wan2_ip
【DEBUG】2025-06-04 10:23:22,398 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.check
【DEBUG】2025-06-04 10:23:22,399 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.push_qr_code
【DEBUG】2025-06-04 10:23:22,399 - event.py - Subscribed to broadcast event: user.message - app.plugins.dynamicwechat.DynamicWeChat.talk
【DEBUG】2025-06-04 11:23:13,591 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.forced_change
【DEBUG】2025-06-04 11:23:13,591 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.local_scanning
【DEBUG】2025-06-04 11:23:13,592 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.write_wan2_ip
【DEBUG】2025-06-04 11:23:13,592 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.check
【DEBUG】2025-06-04 11:23:13,592 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.push_qr_code
【DEBUG】2025-06-04 11:23:13,593 - event.py - Subscribed to broadcast event: user.message - app.plugins.dynamicwechat.DynamicWeChat.talk
【DEBUG】2025-06-04 16:50:11,961 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.forced_change
【DEBUG】2025-06-04 16:50:11,962 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.local_scanning
【DEBUG】2025-06-04 16:50:11,962 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.write_wan2_ip
【DEBUG】2025-06-04 16:50:11,963 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.check
【DEBUG】2025-06-04 16:50:11,963 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.push_qr_code
【DEBUG】2025-06-04 16:50:11,963 - event.py - Subscribed to broadcast event: user.message - app.plugins.dynamicwechat.DynamicWeChat.talk
【DEBUG】2025-06-04 23:14:03,607 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.forced_change
【DEBUG】2025-06-04 23:14:03,607 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.local_scanning
【DEBUG】2025-06-04 23:14:03,607 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.write_wan2_ip
【DEBUG】2025-06-04 23:14:03,608 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.check
【DEBUG】2025-06-04 23:14:03,608 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.push_qr_code
【DEBUG】2025-06-04 23:14:03,608 - event.py - Subscribed to broadcast event: user.message - app.plugins.dynamicwechat.DynamicWeChat.talk
【DEBUG】2025-06-04 23:14:59,148 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.forced_change
【DEBUG】2025-06-04 23:14:59,149 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.local_scanning
【DEBUG】2025-06-04 23:14:59,149 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.write_wan2_ip
【DEBUG】2025-06-04 23:14:59,149 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.check
【DEBUG】2025-06-04 23:14:59,149 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.push_qr_code
【DEBUG】2025-06-04 23:14:59,150 - event.py - Subscribed to broadcast event: user.message - app.plugins.dynamicwechat.DynamicWeChat.talk
【WARNING】2025-06-04 23:15:40,253 - dynamicwechat - https://myip.ipip.net 获取IP失败, Error: HTTPSConnectionPool(host='myip.ipip.net', port=443): Max retries exceeded with url: / (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x7f113263e0f0>: Failed to resolve 'myip.ipip.net' ([Errno -3] Temporary failure in name resolution)"))
【WARNING】2025-06-04 23:15:44,313 - dynamicwechat - https://4.ipw.cn 获取IP失败, Error: HTTPSConnectionPool(host='4.ipw.cn', port=443): Max retries exceeded with url: / (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x7f11325d8fb0>: Failed to resolve '4.ipw.cn' ([Errno -3] Temporary failure in name resolution)"))
【WARNING】2025-06-04 23:15:44,333 - dynamicwechat - https://ddns.oray.com/checkip 获取IP失败, Error: HTTPSConnectionPool(host='ddns.oray.com', port=443): Max retries exceeded with url: /checkip (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x7f1132591250>: Failed to resolve 'ddns.oray.com' ([Errno -3] Temporary failure in name resolution)"))
【WARNING】2025-06-04 23:15:48,352 - dynamicwechat - https://ip.3322.net 获取IP失败, Error: HTTPSConnectionPool(host='ip.3322.net', port=443): Max retries exceeded with url: / (Caused by NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x7f11325ac800>: Failed to resolve 'ip.3322.net' ([Errno -3] Temporary failure in name resolution)"))
【DEBUG】2025-06-04 23:16:27,240 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.forced_change
【DEBUG】2025-06-04 23:16:27,241 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.local_scanning
【DEBUG】2025-06-04 23:16:27,241 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.write_wan2_ip
【DEBUG】2025-06-04 23:16:27,241 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.check
【DEBUG】2025-06-04 23:16:27,242 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.push_qr_code
【DEBUG】2025-06-04 23:16:27,242 - event.py - Subscribed to broadcast event: user.message - app.plugins.dynamicwechat.DynamicWeChat.talk
【DEBUG】2025-06-04 23:24:03,965 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.forced_change
【DEBUG】2025-06-04 23:24:03,965 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.local_scanning
【DEBUG】2025-06-04 23:24:03,965 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.write_wan2_ip
【DEBUG】2025-06-04 23:24:03,965 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.check
【DEBUG】2025-06-04 23:24:03,966 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.push_qr_code
【DEBUG】2025-06-04 23:24:03,966 - event.py - Subscribed to broadcast event: user.message - app.plugins.dynamicwechat.DynamicWeChat.talk
【DEBUG】2025-06-05 00:35:15,002 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.forced_change
【DEBUG】2025-06-05 00:35:15,002 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.local_scanning
【DEBUG】2025-06-05 00:35:15,003 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.write_wan2_ip
【DEBUG】2025-06-05 00:35:15,003 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.check
【DEBUG】2025-06-05 00:35:15,003 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.push_qr_code
【DEBUG】2025-06-05 00:35:15,004 - event.py - Subscribed to broadcast event: user.message - app.plugins.dynamicwechat.DynamicWeChat.talk
【DEBUG】2025-06-05 00:36:53,581 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.forced_change
【DEBUG】2025-06-05 00:36:53,582 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.local_scanning
【DEBUG】2025-06-05 00:36:53,582 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.write_wan2_ip
【DEBUG】2025-06-05 00:36:53,582 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.check
【DEBUG】2025-06-05 00:36:53,582 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.push_qr_code
【DEBUG】2025-06-05 00:36:53,583 - event.py - Subscribed to broadcast event: user.message - app.plugins.dynamicwechat.DynamicWeChat.talk
【DEBUG】2025-06-05 07:23:47,313 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.forced_change
【DEBUG】2025-06-05 07:23:47,313 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.local_scanning
【DEBUG】2025-06-05 07:23:47,314 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.write_wan2_ip
【DEBUG】2025-06-05 07:23:47,314 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.check
【DEBUG】2025-06-05 07:23:47,314 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.push_qr_code
【DEBUG】2025-06-05 07:23:47,315 - event.py - Subscribed to broadcast event: user.message - app.plugins.dynamicwechat.DynamicWeChat.talk
【DEBUG】2025-06-05 16:06:29,829 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.forced_change
【DEBUG】2025-06-05 16:06:29,829 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.local_scanning
【DEBUG】2025-06-05 16:06:29,829 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.write_wan2_ip
【DEBUG】2025-06-05 16:06:29,830 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.check
【DEBUG】2025-06-05 16:06:29,830 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.push_qr_code
【DEBUG】2025-06-05 16:06:29,831 - event.py - Subscribed to broadcast event: user.message - app.plugins.dynamicwechat.DynamicWeChat.talk
【DEBUG】2025-06-05 21:24:16,341 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.forced_change
【DEBUG】2025-06-05 21:24:16,341 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.local_scanning
【DEBUG】2025-06-05 21:24:16,342 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.write_wan2_ip
【DEBUG】2025-06-05 21:24:16,342 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.check
【DEBUG】2025-06-05 21:24:16,342 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.push_qr_code
【DEBUG】2025-06-05 21:24:16,343 - event.py - Subscribed to broadcast event: user.message - app.plugins.dynamicwechat.DynamicWeChat.talk
【DEBUG】2025-06-05 21:36:38,203 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.forced_change
【DEBUG】2025-06-05 21:36:38,204 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.local_scanning
【DEBUG】2025-06-05 21:36:38,204 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.write_wan2_ip
【DEBUG】2025-06-05 21:36:38,204 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.check
【DEBUG】2025-06-05 21:36:38,205 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.push_qr_code
【DEBUG】2025-06-05 21:36:38,205 - event.py - Subscribed to broadcast event: user.message - app.plugins.dynamicwechat.DynamicWeChat.talk
【DEBUG】2025-06-06 11:17:48,347 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.forced_change
【DEBUG】2025-06-06 11:17:48,347 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.local_scanning
【DEBUG】2025-06-06 11:17:48,347 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.write_wan2_ip
【DEBUG】2025-06-06 11:17:48,347 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.check
【DEBUG】2025-06-06 11:17:48,348 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.push_qr_code
【DEBUG】2025-06-06 11:17:48,348 - event.py - Subscribed to broadcast event: user.message - app.plugins.dynamicwechat.DynamicWeChat.talk
【DEBUG】2025-06-06 16:20:28,946 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.forced_change
【DEBUG】2025-06-06 16:20:28,946 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.local_scanning
【DEBUG】2025-06-06 16:20:28,947 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.write_wan2_ip
【DEBUG】2025-06-06 16:20:28,947 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.check
【DEBUG】2025-06-06 16:20:28,947 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.push_qr_code
【DEBUG】2025-06-06 16:20:28,948 - event.py - Subscribed to broadcast event: user.message - app.plugins.dynamicwechat.DynamicWeChat.talk
【DEBUG】2025-06-06 16:57:39,323 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.forced_change
【DEBUG】2025-06-06 16:57:39,324 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.local_scanning
【DEBUG】2025-06-06 16:57:39,324 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.write_wan2_ip
【DEBUG】2025-06-06 16:57:39,324 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.check
【DEBUG】2025-06-06 16:57:39,325 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.push_qr_code
【DEBUG】2025-06-06 16:57:39,325 - event.py - Subscribed to broadcast event: user.message - app.plugins.dynamicwechat.DynamicWeChat.talk
【DEBUG】2025-06-06 16:58:48,486 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.forced_change
【DEBUG】2025-06-06 16:58:48,486 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.local_scanning
【DEBUG】2025-06-06 16:58:48,487 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.write_wan2_ip
【DEBUG】2025-06-06 16:58:48,487 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.check
【DEBUG】2025-06-06 16:58:48,487 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.push_qr_code
【DEBUG】2025-06-06 16:58:48,488 - event.py - Subscribed to broadcast event: user.message - app.plugins.dynamicwechat.DynamicWeChat.talk
【DEBUG】2025-06-06 22:26:08,152 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.forced_change
【DEBUG】2025-06-06 22:26:08,152 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.local_scanning
【DEBUG】2025-06-06 22:26:08,153 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.write_wan2_ip
【DEBUG】2025-06-06 22:26:08,153 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.check
【DEBUG】2025-06-06 22:26:08,154 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.push_qr_code
【DEBUG】2025-06-06 22:26:08,154 - event.py - Subscribed to broadcast event: user.message - app.plugins.dynamicwechat.DynamicWeChat.talk
【DEBUG】2025-06-07 00:26:30,572 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.forced_change
【DEBUG】2025-06-07 00:26:30,572 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.local_scanning
【DEBUG】2025-06-07 00:26:30,572 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.write_wan2_ip
【DEBUG】2025-06-07 00:26:30,573 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.check
【DEBUG】2025-06-07 00:26:30,573 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.push_qr_code
【DEBUG】2025-06-07 00:26:30,573 - event.py - Subscribed to broadcast event: user.message - app.plugins.dynamicwechat.DynamicWeChat.talk
【DEBUG】2025-06-07 00:27:33,190 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.forced_change
【DEBUG】2025-06-07 00:27:33,191 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.local_scanning
【DEBUG】2025-06-07 00:27:33,191 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.write_wan2_ip
【DEBUG】2025-06-07 00:27:33,191 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.check
【DEBUG】2025-06-07 00:27:33,192 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.dynamicwechat.DynamicWeChat.push_qr_code
【DEBUG】2025-06-07 00:27:33,192 - event.py - Subscribed to broadcast event: user.message - app.plugins.dynamicwechat.DynamicWeChat.talk
