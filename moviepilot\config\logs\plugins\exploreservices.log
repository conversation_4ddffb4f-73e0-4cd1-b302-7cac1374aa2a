【INFO】2025-05-30 22:30:09,055 - exploreservices - [资源探索集合] Total registered APIs: 0
【INFO】2025-06-02 10:29:35,265 - exploreservices - [资源探索集合] Total registered APIs: 0
【INFO】2025-06-03 19:04:44,171 - exploreservices - [资源探索集合] Total registered APIs: 0
【INFO】2025-06-03 19:12:22,926 - exploreservices - [资源探索集合] Total registered APIs: 0
【INFO】2025-06-03 19:14:28,793 - exploreservices - [资源探索集合] Total registered APIs: 0
【INFO】2025-06-03 19:38:01,663 - exploreservices - [资源探索集合] Total registered APIs: 0
【DEBUG】2025-06-03 19:59:00,472 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-03 19:59:00,472 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-03 19:59:00,473 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-03 19:59:00,473 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-03 19:59:01,610 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-03 19:59:01,610 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-03 19:59:01,611 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-03 19:59:01,611 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-03 19:59:01,611 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-03 19:59:01,612 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-03 19:59:03,415 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-03 19:59:03,415 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-03 19:59:03,417 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-03 19:59:03,418 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-03 19:59:03,418 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-03 19:59:03,419 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-03 19:59:03,421 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-03 19:59:03,421 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-03 19:59:03,422 - exploreservices - [资源探索集合] Total registered APIs: 6
【DEBUG】2025-06-03 19:59:11,527 - tencentvideodiscoverchain - Final poster URL for 折腰: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200kqpbtfg1747301343207
【DEBUG】2025-06-03 19:59:11,527 - tencentvideodiscoverchain - Final poster URL for 陷入我们的热恋: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200vvr86iv1747823989300
【DEBUG】2025-06-03 19:59:11,528 - tencentvideodiscoverchain - Final poster URL for 刑侦12[粤语版]: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200s3ovtv01747372076164
【DEBUG】2025-06-03 19:59:11,528 - tencentvideodiscoverchain - Final poster URL for 刑侦12[普通话版]: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200cjoq8wk1747372045719
【DEBUG】2025-06-03 19:59:11,529 - tencentvideodiscoverchain - Final poster URL for 护宝寻踪: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc002004c7d72n1747664815461
【DEBUG】2025-06-03 19:59:11,529 - tencentvideodiscoverchain - Final poster URL for 逆天成仙: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc002006il1d4p1745828843924
【DEBUG】2025-06-03 19:59:11,530 - tencentvideodiscoverchain - Final poster URL for 无尽的尽头: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200wcmszla1745380039152
【DEBUG】2025-06-03 19:59:11,530 - tencentvideodiscoverchain - Final poster URL for 紫川之光明王: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc002007fr422r1748252862538
【DEBUG】2025-06-03 19:59:11,531 - tencentvideodiscoverchain - Final poster URL for 刑警的日子: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200zeaf7ei1747298171691
【DEBUG】2025-06-03 19:59:11,531 - tencentvideodiscoverchain - Final poster URL for 亲爱的仇敌: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc0020021vnujn1747292027271
【DEBUG】2025-06-03 19:59:11,532 - tencentvideodiscoverchain - Final poster URL for 榜上佳婿: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc002003qd6zlh1745463846420
【DEBUG】2025-06-03 19:59:11,532 - tencentvideodiscoverchain - Final poster URL for 错嫁世子妃: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200t6ojf6k1742459978584
【DEBUG】2025-06-03 19:59:11,532 - tencentvideodiscoverchain - Final poster URL for 云赫谣: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200ap9t6911748252276785
【DEBUG】2025-06-03 19:59:11,533 - tencentvideodiscoverchain - Final poster URL for 狮城山海: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200qczv7cf1745378619305
【DEBUG】2025-06-03 19:59:11,533 - tencentvideodiscoverchain - Final poster URL for 神都狐探: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200wuq4dri1747715976056
【DEBUG】2025-06-03 19:59:11,534 - tencentvideodiscoverchain - Final poster URL for 我叫张思德: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200hogvt0g1748401785017
【DEBUG】2025-06-03 19:59:11,534 - tencentvideodiscoverchain - Final poster URL for 藏珠: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200c24itxk1745563607136
【DEBUG】2025-06-03 19:59:11,535 - tencentvideodiscoverchain - Final poster URL for 九重紫: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200vkqr54u1733107610212
【DEBUG】2025-06-03 19:59:11,535 - tencentvideodiscoverchain - Final poster URL for 庆余年第二季: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc002002kqssyu1715223183563
【DEBUG】2025-06-03 19:59:11,536 - tencentvideodiscoverchain - Final poster URL for 千金一掷: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200ggc2rfs1747189814063
【DEBUG】2025-06-03 19:59:11,536 - tencentvideodiscoverchain - Final poster URL for 成家: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc002009upgf7x1745379655515
【DEBUG】2025-06-03 19:59:11,806 - tencentvideodiscoverchain - Final poster URL for 穆桂英挂帅: http://puui.qpic.cn/vcover_vt_pic/0/hztm1208v348aa41540456812
【DEBUG】2025-06-03 19:59:11,807 - tencentvideodiscoverchain - Final poster URL for 我的后半生: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200rb0xufo1745377997565
【DEBUG】2025-06-03 19:59:11,809 - tencentvideodiscoverchain - Final poster URL for 雁回时: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200m92miea1745379028986
【DEBUG】2025-06-03 19:59:11,810 - tencentvideodiscoverchain - Final poster URL for 望母成凤: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200z3b4mu31747622442674
【DEBUG】2025-06-03 19:59:11,811 - tencentvideodiscoverchain - Final poster URL for 藏海花: http://puui.qpic.cn/vcover_vt_pic/0/mzc00200n81wxis1667457517555
【DEBUG】2025-06-03 19:59:11,813 - tencentvideodiscoverchain - Final poster URL for 大奉打更人: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200qon7vo31729214997689
【DEBUG】2025-06-03 19:59:11,815 - tencentvideodiscoverchain - Final poster URL for 棋士: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200htmlstu1736045822632
【DEBUG】2025-06-03 19:59:11,816 - tencentvideodiscoverchain - Final poster URL for 三嫁魔君: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200r6mj3au1736827149839
【DEBUG】2025-06-03 19:59:11,817 - tencentvideodiscoverchain - Final poster URL for 悬镜: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200m49hfid1745378506338
【DEBUG】2025-06-03 19:59:11,820 - tencentvideodiscoverchain - Final poster URL for 值得爱: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200o4ikbtq1745378432715
【DEBUG】2025-06-03 19:59:11,822 - tencentvideodiscoverchain - Final poster URL for 月升沧海: http://puui.qpic.cn/vcover_vt_pic/0/mzc00200vmd652y1658930222301
【DEBUG】2025-06-03 19:59:11,824 - tencentvideodiscoverchain - Final poster URL for 六姊妹: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200vfspynw1715158153074
【DEBUG】2025-06-03 19:59:11,826 - tencentvideodiscoverchain - Final poster URL for 似锦: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200fbyr29e1738814419034
【DEBUG】2025-06-03 19:59:11,828 - tencentvideodiscoverchain - Final poster URL for 庆余年: http://puui.qpic.cn/vcover_vt_pic/0/rjae621myqca41h1642080300304
【DEBUG】2025-06-03 19:59:11,830 - tencentvideodiscoverchain - Final poster URL for 2099: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200a1jzqrf1747039509904
【DEBUG】2025-06-03 19:59:11,831 - tencentvideodiscoverchain - Final poster URL for 永夜星河: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc002007sqbpce1719196138249
【DEBUG】2025-06-03 19:59:11,831 - tencentvideodiscoverchain - Final poster URL for 孤芳不自赏: http://puui.qpic.cn/vcover_vt_pic/0/1c1q73bcgr8ykwj1506483658
【DEBUG】2025-06-03 19:59:11,832 - tencentvideodiscoverchain - Final poster URL for 轮廓中的真相: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc00200q1xty1l1747188227212
【DEBUG】2025-06-03 19:59:11,835 - tencentvideodiscoverchain - Final poster URL for 玫瑰的故事: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/mzc002002s2ark51717137587048
【DEBUG】2025-06-03 19:59:11,836 - tencentvideodiscoverchain - Final poster URL for 新三国: https://vcover-vt-pic.puui.qpic.cn/vcover_vt_pic/0/ceusrubximxm0g81711423309775
【DEBUG】2025-06-03 19:59:11,836 - tencentvideodiscoverchain - Final poster URL for 陈情令: http://puui.qpic.cn/vcover_vt_pic/0/vbb35hm6m6da1wc1561952321
【DEBUG】2025-06-03 20:13:29,885 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain.discover_source
【DEBUG】2025-06-03 20:13:29,891 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain.discover_source
【DEBUG】2025-06-03 20:13:29,893 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain.discover_source
【DEBUG】2025-06-03 20:13:29,897 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain.discover_source
【DEBUG】2025-06-03 20:13:29,901 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain.discover_source
【DEBUG】2025-06-03 20:13:29,904 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain.discover_source
【DEBUG】2025-06-03 20:13:29,905 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.ExploreServices.discover_source
【DEBUG】2025-06-03 20:13:29,978 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-03 20:13:29,979 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-03 20:13:29,979 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-03 20:13:29,979 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-03 20:13:30,797 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-03 20:13:30,798 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-03 20:13:30,798 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-03 20:13:30,799 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-03 20:13:30,799 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-03 20:13:30,800 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-03 20:13:32,240 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-03 20:13:32,241 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-03 20:13:32,849 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-03 20:13:32,849 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-03 20:13:32,849 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-03 20:13:32,850 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-03 20:13:32,850 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-03 20:13:32,851 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-03 20:13:32,851 - exploreservices - [资源探索集合] Total registered APIs: 6
【DEBUG】2025-06-03 22:24:38,509 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain.discover_source
【DEBUG】2025-06-03 22:24:38,624 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain.discover_source
【DEBUG】2025-06-03 22:24:38,718 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain.discover_source
【DEBUG】2025-06-03 22:24:38,860 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain.discover_source
【DEBUG】2025-06-03 22:24:39,089 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain.discover_source
【DEBUG】2025-06-03 22:24:39,269 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain.discover_source
【DEBUG】2025-06-03 22:24:39,347 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.ExploreServices.discover_source
【DEBUG】2025-06-03 22:24:47,048 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-03 22:24:47,077 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-03 22:24:47,092 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-03 22:24:47,115 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-03 22:24:48,963 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-03 22:24:48,987 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-03 22:24:49,021 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-03 22:24:49,042 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-03 22:24:49,044 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-03 22:24:49,075 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-03 22:24:52,284 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-03 22:24:52,311 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-03 22:24:53,196 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-03 22:24:53,196 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-03 22:24:53,196 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-03 22:24:53,197 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-03 22:24:53,197 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-03 22:24:53,197 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-03 22:24:53,198 - exploreservices - [资源探索集合] Total registered APIs: 6
【DEBUG】2025-06-04 10:24:13,065 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain.discover_source
【DEBUG】2025-06-04 10:24:13,131 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain.discover_source
【DEBUG】2025-06-04 10:24:13,201 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain.discover_source
【DEBUG】2025-06-04 10:24:13,292 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain.discover_source
【DEBUG】2025-06-04 10:24:13,444 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain.discover_source
【DEBUG】2025-06-04 10:24:13,586 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain.discover_source
【DEBUG】2025-06-04 10:24:13,591 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.ExploreServices.discover_source
【DEBUG】2025-06-04 10:24:21,317 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-04 10:24:21,323 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-04 10:24:21,336 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-04 10:24:21,390 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-04 10:24:23,238 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-04 10:24:23,276 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-04 10:24:23,278 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-04 10:24:23,302 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-04 10:24:23,307 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-04 10:24:23,369 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-04 10:24:25,408 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-04 10:24:25,409 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-04 10:24:29,312 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-04 10:24:29,312 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-04 10:24:29,400 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-04 10:24:29,403 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-04 10:24:29,407 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-04 10:24:29,431 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-04 10:24:29,439 - exploreservices - [资源探索集合] Total registered APIs: 6
【INFO】2025-06-04 11:22:39,073 - exploreservices - [资源探索集合] Bangumi plugin stopped and event handler unregistered
【INFO】2025-06-04 11:22:39,074 - exploreservices - [资源探索集合] Bilibili plugin stopped and event handler unregistered
【INFO】2025-06-04 11:22:39,074 - exploreservices - [资源探索集合] MangoTV plugin stopped and event handler unregistered
【INFO】2025-06-04 11:22:39,074 - exploreservices - [资源探索集合] MiGuVideo plugin stopped and event handler unregistered
【INFO】2025-06-04 11:22:39,075 - exploreservices - [资源探索集合] CCTV plugin stopped and event handler unregistered
【INFO】2025-06-04 11:22:39,075 - exploreservices - [资源探索集合] TencentVideo plugin stopped and event handler unregistered
【DEBUG】2025-06-04 11:24:03,033 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain.discover_source
【DEBUG】2025-06-04 11:24:03,253 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain.discover_source
【DEBUG】2025-06-04 11:24:03,651 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain.discover_source
【DEBUG】2025-06-04 11:24:03,845 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain.discover_source
【DEBUG】2025-06-04 11:24:03,965 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain.discover_source
【DEBUG】2025-06-04 11:24:04,095 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain.discover_source
【DEBUG】2025-06-04 11:24:04,129 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.ExploreServices.discover_source
【DEBUG】2025-06-04 11:24:11,775 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-04 11:24:11,790 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-04 11:24:11,794 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-04 11:24:11,813 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-04 11:24:13,859 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-04 11:24:13,887 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-04 11:24:13,890 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-04 11:24:13,897 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-04 11:24:13,930 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-04 11:24:13,959 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-04 11:24:16,616 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-04 11:24:16,617 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-04 11:24:20,407 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-04 11:24:20,407 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-04 11:24:20,446 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-04 11:24:20,476 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-04 11:24:20,482 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-04 11:24:20,505 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-04 11:24:20,517 - exploreservices - [资源探索集合] Total registered APIs: 6
【INFO】2025-06-04 16:49:09,718 - exploreservices - [资源探索集合] Bangumi plugin stopped and event handler unregistered
【INFO】2025-06-04 16:49:09,718 - exploreservices - [资源探索集合] Bilibili plugin stopped and event handler unregistered
【INFO】2025-06-04 16:49:09,718 - exploreservices - [资源探索集合] MangoTV plugin stopped and event handler unregistered
【INFO】2025-06-04 16:49:09,719 - exploreservices - [资源探索集合] MiGuVideo plugin stopped and event handler unregistered
【INFO】2025-06-04 16:49:09,719 - exploreservices - [资源探索集合] CCTV plugin stopped and event handler unregistered
【INFO】2025-06-04 16:49:09,719 - exploreservices - [资源探索集合] TencentVideo plugin stopped and event handler unregistered
【DEBUG】2025-06-04 16:50:11,974 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain.discover_source
【DEBUG】2025-06-04 16:50:11,975 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain.discover_source
【DEBUG】2025-06-04 16:50:11,976 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain.discover_source
【DEBUG】2025-06-04 16:50:11,977 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain.discover_source
【DEBUG】2025-06-04 16:50:11,979 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain.discover_source
【DEBUG】2025-06-04 16:50:11,980 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain.discover_source
【DEBUG】2025-06-04 16:50:11,981 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.ExploreServices.discover_source
【DEBUG】2025-06-04 16:50:12,938 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-04 16:50:12,938 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-04 16:50:12,938 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-04 16:50:12,939 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-04 16:50:13,838 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-04 16:50:13,839 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-04 16:50:13,839 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-04 16:50:13,839 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-04 16:50:13,839 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-04 16:50:13,840 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-04 16:50:15,244 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-04 16:50:15,244 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-04 16:50:15,481 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-04 16:50:15,482 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-04 16:50:15,482 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-04 16:50:15,482 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-04 16:50:15,482 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-04 16:50:15,483 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-04 16:50:15,483 - exploreservices - [资源探索集合] Total registered APIs: 6
【INFO】2025-06-04 16:50:45,847 - exploreservices - [资源探索集合] Bangumi plugin stopped and event handler unregistered
【INFO】2025-06-04 16:50:45,909 - exploreservices - [资源探索集合] Bilibili plugin stopped and event handler unregistered
【INFO】2025-06-04 16:50:45,935 - exploreservices - [资源探索集合] MangoTV plugin stopped and event handler unregistered
【INFO】2025-06-04 16:50:45,948 - exploreservices - [资源探索集合] MiGuVideo plugin stopped and event handler unregistered
【INFO】2025-06-04 16:50:45,970 - exploreservices - [资源探索集合] CCTV plugin stopped and event handler unregistered
【INFO】2025-06-04 16:50:45,980 - exploreservices - [资源探索集合] TencentVideo plugin stopped and event handler unregistered
【DEBUG】2025-06-04 16:50:55,315 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-04 16:50:55,337 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-04 16:50:55,357 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-04 16:50:55,404 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-04 16:50:57,316 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-04 16:50:57,330 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-04 16:50:57,334 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-04 16:50:57,335 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-04 16:50:57,398 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-04 16:50:57,411 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-04 16:51:00,136 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-04 16:51:00,141 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-04 16:51:03,558 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-04 16:51:03,564 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-04 16:51:03,570 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-04 16:51:03,571 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-04 16:51:03,588 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-04 16:51:03,620 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-04 16:51:03,655 - exploreservices - [资源探索集合] Total registered APIs: 6
【DEBUG】2025-06-04 23:14:03,616 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain.discover_source
【DEBUG】2025-06-04 23:14:03,618 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain.discover_source
【DEBUG】2025-06-04 23:14:03,618 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain.discover_source
【DEBUG】2025-06-04 23:14:03,619 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain.discover_source
【DEBUG】2025-06-04 23:14:03,621 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain.discover_source
【DEBUG】2025-06-04 23:14:03,622 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain.discover_source
【DEBUG】2025-06-04 23:14:03,622 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.ExploreServices.discover_source
【DEBUG】2025-06-04 23:14:04,420 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-04 23:14:04,420 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-04 23:14:04,420 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-04 23:14:04,420 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-04 23:14:05,291 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-04 23:14:05,291 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-04 23:14:05,291 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-04 23:14:05,292 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-04 23:14:05,292 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-04 23:14:05,292 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-04 23:14:06,785 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-04 23:14:06,786 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-04 23:14:07,018 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-04 23:14:07,018 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-04 23:14:07,018 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-04 23:14:07,019 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-04 23:14:07,019 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-04 23:14:07,019 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-04 23:14:07,019 - exploreservices - [资源探索集合] Total registered APIs: 6
【INFO】2025-06-04 23:14:33,256 - exploreservices - [资源探索集合] Bangumi plugin stopped and event handler unregistered
【INFO】2025-06-04 23:14:33,267 - exploreservices - [资源探索集合] Bilibili plugin stopped and event handler unregistered
【INFO】2025-06-04 23:14:33,285 - exploreservices - [资源探索集合] MangoTV plugin stopped and event handler unregistered
【INFO】2025-06-04 23:14:33,302 - exploreservices - [资源探索集合] MiGuVideo plugin stopped and event handler unregistered
【INFO】2025-06-04 23:14:33,349 - exploreservices - [资源探索集合] CCTV plugin stopped and event handler unregistered
【INFO】2025-06-04 23:14:33,350 - exploreservices - [资源探索集合] TencentVideo plugin stopped and event handler unregistered
【DEBUG】2025-06-04 23:14:59,157 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain.discover_source
【DEBUG】2025-06-04 23:14:59,158 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain.discover_source
【DEBUG】2025-06-04 23:14:59,159 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain.discover_source
【DEBUG】2025-06-04 23:14:59,160 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain.discover_source
【DEBUG】2025-06-04 23:14:59,162 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain.discover_source
【DEBUG】2025-06-04 23:14:59,163 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain.discover_source
【DEBUG】2025-06-04 23:14:59,163 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.ExploreServices.discover_source
【DEBUG】2025-06-04 23:14:59,975 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-04 23:14:59,975 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-04 23:14:59,975 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-04 23:14:59,975 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-04 23:15:00,844 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-04 23:15:00,844 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-04 23:15:00,844 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-04 23:15:00,845 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-04 23:15:00,845 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-04 23:15:00,845 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-04 23:15:02,398 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-04 23:15:02,398 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-04 23:15:02,656 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-04 23:15:02,657 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-04 23:15:02,657 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-04 23:15:02,657 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-04 23:15:02,658 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-04 23:15:02,658 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-04 23:15:02,658 - exploreservices - [资源探索集合] Total registered APIs: 6
【INFO】2025-06-04 23:15:23,249 - exploreservices - [资源探索集合] Bangumi plugin stopped and event handler unregistered
【INFO】2025-06-04 23:15:23,337 - exploreservices - [资源探索集合] Bilibili plugin stopped and event handler unregistered
【INFO】2025-06-04 23:15:23,373 - exploreservices - [资源探索集合] MangoTV plugin stopped and event handler unregistered
【INFO】2025-06-04 23:15:23,396 - exploreservices - [资源探索集合] MiGuVideo plugin stopped and event handler unregistered
【INFO】2025-06-04 23:15:23,422 - exploreservices - [资源探索集合] CCTV plugin stopped and event handler unregistered
【INFO】2025-06-04 23:15:23,436 - exploreservices - [资源探索集合] TencentVideo plugin stopped and event handler unregistered
【DEBUG】2025-06-04 23:15:33,213 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-04 23:15:33,239 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-04 23:15:33,244 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-04 23:15:33,325 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-04 23:15:35,156 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-04 23:15:35,270 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-04 23:15:35,277 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-04 23:15:35,289 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-04 23:15:35,322 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-04 23:15:35,344 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-04 23:15:38,247 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-04 23:15:38,251 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-04 23:15:49,207 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-04 23:15:49,232 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-04 23:15:49,235 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-04 23:15:49,301 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-04 23:15:49,415 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-04 23:15:49,418 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-04 23:15:49,419 - exploreservices - [资源探索集合] Total registered APIs: 6
【DEBUG】2025-06-04 23:16:27,250 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain.discover_source
【DEBUG】2025-06-04 23:16:27,251 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain.discover_source
【DEBUG】2025-06-04 23:16:27,252 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain.discover_source
【DEBUG】2025-06-04 23:16:27,253 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain.discover_source
【DEBUG】2025-06-04 23:16:27,255 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain.discover_source
【DEBUG】2025-06-04 23:16:27,255 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain.discover_source
【DEBUG】2025-06-04 23:16:27,256 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.ExploreServices.discover_source
【DEBUG】2025-06-04 23:16:28,121 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-04 23:16:28,121 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-04 23:16:28,121 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-04 23:16:28,122 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-04 23:16:28,921 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-04 23:16:28,922 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-04 23:16:28,922 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-04 23:16:28,923 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-04 23:16:28,923 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-04 23:16:28,923 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-04 23:16:30,475 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-04 23:16:30,475 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-04 23:16:30,701 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-04 23:16:30,702 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-04 23:16:30,702 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-04 23:16:30,702 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-04 23:16:30,703 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-04 23:16:30,703 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-04 23:16:30,703 - exploreservices - [资源探索集合] Total registered APIs: 6
【INFO】2025-06-04 23:17:12,065 - exploreservices - [资源探索集合] Bangumi plugin stopped and event handler unregistered
【INFO】2025-06-04 23:17:12,156 - exploreservices - [资源探索集合] Bilibili plugin stopped and event handler unregistered
【INFO】2025-06-04 23:17:12,181 - exploreservices - [资源探索集合] MangoTV plugin stopped and event handler unregistered
【INFO】2025-06-04 23:17:12,189 - exploreservices - [资源探索集合] MiGuVideo plugin stopped and event handler unregistered
【INFO】2025-06-04 23:17:12,195 - exploreservices - [资源探索集合] CCTV plugin stopped and event handler unregistered
【INFO】2025-06-04 23:17:12,223 - exploreservices - [资源探索集合] TencentVideo plugin stopped and event handler unregistered
【DEBUG】2025-06-04 23:17:20,323 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-04 23:17:20,325 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-04 23:17:20,357 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-04 23:17:20,357 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-04 23:17:22,128 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-04 23:17:22,155 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-04 23:17:22,157 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-04 23:17:22,213 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-04 23:17:22,223 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-04 23:17:22,250 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-04 23:17:24,828 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-04 23:17:24,834 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-04 23:17:28,156 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-04 23:17:28,201 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-04 23:17:28,226 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-04 23:17:28,254 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-04 23:17:28,255 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-04 23:17:28,301 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-04 23:17:28,302 - exploreservices - [资源探索集合] Total registered APIs: 6
【DEBUG】2025-06-04 23:24:03,974 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain.discover_source
【DEBUG】2025-06-04 23:24:03,975 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain.discover_source
【DEBUG】2025-06-04 23:24:03,976 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain.discover_source
【DEBUG】2025-06-04 23:24:03,977 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain.discover_source
【DEBUG】2025-06-04 23:24:03,979 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain.discover_source
【DEBUG】2025-06-04 23:24:03,980 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain.discover_source
【DEBUG】2025-06-04 23:24:03,980 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.ExploreServices.discover_source
【DEBUG】2025-06-04 23:24:04,851 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-04 23:24:04,851 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-04 23:24:04,851 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-04 23:24:04,851 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-04 23:24:05,596 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-04 23:24:05,597 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-04 23:24:05,597 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-04 23:24:05,598 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-04 23:24:05,598 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-04 23:24:05,598 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-04 23:24:07,110 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-04 23:24:07,111 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-04 23:24:07,364 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-04 23:24:07,364 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-04 23:24:07,364 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-04 23:24:07,365 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-04 23:24:07,365 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-04 23:24:07,365 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-04 23:24:07,366 - exploreservices - [资源探索集合] Total registered APIs: 6
【INFO】2025-06-04 23:24:41,919 - exploreservices - [资源探索集合] Bangumi plugin stopped and event handler unregistered
【INFO】2025-06-04 23:24:41,937 - exploreservices - [资源探索集合] Bilibili plugin stopped and event handler unregistered
【INFO】2025-06-04 23:24:42,011 - exploreservices - [资源探索集合] MangoTV plugin stopped and event handler unregistered
【INFO】2025-06-04 23:24:42,043 - exploreservices - [资源探索集合] MiGuVideo plugin stopped and event handler unregistered
【INFO】2025-06-04 23:24:42,044 - exploreservices - [资源探索集合] CCTV plugin stopped and event handler unregistered
【INFO】2025-06-04 23:24:42,076 - exploreservices - [资源探索集合] TencentVideo plugin stopped and event handler unregistered
【DEBUG】2025-06-04 23:24:51,623 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-04 23:24:51,650 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-04 23:24:51,663 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-04 23:24:51,664 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-04 23:24:53,586 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-04 23:24:53,590 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-04 23:24:53,596 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-04 23:24:53,621 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-04 23:24:53,652 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-04 23:24:53,656 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-04 23:24:56,335 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-04 23:24:56,347 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-04 23:24:59,213 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-04 23:24:59,227 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-04 23:24:59,237 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-04 23:24:59,311 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-04 23:24:59,314 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-04 23:24:59,332 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-04 23:24:59,427 - exploreservices - [资源探索集合] Total registered APIs: 6
【DEBUG】2025-06-05 00:35:15,012 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain.discover_source
【DEBUG】2025-06-05 00:35:15,013 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain.discover_source
【DEBUG】2025-06-05 00:35:15,014 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain.discover_source
【DEBUG】2025-06-05 00:35:15,015 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain.discover_source
【DEBUG】2025-06-05 00:35:15,017 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain.discover_source
【DEBUG】2025-06-05 00:35:15,018 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain.discover_source
【DEBUG】2025-06-05 00:35:15,018 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.ExploreServices.discover_source
【DEBUG】2025-06-05 00:35:15,924 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-05 00:35:15,924 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-05 00:35:15,925 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-05 00:35:15,925 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-05 00:35:16,698 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-05 00:35:16,699 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-05 00:35:16,699 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-05 00:35:16,699 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-05 00:35:16,700 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-05 00:35:16,700 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-05 00:35:18,015 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-05 00:35:18,016 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-05 00:35:18,250 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-05 00:35:18,251 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-05 00:35:18,251 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-05 00:35:18,251 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-05 00:35:18,251 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-05 00:35:18,252 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-05 00:35:18,252 - exploreservices - [资源探索集合] Total registered APIs: 6
【INFO】2025-06-05 00:35:55,045 - exploreservices - [资源探索集合] Bangumi plugin stopped and event handler unregistered
【INFO】2025-06-05 00:35:55,063 - exploreservices - [资源探索集合] Bilibili plugin stopped and event handler unregistered
【INFO】2025-06-05 00:35:55,068 - exploreservices - [资源探索集合] MangoTV plugin stopped and event handler unregistered
【INFO】2025-06-05 00:35:55,076 - exploreservices - [资源探索集合] MiGuVideo plugin stopped and event handler unregistered
【INFO】2025-06-05 00:35:55,100 - exploreservices - [资源探索集合] CCTV plugin stopped and event handler unregistered
【INFO】2025-06-05 00:35:55,120 - exploreservices - [资源探索集合] TencentVideo plugin stopped and event handler unregistered
【DEBUG】2025-06-05 00:36:53,591 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain.discover_source
【DEBUG】2025-06-05 00:36:53,592 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain.discover_source
【DEBUG】2025-06-05 00:36:53,593 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain.discover_source
【DEBUG】2025-06-05 00:36:53,593 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain.discover_source
【DEBUG】2025-06-05 00:36:53,596 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain.discover_source
【DEBUG】2025-06-05 00:36:53,596 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain.discover_source
【DEBUG】2025-06-05 00:36:53,597 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.ExploreServices.discover_source
【DEBUG】2025-06-05 00:36:54,453 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-05 00:36:54,453 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-05 00:36:54,453 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-05 00:36:54,454 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-05 00:36:55,244 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-05 00:36:55,246 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-05 00:36:55,247 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-05 00:36:55,248 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-05 00:36:55,248 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-05 00:36:55,248 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-05 00:36:56,759 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-05 00:36:56,760 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-05 00:36:56,995 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-05 00:36:56,996 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-05 00:36:56,996 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-05 00:36:56,996 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-05 00:36:56,996 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-05 00:36:56,997 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-05 00:36:56,997 - exploreservices - [资源探索集合] Total registered APIs: 6
【INFO】2025-06-05 00:37:29,798 - exploreservices - [资源探索集合] Bangumi plugin stopped and event handler unregistered
【INFO】2025-06-05 00:37:29,819 - exploreservices - [资源探索集合] Bilibili plugin stopped and event handler unregistered
【INFO】2025-06-05 00:37:29,837 - exploreservices - [资源探索集合] MangoTV plugin stopped and event handler unregistered
【INFO】2025-06-05 00:37:29,840 - exploreservices - [资源探索集合] MiGuVideo plugin stopped and event handler unregistered
【INFO】2025-06-05 00:37:29,872 - exploreservices - [资源探索集合] CCTV plugin stopped and event handler unregistered
【INFO】2025-06-05 00:37:29,919 - exploreservices - [资源探索集合] TencentVideo plugin stopped and event handler unregistered
【DEBUG】2025-06-05 00:37:39,615 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-05 00:37:39,722 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-05 00:37:39,728 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-05 00:37:39,755 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-05 00:37:41,640 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-05 00:37:41,641 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-05 00:37:41,642 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-05 00:37:41,709 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-05 00:37:41,757 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-05 00:37:41,765 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-05 00:37:44,207 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-05 00:37:44,244 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-05 00:37:47,957 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-05 00:37:47,996 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-05 00:37:48,016 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-05 00:37:48,048 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-05 00:37:48,115 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-05 00:37:48,140 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-05 00:37:48,146 - exploreservices - [资源探索集合] Total registered APIs: 6
【INFO】2025-06-05 07:22:20,632 - exploreservices - [资源探索集合] Bangumi plugin stopped and event handler unregistered
【INFO】2025-06-05 07:22:20,632 - exploreservices - [资源探索集合] Bilibili plugin stopped and event handler unregistered
【INFO】2025-06-05 07:22:20,633 - exploreservices - [资源探索集合] MangoTV plugin stopped and event handler unregistered
【INFO】2025-06-05 07:22:20,633 - exploreservices - [资源探索集合] MiGuVideo plugin stopped and event handler unregistered
【INFO】2025-06-05 07:22:20,633 - exploreservices - [资源探索集合] CCTV plugin stopped and event handler unregistered
【INFO】2025-06-05 07:22:20,634 - exploreservices - [资源探索集合] TencentVideo plugin stopped and event handler unregistered
【DEBUG】2025-06-05 07:24:39,824 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain.discover_source
【DEBUG】2025-06-05 07:24:39,956 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain.discover_source
【DEBUG】2025-06-05 07:24:40,150 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain.discover_source
【DEBUG】2025-06-05 07:24:40,254 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain.discover_source
【DEBUG】2025-06-05 07:24:40,468 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain.discover_source
【DEBUG】2025-06-05 07:24:40,753 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain.discover_source
【DEBUG】2025-06-05 07:24:40,794 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.ExploreServices.discover_source
【DEBUG】2025-06-05 07:24:47,039 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-05 07:24:47,039 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-05 07:24:47,039 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-05 07:24:47,040 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-05 07:24:48,520 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-05 07:24:48,521 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-05 07:24:48,527 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-05 07:24:48,541 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-05 07:24:48,545 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-05 07:24:48,552 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-05 07:24:51,210 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-05 07:24:51,216 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-05 07:24:55,074 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-05 07:24:55,075 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-05 07:24:55,076 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-05 07:24:55,078 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-05 07:24:55,079 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-05 07:24:55,081 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-05 07:24:55,082 - exploreservices - [资源探索集合] Total registered APIs: 6
【DEBUG】2025-06-05 16:06:29,840 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain.discover_source
【DEBUG】2025-06-05 16:06:29,841 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain.discover_source
【DEBUG】2025-06-05 16:06:29,842 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain.discover_source
【DEBUG】2025-06-05 16:06:29,843 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain.discover_source
【DEBUG】2025-06-05 16:06:29,845 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain.discover_source
【DEBUG】2025-06-05 16:06:29,846 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain.discover_source
【DEBUG】2025-06-05 16:06:29,846 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.ExploreServices.discover_source
【DEBUG】2025-06-05 16:06:30,774 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-05 16:06:30,775 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-05 16:06:30,775 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-05 16:06:30,775 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-05 16:06:31,561 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-05 16:06:31,562 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-05 16:06:31,563 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-05 16:06:31,563 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-05 16:06:31,563 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-05 16:06:31,564 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-05 16:06:32,998 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-05 16:06:32,999 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-05 16:06:33,245 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-05 16:06:33,245 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-05 16:06:33,246 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-05 16:06:33,246 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-05 16:06:33,246 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-05 16:06:33,247 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-05 16:06:33,247 - exploreservices - [资源探索集合] Total registered APIs: 6
【INFO】2025-06-05 16:07:03,020 - exploreservices - [资源探索集合] Bangumi plugin stopped and event handler unregistered
【INFO】2025-06-05 16:07:03,027 - exploreservices - [资源探索集合] Bilibili plugin stopped and event handler unregistered
【INFO】2025-06-05 16:07:03,045 - exploreservices - [资源探索集合] MangoTV plugin stopped and event handler unregistered
【INFO】2025-06-05 16:07:03,108 - exploreservices - [资源探索集合] MiGuVideo plugin stopped and event handler unregistered
【INFO】2025-06-05 16:07:03,132 - exploreservices - [资源探索集合] CCTV plugin stopped and event handler unregistered
【INFO】2025-06-05 16:07:03,134 - exploreservices - [资源探索集合] TencentVideo plugin stopped and event handler unregistered
【DEBUG】2025-06-05 16:07:12,337 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-05 16:07:12,346 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-05 16:07:12,362 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-05 16:07:12,366 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-05 16:07:14,352 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-05 16:07:14,381 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-05 16:07:14,394 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-05 16:07:14,440 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-05 16:07:14,455 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-05 16:07:14,469 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-05 16:07:16,817 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-05 16:07:16,836 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-05 16:07:20,000 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-05 16:07:20,011 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-05 16:07:20,012 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-05 16:07:20,041 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-05 16:07:20,046 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-05 16:07:20,068 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-05 16:07:20,091 - exploreservices - [资源探索集合] Total registered APIs: 6
【DEBUG】2025-06-05 21:24:16,351 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain.discover_source
【DEBUG】2025-06-05 21:24:16,352 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain.discover_source
【DEBUG】2025-06-05 21:24:16,353 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain.discover_source
【DEBUG】2025-06-05 21:24:16,354 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain.discover_source
【DEBUG】2025-06-05 21:24:16,356 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain.discover_source
【DEBUG】2025-06-05 21:24:16,357 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain.discover_source
【DEBUG】2025-06-05 21:24:16,357 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.ExploreServices.discover_source
【DEBUG】2025-06-05 21:24:17,429 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-05 21:24:17,430 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-05 21:24:17,430 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-05 21:24:17,430 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-05 21:24:18,225 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-05 21:24:18,226 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-05 21:24:18,226 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-05 21:24:18,226 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-05 21:24:18,227 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-05 21:24:18,227 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-05 21:24:19,803 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-05 21:24:19,804 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-05 21:24:20,015 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-05 21:24:20,015 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-05 21:24:20,016 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-05 21:24:20,016 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-05 21:24:20,016 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-05 21:24:20,017 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-05 21:24:20,017 - exploreservices - [资源探索集合] Total registered APIs: 6
【INFO】2025-06-05 21:24:44,526 - exploreservices - [资源探索集合] Bangumi plugin stopped and event handler unregistered
【INFO】2025-06-05 21:24:44,527 - exploreservices - [资源探索集合] Bilibili plugin stopped and event handler unregistered
【INFO】2025-06-05 21:24:44,530 - exploreservices - [资源探索集合] MangoTV plugin stopped and event handler unregistered
【INFO】2025-06-05 21:24:44,542 - exploreservices - [资源探索集合] MiGuVideo plugin stopped and event handler unregistered
【INFO】2025-06-05 21:24:44,573 - exploreservices - [资源探索集合] CCTV plugin stopped and event handler unregistered
【INFO】2025-06-05 21:24:44,616 - exploreservices - [资源探索集合] TencentVideo plugin stopped and event handler unregistered
【DEBUG】2025-06-05 21:24:53,852 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-05 21:24:53,924 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-05 21:24:53,925 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-05 21:24:53,929 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-05 21:24:56,247 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-05 21:24:56,252 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-05 21:24:56,263 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-05 21:24:56,303 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-05 21:24:56,305 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-05 21:24:56,374 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-05 21:24:59,383 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-05 21:24:59,462 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-05 21:25:02,418 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-05 21:25:02,434 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-05 21:25:02,436 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-05 21:25:02,442 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-05 21:25:02,484 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-05 21:25:02,521 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-05 21:25:02,522 - exploreservices - [资源探索集合] Total registered APIs: 6
【DEBUG】2025-06-05 21:36:38,213 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain.discover_source
【DEBUG】2025-06-05 21:36:38,214 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain.discover_source
【DEBUG】2025-06-05 21:36:38,215 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain.discover_source
【DEBUG】2025-06-05 21:36:38,216 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain.discover_source
【DEBUG】2025-06-05 21:36:38,218 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain.discover_source
【DEBUG】2025-06-05 21:36:38,219 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain.discover_source
【DEBUG】2025-06-05 21:36:38,219 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.ExploreServices.discover_source
【DEBUG】2025-06-05 21:36:39,193 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-05 21:36:39,194 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-05 21:36:39,194 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-05 21:36:39,194 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-05 21:36:40,661 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-05 21:36:40,662 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-05 21:36:40,663 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-05 21:36:40,663 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-05 21:36:40,663 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-05 21:36:40,663 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-05 21:36:42,180 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-05 21:36:42,180 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-05 21:36:42,425 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-05 21:36:42,426 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-05 21:36:42,426 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-05 21:36:42,427 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-05 21:36:42,427 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-05 21:36:42,427 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-05 21:36:42,427 - exploreservices - [资源探索集合] Total registered APIs: 6
【INFO】2025-06-05 21:37:26,351 - exploreservices - [资源探索集合] Bangumi plugin stopped and event handler unregistered
【INFO】2025-06-05 21:37:26,381 - exploreservices - [资源探索集合] Bilibili plugin stopped and event handler unregistered
【INFO】2025-06-05 21:37:26,386 - exploreservices - [资源探索集合] MangoTV plugin stopped and event handler unregistered
【INFO】2025-06-05 21:37:26,394 - exploreservices - [资源探索集合] MiGuVideo plugin stopped and event handler unregistered
【INFO】2025-06-05 21:37:26,414 - exploreservices - [资源探索集合] CCTV plugin stopped and event handler unregistered
【INFO】2025-06-05 21:37:26,429 - exploreservices - [资源探索集合] TencentVideo plugin stopped and event handler unregistered
【DEBUG】2025-06-05 21:37:33,095 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-05 21:37:33,096 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-05 21:37:33,104 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-05 21:37:33,106 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-05 21:37:35,475 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-05 21:37:35,484 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-05 21:37:35,484 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-05 21:37:35,511 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-05 21:37:35,513 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-05 21:37:35,576 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-05 21:37:37,974 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-05 21:37:37,983 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-05 21:37:39,919 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-05 21:37:39,952 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-05 21:37:40,018 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-05 21:37:40,027 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-05 21:37:40,057 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-05 21:37:40,060 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-05 21:37:40,061 - exploreservices - [资源探索集合] Total registered APIs: 6
【DEBUG】2025-06-06 11:17:48,356 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain.discover_source
【DEBUG】2025-06-06 11:17:48,357 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain.discover_source
【DEBUG】2025-06-06 11:17:48,358 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain.discover_source
【DEBUG】2025-06-06 11:17:48,359 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain.discover_source
【DEBUG】2025-06-06 11:17:48,361 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain.discover_source
【DEBUG】2025-06-06 11:17:48,362 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain.discover_source
【DEBUG】2025-06-06 11:17:48,362 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.ExploreServices.discover_source
【DEBUG】2025-06-06 11:17:49,231 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-06 11:17:49,232 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-06 11:17:49,232 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-06 11:17:49,232 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-06 11:17:49,969 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-06 11:17:49,970 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-06 11:17:49,970 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-06 11:17:49,970 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-06 11:17:49,971 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-06 11:17:49,971 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-06 11:17:51,567 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-06 11:17:51,568 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-06 11:17:51,793 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-06 11:17:51,793 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-06 11:17:51,794 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-06 11:17:51,794 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-06 11:17:51,794 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-06 11:17:51,794 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-06 11:17:51,795 - exploreservices - [资源探索集合] Total registered APIs: 6
【INFO】2025-06-06 11:18:22,940 - exploreservices - [资源探索集合] Bangumi plugin stopped and event handler unregistered
【INFO】2025-06-06 11:18:22,940 - exploreservices - [资源探索集合] Bilibili plugin stopped and event handler unregistered
【INFO】2025-06-06 11:18:22,972 - exploreservices - [资源探索集合] MangoTV plugin stopped and event handler unregistered
【INFO】2025-06-06 11:18:22,980 - exploreservices - [资源探索集合] MiGuVideo plugin stopped and event handler unregistered
【INFO】2025-06-06 11:18:23,013 - exploreservices - [资源探索集合] CCTV plugin stopped and event handler unregistered
【INFO】2025-06-06 11:18:23,071 - exploreservices - [资源探索集合] TencentVideo plugin stopped and event handler unregistered
【DEBUG】2025-06-06 11:18:30,348 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-06 11:18:30,370 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-06 11:18:30,378 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-06 11:18:30,409 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-06 11:18:32,362 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-06 11:18:32,401 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-06 11:18:32,422 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-06 11:18:32,436 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-06 11:18:32,439 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-06 11:18:32,447 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-06 11:18:34,978 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-06 11:18:34,986 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-06 11:18:38,313 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-06 11:18:38,367 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-06 11:18:38,384 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-06 11:18:38,392 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-06 11:18:38,412 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-06 11:18:38,446 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-06 11:18:38,499 - exploreservices - [资源探索集合] Total registered APIs: 6
【DEBUG】2025-06-06 16:20:28,956 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain.discover_source
【DEBUG】2025-06-06 16:20:28,957 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain.discover_source
【DEBUG】2025-06-06 16:20:28,957 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain.discover_source
【DEBUG】2025-06-06 16:20:28,958 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain.discover_source
【DEBUG】2025-06-06 16:20:28,960 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain.discover_source
【DEBUG】2025-06-06 16:20:28,961 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain.discover_source
【DEBUG】2025-06-06 16:20:28,962 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.ExploreServices.discover_source
【DEBUG】2025-06-06 16:20:29,921 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-06 16:20:29,921 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-06 16:20:29,921 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-06 16:20:29,921 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-06 16:20:30,677 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-06 16:20:30,677 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-06 16:20:30,678 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-06 16:20:30,678 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-06 16:20:30,678 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-06 16:20:30,679 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-06 16:20:32,208 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-06 16:20:32,208 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-06 16:20:32,426 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-06 16:20:32,426 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-06 16:20:32,427 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-06 16:20:32,427 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-06 16:20:32,427 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-06 16:20:32,428 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-06 16:20:32,428 - exploreservices - [资源探索集合] Total registered APIs: 6
【INFO】2025-06-06 16:20:59,882 - exploreservices - [资源探索集合] Bangumi plugin stopped and event handler unregistered
【INFO】2025-06-06 16:20:59,895 - exploreservices - [资源探索集合] Bilibili plugin stopped and event handler unregistered
【INFO】2025-06-06 16:20:59,896 - exploreservices - [资源探索集合] MangoTV plugin stopped and event handler unregistered
【INFO】2025-06-06 16:20:59,896 - exploreservices - [资源探索集合] MiGuVideo plugin stopped and event handler unregistered
【INFO】2025-06-06 16:20:59,903 - exploreservices - [资源探索集合] CCTV plugin stopped and event handler unregistered
【INFO】2025-06-06 16:20:59,909 - exploreservices - [资源探索集合] TencentVideo plugin stopped and event handler unregistered
【DEBUG】2025-06-06 16:21:06,469 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-06 16:21:06,488 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-06 16:21:06,500 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-06 16:21:06,593 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-06 16:21:08,615 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-06 16:21:08,685 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-06 16:21:08,709 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-06 16:21:08,717 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-06 16:21:08,737 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-06 16:21:08,743 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-06 16:21:11,132 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-06 16:21:11,218 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-06 16:21:13,739 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-06 16:21:13,766 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-06 16:21:13,825 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-06 16:21:13,830 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-06 16:21:13,858 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-06 16:21:13,874 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-06 16:21:13,892 - exploreservices - [资源探索集合] Total registered APIs: 6
【INFO】2025-06-06 16:52:07,437 - exploreservices - [资源探索集合] Bangumi plugin stopped and event handler unregistered
【INFO】2025-06-06 16:52:07,437 - exploreservices - [资源探索集合] Bilibili plugin stopped and event handler unregistered
【INFO】2025-06-06 16:52:07,437 - exploreservices - [资源探索集合] MangoTV plugin stopped and event handler unregistered
【INFO】2025-06-06 16:52:07,438 - exploreservices - [资源探索集合] MiGuVideo plugin stopped and event handler unregistered
【INFO】2025-06-06 16:52:07,438 - exploreservices - [资源探索集合] CCTV plugin stopped and event handler unregistered
【INFO】2025-06-06 16:52:07,438 - exploreservices - [资源探索集合] TencentVideo plugin stopped and event handler unregistered
【DEBUG】2025-06-06 16:59:38,106 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain.discover_source
【DEBUG】2025-06-06 16:59:38,145 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain.discover_source
【DEBUG】2025-06-06 16:59:38,376 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain.discover_source
【DEBUG】2025-06-06 16:59:38,630 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain.discover_source
【DEBUG】2025-06-06 16:59:38,742 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain.discover_source
【DEBUG】2025-06-06 16:59:38,922 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain.discover_source
【DEBUG】2025-06-06 16:59:38,923 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.ExploreServices.discover_source
【DEBUG】2025-06-06 16:59:46,778 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-06 16:59:46,795 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-06 16:59:46,799 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-06 16:59:46,811 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-06 16:59:48,358 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-06 16:59:48,359 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-06 16:59:48,360 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-06 16:59:48,360 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-06 16:59:48,361 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-06 16:59:48,361 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-06 16:59:51,215 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-06 16:59:51,216 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-06 16:59:54,653 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-06 16:59:54,661 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-06 16:59:54,688 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-06 16:59:54,693 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-06 16:59:54,702 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-06 16:59:54,740 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-06 16:59:54,757 - exploreservices - [资源探索集合] Total registered APIs: 6
【INFO】2025-06-06 22:25:32,138 - exploreservices - [资源探索集合] Bangumi plugin stopped and event handler unregistered
【INFO】2025-06-06 22:25:32,141 - exploreservices - [资源探索集合] Bilibili plugin stopped and event handler unregistered
【INFO】2025-06-06 22:25:32,141 - exploreservices - [资源探索集合] MangoTV plugin stopped and event handler unregistered
【INFO】2025-06-06 22:25:32,141 - exploreservices - [资源探索集合] MiGuVideo plugin stopped and event handler unregistered
【INFO】2025-06-06 22:25:32,142 - exploreservices - [资源探索集合] CCTV plugin stopped and event handler unregistered
【INFO】2025-06-06 22:25:32,142 - exploreservices - [资源探索集合] TencentVideo plugin stopped and event handler unregistered
【DEBUG】2025-06-06 22:27:04,658 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain.discover_source
【DEBUG】2025-06-06 22:27:04,905 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain.discover_source
【DEBUG】2025-06-06 22:27:05,133 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain.discover_source
【DEBUG】2025-06-06 22:27:05,396 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain.discover_source
【DEBUG】2025-06-06 22:27:05,670 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain.discover_source
【DEBUG】2025-06-06 22:27:05,869 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain.discover_source
【DEBUG】2025-06-06 22:27:05,873 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.ExploreServices.discover_source
【DEBUG】2025-06-06 22:27:13,346 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-06 22:27:13,346 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-06 22:27:13,347 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-06 22:27:13,347 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-06 22:27:14,680 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-06 22:27:14,738 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-06 22:27:14,754 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-06 22:27:14,781 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-06 22:27:14,806 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-06 22:27:14,830 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-06 22:27:17,783 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-06 22:27:17,786 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-06 22:27:21,731 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-06 22:27:21,801 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-06 22:27:21,852 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-06 22:27:21,867 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-06 22:27:21,876 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-06 22:27:21,895 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-06 22:27:21,907 - exploreservices - [资源探索集合] Total registered APIs: 6
【DEBUG】2025-06-07 00:26:30,581 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain.discover_source
【DEBUG】2025-06-07 00:26:30,582 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain.discover_source
【DEBUG】2025-06-07 00:26:30,583 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain.discover_source
【DEBUG】2025-06-07 00:26:30,584 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain.discover_source
【DEBUG】2025-06-07 00:26:30,586 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain.discover_source
【DEBUG】2025-06-07 00:26:30,587 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain.discover_source
【DEBUG】2025-06-07 00:26:30,587 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.ExploreServices.discover_source
【DEBUG】2025-06-07 00:26:30,767 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-07 00:26:30,767 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-07 00:26:30,767 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-07 00:26:30,768 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-07 00:26:31,668 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-07 00:26:31,669 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-07 00:26:31,669 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-07 00:26:31,670 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-07 00:26:31,670 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-07 00:26:31,670 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-07 00:26:33,359 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-07 00:26:33,360 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-07 00:26:33,529 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-07 00:26:33,530 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-07 00:26:33,530 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-07 00:26:33,530 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-07 00:26:33,530 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-07 00:26:33,531 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-07 00:26:33,531 - exploreservices - [资源探索集合] Total registered APIs: 6
【DEBUG】2025-06-07 00:27:33,201 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain.discover_source
【DEBUG】2025-06-07 00:27:33,202 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain.discover_source
【DEBUG】2025-06-07 00:27:33,203 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain.discover_source
【DEBUG】2025-06-07 00:27:33,204 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain.discover_source
【DEBUG】2025-06-07 00:27:33,206 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain.discover_source
【DEBUG】2025-06-07 00:27:33,207 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain.discover_source
【DEBUG】2025-06-07 00:27:33,207 - event.py - Subscribed to chain event: discover.source, Priority: 10 - app.plugins.exploreservices.ExploreServices.discover_source
【DEBUG】2025-06-07 00:27:33,404 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-07 00:27:33,404 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-07 00:27:33,405 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-07 00:27:33,405 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-07 00:27:34,860 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-07 00:27:34,860 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-07 00:27:34,861 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-07 00:27:34,861 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-07 00:27:34,861 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-07 00:27:34,862 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-07 00:27:36,439 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-07 00:27:36,439 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-07 00:27:36,614 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-07 00:27:36,614 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-07 00:27:36,615 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-07 00:27:36,615 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-07 00:27:36,615 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-07 00:27:36,616 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-07 00:27:36,616 - exploreservices - [资源探索集合] Total registered APIs: 6
【INFO】2025-06-07 00:27:59,488 - exploreservices - [资源探索集合] Bangumi plugin stopped and event handler unregistered
【INFO】2025-06-07 00:27:59,497 - exploreservices - [资源探索集合] Bilibili plugin stopped and event handler unregistered
【INFO】2025-06-07 00:27:59,520 - exploreservices - [资源探索集合] MangoTV plugin stopped and event handler unregistered
【INFO】2025-06-07 00:27:59,526 - exploreservices - [资源探索集合] MiGuVideo plugin stopped and event handler unregistered
【INFO】2025-06-07 00:27:59,548 - exploreservices - [资源探索集合] CCTV plugin stopped and event handler unregistered
【INFO】2025-06-07 00:27:59,562 - exploreservices - [资源探索集合] TencentVideo plugin stopped and event handler unregistered
【DEBUG】2025-06-07 00:28:06,018 - event.py - Enabled event handler class - app.plugins.exploreservices.bangumidailydiscoverchain.BangumiDailyDiscoverChain
【INFO】2025-06-07 00:28:06,018 - exploreservices - [资源探索集合] Bangumi plugin enabled and event handler registered
【DEBUG】2025-06-07 00:28:06,019 - event.py - Enabled event handler class - app.plugins.exploreservices.bilibilidiscoverchain.BilibiliDiscoverChain
【INFO】2025-06-07 00:28:06,039 - exploreservices - [资源探索集合] Bilibili plugin enabled and event handler registered
【DEBUG】2025-06-07 00:28:07,991 - event.py - Enabled event handler class - app.plugins.exploreservices.mangguodiscoverchain.MangGuoDiscoverChain
【INFO】2025-06-07 00:28:08,003 - exploreservices - [资源探索集合] MangoTV plugin enabled and event handler registered
【DEBUG】2025-06-07 00:28:08,039 - event.py - Enabled event handler class - app.plugins.exploreservices.migudiscoverchain.MiGuDiscoverChain
【INFO】2025-06-07 00:28:08,040 - exploreservices - [资源探索集合] MiGuVideo plugin enabled and event handler registered
【DEBUG】2025-06-07 00:28:08,052 - event.py - Enabled event handler class - app.plugins.exploreservices.cctvdiscoverchain.CCTVDiscoverChain
【INFO】2025-06-07 00:28:08,053 - exploreservices - [资源探索集合] CCTV plugin enabled and event handler registered
【DEBUG】2025-06-07 00:28:10,675 - event.py - Enabled event handler class - app.plugins.exploreservices.tencentvideodiscoverchain.TencentVideoDiscoverChain
【INFO】2025-06-07 00:28:10,681 - exploreservices - [资源探索集合] TencentVideo plugin enabled and event handler registered
【INFO】2025-06-07 00:28:13,072 - exploreservices - [资源探索集合] Registered Bangumi APIs: 1
【INFO】2025-06-07 00:28:13,126 - exploreservices - [资源探索集合] Registered Bilibili APIs: 1
【INFO】2025-06-07 00:28:13,132 - exploreservices - [资源探索集合] Registered MangoTV APIs: 1
【INFO】2025-06-07 00:28:13,138 - exploreservices - [资源探索集合] Registered MiGuVideo APIs: 1
【INFO】2025-06-07 00:28:13,141 - exploreservices - [资源探索集合] Registered CCTV APIs: 1
【INFO】2025-06-07 00:28:13,154 - exploreservices - [资源探索集合] Registered TencentVideo APIs: 1
【INFO】2025-06-07 00:28:13,171 - exploreservices - [资源探索集合] Total registered APIs: 6
【INFO】2025-06-07 10:35:27,715 - exploreservices - [资源探索集合] Bangumi plugin stopped and event handler unregistered
【INFO】2025-06-07 10:35:27,716 - exploreservices - [资源探索集合] Bilibili plugin stopped and event handler unregistered
【INFO】2025-06-07 10:35:27,716 - exploreservices - [资源探索集合] MangoTV plugin stopped and event handler unregistered
【INFO】2025-06-07 10:35:27,716 - exploreservices - [资源探索集合] MiGuVideo plugin stopped and event handler unregistered
【INFO】2025-06-07 10:35:27,717 - exploreservices - [资源探索集合] CCTV plugin stopped and event handler unregistered
【INFO】2025-06-07 10:35:27,717 - exploreservices - [资源探索集合] TencentVideo plugin stopped and event handler unregistered
