【DEBUG】2025-06-03 20:12:49,637 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_torrent
【DEBUG】2025-06-03 20:12:49,637 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_upload_torrent
【DEBUG】2025-06-03 20:12:49,638 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_download_torrent
【DEBUG】2025-06-03 20:12:49,638 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_checking_torrent
【DEBUG】2025-06-03 20:12:49,640 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_resume_torrent
【DEBUG】2025-06-03 20:12:49,640 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_qb_status
【DEBUG】2025-06-03 20:12:49,640 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_upload_limit
【DEBUG】2025-06-03 20:12:49,641 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_download_limit
【DEBUG】2025-06-03 22:23:50,453 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_torrent
【DEBUG】2025-06-03 22:23:50,453 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_upload_torrent
【DEBUG】2025-06-03 22:23:50,454 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_download_torrent
【DEBUG】2025-06-03 22:23:50,454 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_checking_torrent
【DEBUG】2025-06-03 22:23:50,454 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_resume_torrent
【DEBUG】2025-06-03 22:23:50,455 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_qb_status
【DEBUG】2025-06-03 22:23:50,455 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_upload_limit
【DEBUG】2025-06-03 22:23:50,455 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_download_limit
【DEBUG】2025-06-04 10:23:22,513 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_torrent
【DEBUG】2025-06-04 10:23:22,513 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_upload_torrent
【DEBUG】2025-06-04 10:23:22,513 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_download_torrent
【DEBUG】2025-06-04 10:23:22,514 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_checking_torrent
【DEBUG】2025-06-04 10:23:22,514 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_resume_torrent
【DEBUG】2025-06-04 10:23:22,514 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_qb_status
【DEBUG】2025-06-04 10:23:22,515 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_upload_limit
【DEBUG】2025-06-04 10:23:22,515 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_download_limit
【DEBUG】2025-06-04 11:23:13,700 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_torrent
【DEBUG】2025-06-04 11:23:13,700 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_upload_torrent
【DEBUG】2025-06-04 11:23:13,700 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_download_torrent
【DEBUG】2025-06-04 11:23:13,701 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_checking_torrent
【DEBUG】2025-06-04 11:23:13,701 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_resume_torrent
【DEBUG】2025-06-04 11:23:13,701 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_qb_status
【DEBUG】2025-06-04 11:23:13,702 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_upload_limit
【DEBUG】2025-06-04 11:23:13,702 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_download_limit
【DEBUG】2025-06-04 16:50:12,110 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_torrent
【DEBUG】2025-06-04 16:50:12,110 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_upload_torrent
【DEBUG】2025-06-04 16:50:12,110 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_download_torrent
【DEBUG】2025-06-04 16:50:12,111 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_checking_torrent
【DEBUG】2025-06-04 16:50:12,111 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_resume_torrent
【DEBUG】2025-06-04 16:50:12,111 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_qb_status
【DEBUG】2025-06-04 16:50:12,112 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_upload_limit
【DEBUG】2025-06-04 16:50:12,112 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_download_limit
【DEBUG】2025-06-04 23:14:03,726 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_torrent
【DEBUG】2025-06-04 23:14:03,726 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_upload_torrent
【DEBUG】2025-06-04 23:14:03,726 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_download_torrent
【DEBUG】2025-06-04 23:14:03,727 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_checking_torrent
【DEBUG】2025-06-04 23:14:03,727 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_resume_torrent
【DEBUG】2025-06-04 23:14:03,727 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_qb_status
【DEBUG】2025-06-04 23:14:03,728 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_upload_limit
【DEBUG】2025-06-04 23:14:03,728 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_download_limit
【DEBUG】2025-06-04 23:14:59,266 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_torrent
【DEBUG】2025-06-04 23:14:59,266 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_upload_torrent
【DEBUG】2025-06-04 23:14:59,267 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_download_torrent
【DEBUG】2025-06-04 23:14:59,267 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_checking_torrent
【DEBUG】2025-06-04 23:14:59,267 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_resume_torrent
【DEBUG】2025-06-04 23:14:59,268 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_qb_status
【DEBUG】2025-06-04 23:14:59,268 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_upload_limit
【DEBUG】2025-06-04 23:14:59,268 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_download_limit
【DEBUG】2025-06-04 23:16:27,359 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_torrent
【DEBUG】2025-06-04 23:16:27,359 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_upload_torrent
【DEBUG】2025-06-04 23:16:27,360 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_download_torrent
【DEBUG】2025-06-04 23:16:27,360 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_checking_torrent
【DEBUG】2025-06-04 23:16:27,360 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_resume_torrent
【DEBUG】2025-06-04 23:16:27,360 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_qb_status
【DEBUG】2025-06-04 23:16:27,361 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_upload_limit
【DEBUG】2025-06-04 23:16:27,361 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_download_limit
【DEBUG】2025-06-04 23:24:04,087 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_torrent
【DEBUG】2025-06-04 23:24:04,087 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_upload_torrent
【DEBUG】2025-06-04 23:24:04,087 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_download_torrent
【DEBUG】2025-06-04 23:24:04,088 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_checking_torrent
【DEBUG】2025-06-04 23:24:04,088 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_resume_torrent
【DEBUG】2025-06-04 23:24:04,088 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_qb_status
【DEBUG】2025-06-04 23:24:04,088 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_upload_limit
【DEBUG】2025-06-04 23:24:04,089 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_download_limit
【DEBUG】2025-06-05 00:35:15,129 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_torrent
【DEBUG】2025-06-05 00:35:15,129 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_upload_torrent
【DEBUG】2025-06-05 00:35:15,129 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_download_torrent
【DEBUG】2025-06-05 00:35:15,130 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_checking_torrent
【DEBUG】2025-06-05 00:35:15,130 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_resume_torrent
【DEBUG】2025-06-05 00:35:15,130 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_qb_status
【DEBUG】2025-06-05 00:35:15,130 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_upload_limit
【DEBUG】2025-06-05 00:35:15,131 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_download_limit
【DEBUG】2025-06-05 00:36:53,697 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_torrent
【DEBUG】2025-06-05 00:36:53,697 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_upload_torrent
【DEBUG】2025-06-05 00:36:53,697 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_download_torrent
【DEBUG】2025-06-05 00:36:53,698 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_checking_torrent
【DEBUG】2025-06-05 00:36:53,698 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_resume_torrent
【DEBUG】2025-06-05 00:36:53,698 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_qb_status
【DEBUG】2025-06-05 00:36:53,699 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_upload_limit
【DEBUG】2025-06-05 00:36:53,699 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_download_limit
【DEBUG】2025-06-05 07:23:47,438 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_torrent
【DEBUG】2025-06-05 07:23:47,438 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_upload_torrent
【DEBUG】2025-06-05 07:23:47,439 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_download_torrent
【DEBUG】2025-06-05 07:23:47,440 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_checking_torrent
【DEBUG】2025-06-05 07:23:47,440 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_resume_torrent
【DEBUG】2025-06-05 07:23:47,440 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_qb_status
【DEBUG】2025-06-05 07:23:47,441 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_upload_limit
【DEBUG】2025-06-05 07:23:47,441 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_download_limit
【DEBUG】2025-06-05 16:06:29,956 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_torrent
【DEBUG】2025-06-05 16:06:29,957 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_upload_torrent
【DEBUG】2025-06-05 16:06:29,957 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_download_torrent
【DEBUG】2025-06-05 16:06:29,958 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_checking_torrent
【DEBUG】2025-06-05 16:06:29,958 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_resume_torrent
【DEBUG】2025-06-05 16:06:29,958 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_qb_status
【DEBUG】2025-06-05 16:06:29,959 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_upload_limit
【DEBUG】2025-06-05 16:06:29,959 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_download_limit
【DEBUG】2025-06-05 21:24:16,463 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_torrent
【DEBUG】2025-06-05 21:24:16,463 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_upload_torrent
【DEBUG】2025-06-05 21:24:16,464 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_download_torrent
【DEBUG】2025-06-05 21:24:16,464 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_checking_torrent
【DEBUG】2025-06-05 21:24:16,464 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_resume_torrent
【DEBUG】2025-06-05 21:24:16,464 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_qb_status
【DEBUG】2025-06-05 21:24:16,465 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_upload_limit
【DEBUG】2025-06-05 21:24:16,465 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_download_limit
【DEBUG】2025-06-05 21:36:38,335 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_torrent
【DEBUG】2025-06-05 21:36:38,339 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_upload_torrent
【DEBUG】2025-06-05 21:36:38,340 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_download_torrent
【DEBUG】2025-06-05 21:36:38,342 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_checking_torrent
【DEBUG】2025-06-05 21:36:38,344 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_resume_torrent
【DEBUG】2025-06-05 21:36:38,345 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_qb_status
【DEBUG】2025-06-05 21:36:38,345 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_upload_limit
【DEBUG】2025-06-05 21:36:38,347 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_download_limit
【DEBUG】2025-06-06 11:17:48,467 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_torrent
【DEBUG】2025-06-06 11:17:48,467 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_upload_torrent
【DEBUG】2025-06-06 11:17:48,467 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_download_torrent
【DEBUG】2025-06-06 11:17:48,468 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_checking_torrent
【DEBUG】2025-06-06 11:17:48,468 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_resume_torrent
【DEBUG】2025-06-06 11:17:48,468 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_qb_status
【DEBUG】2025-06-06 11:17:48,469 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_upload_limit
【DEBUG】2025-06-06 11:17:48,469 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_download_limit
【DEBUG】2025-06-06 16:20:29,067 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_torrent
【DEBUG】2025-06-06 16:20:29,067 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_upload_torrent
【DEBUG】2025-06-06 16:20:29,067 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_download_torrent
【DEBUG】2025-06-06 16:20:29,068 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_checking_torrent
【DEBUG】2025-06-06 16:20:29,068 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_resume_torrent
【DEBUG】2025-06-06 16:20:29,068 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_qb_status
【DEBUG】2025-06-06 16:20:29,068 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_upload_limit
【DEBUG】2025-06-06 16:20:29,069 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_download_limit
【DEBUG】2025-06-06 16:57:39,426 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_torrent
【DEBUG】2025-06-06 16:57:39,426 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_upload_torrent
【DEBUG】2025-06-06 16:57:39,427 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_download_torrent
【DEBUG】2025-06-06 16:57:39,427 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_checking_torrent
【DEBUG】2025-06-06 16:57:39,427 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_resume_torrent
【DEBUG】2025-06-06 16:57:39,428 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_qb_status
【DEBUG】2025-06-06 16:57:39,428 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_upload_limit
【DEBUG】2025-06-06 16:57:39,428 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_download_limit
【DEBUG】2025-06-06 16:58:48,604 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_torrent
【DEBUG】2025-06-06 16:58:48,604 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_upload_torrent
【DEBUG】2025-06-06 16:58:48,605 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_download_torrent
【DEBUG】2025-06-06 16:58:48,605 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_checking_torrent
【DEBUG】2025-06-06 16:58:48,605 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_resume_torrent
【DEBUG】2025-06-06 16:58:48,606 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_qb_status
【DEBUG】2025-06-06 16:58:48,606 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_upload_limit
【DEBUG】2025-06-06 16:58:48,607 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_download_limit
【DEBUG】2025-06-06 22:26:08,264 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_torrent
【DEBUG】2025-06-06 22:26:08,265 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_upload_torrent
【DEBUG】2025-06-06 22:26:08,265 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_download_torrent
【DEBUG】2025-06-06 22:26:08,266 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_checking_torrent
【DEBUG】2025-06-06 22:26:08,266 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_resume_torrent
【DEBUG】2025-06-06 22:26:08,266 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_qb_status
【DEBUG】2025-06-06 22:26:08,266 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_upload_limit
【DEBUG】2025-06-06 22:26:08,267 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_download_limit
【DEBUG】2025-06-07 00:26:30,696 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_torrent
【DEBUG】2025-06-07 00:26:30,696 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_upload_torrent
【DEBUG】2025-06-07 00:26:30,696 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_download_torrent
【DEBUG】2025-06-07 00:26:30,696 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_checking_torrent
【DEBUG】2025-06-07 00:26:30,697 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_resume_torrent
【DEBUG】2025-06-07 00:26:30,697 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_qb_status
【DEBUG】2025-06-07 00:26:30,697 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_upload_limit
【DEBUG】2025-06-07 00:26:30,698 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_download_limit
【DEBUG】2025-06-07 00:27:33,312 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_torrent
【DEBUG】2025-06-07 00:27:33,312 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_upload_torrent
【DEBUG】2025-06-07 00:27:33,312 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_download_torrent
【DEBUG】2025-06-07 00:27:33,313 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_pause_checking_torrent
【DEBUG】2025-06-07 00:27:33,313 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_resume_torrent
【DEBUG】2025-06-07 00:27:33,313 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_qb_status
【DEBUG】2025-06-07 00:27:33,313 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_upload_limit
【DEBUG】2025-06-07 00:27:33,314 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.qbcommand.QbCommand.handle_toggle_download_limit
