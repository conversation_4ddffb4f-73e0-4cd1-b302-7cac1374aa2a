【DEBUG】2025-06-03 20:12:49,666 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.speedlimiter.SpeedLimiter.check_playing_sessions
【DEBUG】2025-06-03 22:23:50,480 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.speedlimiter.SpeedLimiter.check_playing_sessions
【DEBUG】2025-06-04 10:23:22,540 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.speedlimiter.SpeedLimiter.check_playing_sessions
【DEBUG】2025-06-04 11:23:13,726 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.speedlimiter.SpeedLimiter.check_playing_sessions
【DEBUG】2025-06-04 16:50:12,122 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.speedlimiter.SpeedLimiter.check_playing_sessions
【DEBUG】2025-06-04 23:14:03,737 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.speedlimiter.SpeedLimiter.check_playing_sessions
【DEBUG】2025-06-04 23:14:59,277 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.speedlimiter.SpeedLimiter.check_playing_sessions
【DEBUG】2025-06-04 23:16:27,370 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.speedlimiter.SpeedLimiter.check_playing_sessions
【DEBUG】2025-06-04 23:24:04,097 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.speedlimiter.SpeedLimiter.check_playing_sessions
【DEBUG】2025-06-05 00:35:15,139 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.speedlimiter.SpeedLimiter.check_playing_sessions
【DEBUG】2025-06-05 00:36:53,708 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.speedlimiter.SpeedLimiter.check_playing_sessions
【DEBUG】2025-06-05 07:23:47,470 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.speedlimiter.SpeedLimiter.check_playing_sessions
【DEBUG】2025-06-05 16:06:29,969 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.speedlimiter.SpeedLimiter.check_playing_sessions
【DEBUG】2025-06-05 21:24:16,476 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.speedlimiter.SpeedLimiter.check_playing_sessions
【DEBUG】2025-06-05 21:36:38,361 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.speedlimiter.SpeedLimiter.check_playing_sessions
【DEBUG】2025-06-06 11:17:48,478 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.speedlimiter.SpeedLimiter.check_playing_sessions
【DEBUG】2025-06-06 16:20:29,078 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.speedlimiter.SpeedLimiter.check_playing_sessions
【DEBUG】2025-06-06 16:57:39,453 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.speedlimiter.SpeedLimiter.check_playing_sessions
【DEBUG】2025-06-06 16:58:48,631 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.speedlimiter.SpeedLimiter.check_playing_sessions
【DEBUG】2025-06-06 22:26:08,292 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.speedlimiter.SpeedLimiter.check_playing_sessions
【DEBUG】2025-06-07 00:26:30,707 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.speedlimiter.SpeedLimiter.check_playing_sessions
【DEBUG】2025-06-07 00:27:33,348 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.speedlimiter.SpeedLimiter.check_playing_sessions
