【DEBUG】2025-06-03 20:12:49,700 - event.py - Subscribed to broadcast event: plugin.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 20:12:49,701 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 20:12:49,701 - event.py - Subscribed to broadcast event: plugin.triggered - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 20:12:49,701 - event.py - Subscribed to broadcast event: command.excute - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 20:12:49,702 - event.py - Subscribed to broadcast event: site.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 20:12:49,702 - event.py - Subscribed to broadcast event: site.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 20:12:49,703 - event.py - Subscribed to broadcast event: site.refreshed - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 20:12:49,703 - event.py - Subscribed to broadcast event: transfer.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 20:12:49,703 - event.py - Subscribed to broadcast event: download.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 20:12:49,704 - event.py - Subscribed to broadcast event: history.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 20:12:49,704 - event.py - Subscribed to broadcast event: downloadfile.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 20:12:49,704 - event.py - Subscribed to broadcast event: download.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 20:12:49,705 - event.py - Subscribed to broadcast event: user.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 20:12:49,705 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 20:12:49,706 - event.py - Subscribed to broadcast event: notice.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 20:12:49,706 - event.py - Subscribed to broadcast event: subscribe.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 20:12:49,706 - event.py - Subscribed to broadcast event: subscribe.modified - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 20:12:49,707 - event.py - Subscribed to broadcast event: subscribe.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 20:12:49,707 - event.py - Subscribed to broadcast event: subscribe.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 20:12:49,707 - event.py - Subscribed to broadcast event: system.error - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 20:12:49,708 - event.py - Subscribed to broadcast event: metadata.scrape - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 20:12:49,708 - event.py - Subscribed to broadcast event: module.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 22:23:50,515 - event.py - Subscribed to broadcast event: plugin.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 22:23:50,515 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 22:23:50,516 - event.py - Subscribed to broadcast event: plugin.triggered - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 22:23:50,516 - event.py - Subscribed to broadcast event: command.excute - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 22:23:50,516 - event.py - Subscribed to broadcast event: site.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 22:23:50,517 - event.py - Subscribed to broadcast event: site.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 22:23:50,517 - event.py - Subscribed to broadcast event: site.refreshed - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 22:23:50,517 - event.py - Subscribed to broadcast event: transfer.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 22:23:50,518 - event.py - Subscribed to broadcast event: download.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 22:23:50,518 - event.py - Subscribed to broadcast event: history.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 22:23:50,519 - event.py - Subscribed to broadcast event: downloadfile.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 22:23:50,519 - event.py - Subscribed to broadcast event: download.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 22:23:50,519 - event.py - Subscribed to broadcast event: user.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 22:23:50,520 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 22:23:50,520 - event.py - Subscribed to broadcast event: notice.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 22:23:50,520 - event.py - Subscribed to broadcast event: subscribe.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 22:23:50,521 - event.py - Subscribed to broadcast event: subscribe.modified - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 22:23:50,521 - event.py - Subscribed to broadcast event: subscribe.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 22:23:50,521 - event.py - Subscribed to broadcast event: subscribe.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 22:23:50,522 - event.py - Subscribed to broadcast event: system.error - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 22:23:50,522 - event.py - Subscribed to broadcast event: metadata.scrape - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-03 22:23:50,522 - event.py - Subscribed to broadcast event: module.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 10:23:22,574 - event.py - Subscribed to broadcast event: plugin.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 10:23:22,574 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 10:23:22,575 - event.py - Subscribed to broadcast event: plugin.triggered - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 10:23:22,575 - event.py - Subscribed to broadcast event: command.excute - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 10:23:22,575 - event.py - Subscribed to broadcast event: site.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 10:23:22,576 - event.py - Subscribed to broadcast event: site.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 10:23:22,576 - event.py - Subscribed to broadcast event: site.refreshed - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 10:23:22,576 - event.py - Subscribed to broadcast event: transfer.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 10:23:22,577 - event.py - Subscribed to broadcast event: download.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 10:23:22,577 - event.py - Subscribed to broadcast event: history.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 10:23:22,577 - event.py - Subscribed to broadcast event: downloadfile.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 10:23:22,578 - event.py - Subscribed to broadcast event: download.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 10:23:22,578 - event.py - Subscribed to broadcast event: user.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 10:23:22,578 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 10:23:22,579 - event.py - Subscribed to broadcast event: notice.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 10:23:22,579 - event.py - Subscribed to broadcast event: subscribe.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 10:23:22,579 - event.py - Subscribed to broadcast event: subscribe.modified - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 10:23:22,580 - event.py - Subscribed to broadcast event: subscribe.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 10:23:22,580 - event.py - Subscribed to broadcast event: subscribe.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 10:23:22,580 - event.py - Subscribed to broadcast event: system.error - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 10:23:22,580 - event.py - Subscribed to broadcast event: metadata.scrape - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 10:23:22,581 - event.py - Subscribed to broadcast event: module.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 11:23:13,759 - event.py - Subscribed to broadcast event: plugin.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 11:23:13,760 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 11:23:13,760 - event.py - Subscribed to broadcast event: plugin.triggered - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 11:23:13,760 - event.py - Subscribed to broadcast event: command.excute - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 11:23:13,761 - event.py - Subscribed to broadcast event: site.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 11:23:13,761 - event.py - Subscribed to broadcast event: site.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 11:23:13,761 - event.py - Subscribed to broadcast event: site.refreshed - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 11:23:13,762 - event.py - Subscribed to broadcast event: transfer.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 11:23:13,762 - event.py - Subscribed to broadcast event: download.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 11:23:13,762 - event.py - Subscribed to broadcast event: history.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 11:23:13,762 - event.py - Subscribed to broadcast event: downloadfile.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 11:23:13,763 - event.py - Subscribed to broadcast event: download.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 11:23:13,763 - event.py - Subscribed to broadcast event: user.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 11:23:13,763 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 11:23:13,763 - event.py - Subscribed to broadcast event: notice.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 11:23:13,764 - event.py - Subscribed to broadcast event: subscribe.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 11:23:13,764 - event.py - Subscribed to broadcast event: subscribe.modified - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 11:23:13,764 - event.py - Subscribed to broadcast event: subscribe.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 11:23:13,765 - event.py - Subscribed to broadcast event: subscribe.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 11:23:13,765 - event.py - Subscribed to broadcast event: system.error - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 11:23:13,765 - event.py - Subscribed to broadcast event: metadata.scrape - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 11:23:13,765 - event.py - Subscribed to broadcast event: module.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 16:50:12,144 - event.py - Subscribed to broadcast event: plugin.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 16:50:12,144 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 16:50:12,145 - event.py - Subscribed to broadcast event: plugin.triggered - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 16:50:12,145 - event.py - Subscribed to broadcast event: command.excute - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 16:50:12,145 - event.py - Subscribed to broadcast event: site.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 16:50:12,145 - event.py - Subscribed to broadcast event: site.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 16:50:12,146 - event.py - Subscribed to broadcast event: site.refreshed - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 16:50:12,146 - event.py - Subscribed to broadcast event: transfer.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 16:50:12,146 - event.py - Subscribed to broadcast event: download.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 16:50:12,146 - event.py - Subscribed to broadcast event: history.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 16:50:12,147 - event.py - Subscribed to broadcast event: downloadfile.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 16:50:12,147 - event.py - Subscribed to broadcast event: download.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 16:50:12,147 - event.py - Subscribed to broadcast event: user.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 16:50:12,148 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 16:50:12,148 - event.py - Subscribed to broadcast event: notice.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 16:50:12,148 - event.py - Subscribed to broadcast event: subscribe.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 16:50:12,148 - event.py - Subscribed to broadcast event: subscribe.modified - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 16:50:12,149 - event.py - Subscribed to broadcast event: subscribe.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 16:50:12,149 - event.py - Subscribed to broadcast event: subscribe.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 16:50:12,149 - event.py - Subscribed to broadcast event: system.error - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 16:50:12,150 - event.py - Subscribed to broadcast event: metadata.scrape - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 16:50:12,150 - event.py - Subscribed to broadcast event: module.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:03,757 - event.py - Subscribed to broadcast event: plugin.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:03,758 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:03,758 - event.py - Subscribed to broadcast event: plugin.triggered - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:03,758 - event.py - Subscribed to broadcast event: command.excute - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:03,759 - event.py - Subscribed to broadcast event: site.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:03,759 - event.py - Subscribed to broadcast event: site.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:03,759 - event.py - Subscribed to broadcast event: site.refreshed - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:03,759 - event.py - Subscribed to broadcast event: transfer.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:03,760 - event.py - Subscribed to broadcast event: download.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:03,760 - event.py - Subscribed to broadcast event: history.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:03,760 - event.py - Subscribed to broadcast event: downloadfile.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:03,761 - event.py - Subscribed to broadcast event: download.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:03,761 - event.py - Subscribed to broadcast event: user.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:03,761 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:03,761 - event.py - Subscribed to broadcast event: notice.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:03,762 - event.py - Subscribed to broadcast event: subscribe.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:03,762 - event.py - Subscribed to broadcast event: subscribe.modified - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:03,762 - event.py - Subscribed to broadcast event: subscribe.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:03,762 - event.py - Subscribed to broadcast event: subscribe.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:03,763 - event.py - Subscribed to broadcast event: system.error - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:03,763 - event.py - Subscribed to broadcast event: metadata.scrape - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:03,763 - event.py - Subscribed to broadcast event: module.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:59,296 - event.py - Subscribed to broadcast event: plugin.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:59,296 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:59,297 - event.py - Subscribed to broadcast event: plugin.triggered - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:59,297 - event.py - Subscribed to broadcast event: command.excute - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:59,297 - event.py - Subscribed to broadcast event: site.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:59,298 - event.py - Subscribed to broadcast event: site.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:59,298 - event.py - Subscribed to broadcast event: site.refreshed - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:59,298 - event.py - Subscribed to broadcast event: transfer.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:59,298 - event.py - Subscribed to broadcast event: download.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:59,299 - event.py - Subscribed to broadcast event: history.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:59,299 - event.py - Subscribed to broadcast event: downloadfile.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:59,299 - event.py - Subscribed to broadcast event: download.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:59,299 - event.py - Subscribed to broadcast event: user.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:59,300 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:59,300 - event.py - Subscribed to broadcast event: notice.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:59,300 - event.py - Subscribed to broadcast event: subscribe.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:59,301 - event.py - Subscribed to broadcast event: subscribe.modified - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:59,301 - event.py - Subscribed to broadcast event: subscribe.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:59,301 - event.py - Subscribed to broadcast event: subscribe.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:59,301 - event.py - Subscribed to broadcast event: system.error - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:59,302 - event.py - Subscribed to broadcast event: metadata.scrape - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:14:59,302 - event.py - Subscribed to broadcast event: module.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:16:27,389 - event.py - Subscribed to broadcast event: plugin.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:16:27,390 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:16:27,390 - event.py - Subscribed to broadcast event: plugin.triggered - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:16:27,390 - event.py - Subscribed to broadcast event: command.excute - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:16:27,391 - event.py - Subscribed to broadcast event: site.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:16:27,391 - event.py - Subscribed to broadcast event: site.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:16:27,391 - event.py - Subscribed to broadcast event: site.refreshed - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:16:27,392 - event.py - Subscribed to broadcast event: transfer.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:16:27,392 - event.py - Subscribed to broadcast event: download.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:16:27,392 - event.py - Subscribed to broadcast event: history.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:16:27,392 - event.py - Subscribed to broadcast event: downloadfile.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:16:27,393 - event.py - Subscribed to broadcast event: download.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:16:27,393 - event.py - Subscribed to broadcast event: user.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:16:27,393 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:16:27,394 - event.py - Subscribed to broadcast event: notice.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:16:27,394 - event.py - Subscribed to broadcast event: subscribe.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:16:27,394 - event.py - Subscribed to broadcast event: subscribe.modified - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:16:27,395 - event.py - Subscribed to broadcast event: subscribe.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:16:27,395 - event.py - Subscribed to broadcast event: subscribe.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:16:27,395 - event.py - Subscribed to broadcast event: system.error - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:16:27,395 - event.py - Subscribed to broadcast event: metadata.scrape - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:16:27,396 - event.py - Subscribed to broadcast event: module.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:24:04,116 - event.py - Subscribed to broadcast event: plugin.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:24:04,117 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:24:04,117 - event.py - Subscribed to broadcast event: plugin.triggered - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:24:04,117 - event.py - Subscribed to broadcast event: command.excute - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:24:04,117 - event.py - Subscribed to broadcast event: site.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:24:04,118 - event.py - Subscribed to broadcast event: site.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:24:04,118 - event.py - Subscribed to broadcast event: site.refreshed - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:24:04,118 - event.py - Subscribed to broadcast event: transfer.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:24:04,118 - event.py - Subscribed to broadcast event: download.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:24:04,119 - event.py - Subscribed to broadcast event: history.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:24:04,119 - event.py - Subscribed to broadcast event: downloadfile.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:24:04,119 - event.py - Subscribed to broadcast event: download.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:24:04,119 - event.py - Subscribed to broadcast event: user.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:24:04,120 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:24:04,120 - event.py - Subscribed to broadcast event: notice.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:24:04,120 - event.py - Subscribed to broadcast event: subscribe.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:24:04,121 - event.py - Subscribed to broadcast event: subscribe.modified - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:24:04,121 - event.py - Subscribed to broadcast event: subscribe.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:24:04,121 - event.py - Subscribed to broadcast event: subscribe.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:24:04,121 - event.py - Subscribed to broadcast event: system.error - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:24:04,122 - event.py - Subscribed to broadcast event: metadata.scrape - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-04 23:24:04,122 - event.py - Subscribed to broadcast event: module.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:35:15,159 - event.py - Subscribed to broadcast event: plugin.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:35:15,160 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:35:15,160 - event.py - Subscribed to broadcast event: plugin.triggered - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:35:15,160 - event.py - Subscribed to broadcast event: command.excute - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:35:15,161 - event.py - Subscribed to broadcast event: site.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:35:15,161 - event.py - Subscribed to broadcast event: site.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:35:15,161 - event.py - Subscribed to broadcast event: site.refreshed - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:35:15,162 - event.py - Subscribed to broadcast event: transfer.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:35:15,162 - event.py - Subscribed to broadcast event: download.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:35:15,162 - event.py - Subscribed to broadcast event: history.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:35:15,163 - event.py - Subscribed to broadcast event: downloadfile.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:35:15,163 - event.py - Subscribed to broadcast event: download.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:35:15,163 - event.py - Subscribed to broadcast event: user.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:35:15,164 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:35:15,164 - event.py - Subscribed to broadcast event: notice.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:35:15,164 - event.py - Subscribed to broadcast event: subscribe.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:35:15,164 - event.py - Subscribed to broadcast event: subscribe.modified - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:35:15,165 - event.py - Subscribed to broadcast event: subscribe.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:35:15,165 - event.py - Subscribed to broadcast event: subscribe.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:35:15,165 - event.py - Subscribed to broadcast event: system.error - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:35:15,166 - event.py - Subscribed to broadcast event: metadata.scrape - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:35:15,166 - event.py - Subscribed to broadcast event: module.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:36:53,726 - event.py - Subscribed to broadcast event: plugin.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:36:53,726 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:36:53,727 - event.py - Subscribed to broadcast event: plugin.triggered - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:36:53,727 - event.py - Subscribed to broadcast event: command.excute - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:36:53,727 - event.py - Subscribed to broadcast event: site.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:36:53,727 - event.py - Subscribed to broadcast event: site.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:36:53,728 - event.py - Subscribed to broadcast event: site.refreshed - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:36:53,728 - event.py - Subscribed to broadcast event: transfer.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:36:53,728 - event.py - Subscribed to broadcast event: download.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:36:53,729 - event.py - Subscribed to broadcast event: history.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:36:53,729 - event.py - Subscribed to broadcast event: downloadfile.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:36:53,729 - event.py - Subscribed to broadcast event: download.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:36:53,729 - event.py - Subscribed to broadcast event: user.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:36:53,730 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:36:53,730 - event.py - Subscribed to broadcast event: notice.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:36:53,730 - event.py - Subscribed to broadcast event: subscribe.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:36:53,731 - event.py - Subscribed to broadcast event: subscribe.modified - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:36:53,731 - event.py - Subscribed to broadcast event: subscribe.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:36:53,731 - event.py - Subscribed to broadcast event: subscribe.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:36:53,731 - event.py - Subscribed to broadcast event: system.error - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:36:53,732 - event.py - Subscribed to broadcast event: metadata.scrape - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 00:36:53,732 - event.py - Subscribed to broadcast event: module.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 07:23:47,507 - event.py - Subscribed to broadcast event: plugin.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 07:23:47,508 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 07:23:47,508 - event.py - Subscribed to broadcast event: plugin.triggered - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 07:23:47,508 - event.py - Subscribed to broadcast event: command.excute - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 07:23:47,509 - event.py - Subscribed to broadcast event: site.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 07:23:47,509 - event.py - Subscribed to broadcast event: site.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 07:23:47,509 - event.py - Subscribed to broadcast event: site.refreshed - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 07:23:47,510 - event.py - Subscribed to broadcast event: transfer.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 07:23:47,510 - event.py - Subscribed to broadcast event: download.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 07:23:47,510 - event.py - Subscribed to broadcast event: history.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 07:23:47,511 - event.py - Subscribed to broadcast event: downloadfile.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 07:23:47,511 - event.py - Subscribed to broadcast event: download.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 07:23:47,512 - event.py - Subscribed to broadcast event: user.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 07:23:47,512 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 07:23:47,512 - event.py - Subscribed to broadcast event: notice.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 07:23:47,513 - event.py - Subscribed to broadcast event: subscribe.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 07:23:47,513 - event.py - Subscribed to broadcast event: subscribe.modified - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 07:23:47,513 - event.py - Subscribed to broadcast event: subscribe.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 07:23:47,514 - event.py - Subscribed to broadcast event: subscribe.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 07:23:47,514 - event.py - Subscribed to broadcast event: system.error - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 07:23:47,514 - event.py - Subscribed to broadcast event: metadata.scrape - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 07:23:47,515 - event.py - Subscribed to broadcast event: module.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 16:06:29,991 - event.py - Subscribed to broadcast event: plugin.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 16:06:29,991 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 16:06:29,992 - event.py - Subscribed to broadcast event: plugin.triggered - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 16:06:29,992 - event.py - Subscribed to broadcast event: command.excute - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 16:06:29,993 - event.py - Subscribed to broadcast event: site.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 16:06:29,993 - event.py - Subscribed to broadcast event: site.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 16:06:29,993 - event.py - Subscribed to broadcast event: site.refreshed - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 16:06:29,994 - event.py - Subscribed to broadcast event: transfer.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 16:06:29,994 - event.py - Subscribed to broadcast event: download.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 16:06:29,994 - event.py - Subscribed to broadcast event: history.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 16:06:29,995 - event.py - Subscribed to broadcast event: downloadfile.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 16:06:29,995 - event.py - Subscribed to broadcast event: download.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 16:06:29,995 - event.py - Subscribed to broadcast event: user.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 16:06:29,996 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 16:06:29,996 - event.py - Subscribed to broadcast event: notice.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 16:06:29,996 - event.py - Subscribed to broadcast event: subscribe.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 16:06:29,997 - event.py - Subscribed to broadcast event: subscribe.modified - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 16:06:29,997 - event.py - Subscribed to broadcast event: subscribe.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 16:06:29,997 - event.py - Subscribed to broadcast event: subscribe.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 16:06:29,998 - event.py - Subscribed to broadcast event: system.error - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 16:06:29,998 - event.py - Subscribed to broadcast event: metadata.scrape - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 16:06:29,999 - event.py - Subscribed to broadcast event: module.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:24:16,518 - event.py - Subscribed to broadcast event: plugin.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:24:16,518 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:24:16,519 - event.py - Subscribed to broadcast event: plugin.triggered - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:24:16,519 - event.py - Subscribed to broadcast event: command.excute - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:24:16,519 - event.py - Subscribed to broadcast event: site.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:24:16,520 - event.py - Subscribed to broadcast event: site.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:24:16,520 - event.py - Subscribed to broadcast event: site.refreshed - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:24:16,520 - event.py - Subscribed to broadcast event: transfer.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:24:16,521 - event.py - Subscribed to broadcast event: download.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:24:16,521 - event.py - Subscribed to broadcast event: history.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:24:16,521 - event.py - Subscribed to broadcast event: downloadfile.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:24:16,522 - event.py - Subscribed to broadcast event: download.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:24:16,522 - event.py - Subscribed to broadcast event: user.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:24:16,522 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:24:16,523 - event.py - Subscribed to broadcast event: notice.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:24:16,523 - event.py - Subscribed to broadcast event: subscribe.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:24:16,523 - event.py - Subscribed to broadcast event: subscribe.modified - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:24:16,524 - event.py - Subscribed to broadcast event: subscribe.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:24:16,524 - event.py - Subscribed to broadcast event: subscribe.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:24:16,524 - event.py - Subscribed to broadcast event: system.error - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:24:16,525 - event.py - Subscribed to broadcast event: metadata.scrape - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:24:16,525 - event.py - Subscribed to broadcast event: module.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:36:38,383 - event.py - Subscribed to broadcast event: plugin.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:36:38,383 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:36:38,384 - event.py - Subscribed to broadcast event: plugin.triggered - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:36:38,384 - event.py - Subscribed to broadcast event: command.excute - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:36:38,384 - event.py - Subscribed to broadcast event: site.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:36:38,385 - event.py - Subscribed to broadcast event: site.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:36:38,385 - event.py - Subscribed to broadcast event: site.refreshed - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:36:38,385 - event.py - Subscribed to broadcast event: transfer.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:36:38,386 - event.py - Subscribed to broadcast event: download.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:36:38,386 - event.py - Subscribed to broadcast event: history.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:36:38,386 - event.py - Subscribed to broadcast event: downloadfile.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:36:38,387 - event.py - Subscribed to broadcast event: download.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:36:38,387 - event.py - Subscribed to broadcast event: user.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:36:38,387 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:36:38,388 - event.py - Subscribed to broadcast event: notice.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:36:38,388 - event.py - Subscribed to broadcast event: subscribe.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:36:38,388 - event.py - Subscribed to broadcast event: subscribe.modified - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:36:38,389 - event.py - Subscribed to broadcast event: subscribe.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:36:38,389 - event.py - Subscribed to broadcast event: subscribe.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:36:38,389 - event.py - Subscribed to broadcast event: system.error - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:36:38,390 - event.py - Subscribed to broadcast event: metadata.scrape - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-05 21:36:38,390 - event.py - Subscribed to broadcast event: module.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 11:17:48,500 - event.py - Subscribed to broadcast event: plugin.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 11:17:48,500 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 11:17:48,500 - event.py - Subscribed to broadcast event: plugin.triggered - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 11:17:48,500 - event.py - Subscribed to broadcast event: command.excute - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 11:17:48,501 - event.py - Subscribed to broadcast event: site.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 11:17:48,501 - event.py - Subscribed to broadcast event: site.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 11:17:48,501 - event.py - Subscribed to broadcast event: site.refreshed - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 11:17:48,502 - event.py - Subscribed to broadcast event: transfer.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 11:17:48,502 - event.py - Subscribed to broadcast event: download.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 11:17:48,502 - event.py - Subscribed to broadcast event: history.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 11:17:48,503 - event.py - Subscribed to broadcast event: downloadfile.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 11:17:48,503 - event.py - Subscribed to broadcast event: download.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 11:17:48,503 - event.py - Subscribed to broadcast event: user.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 11:17:48,503 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 11:17:48,504 - event.py - Subscribed to broadcast event: notice.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 11:17:48,504 - event.py - Subscribed to broadcast event: subscribe.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 11:17:48,504 - event.py - Subscribed to broadcast event: subscribe.modified - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 11:17:48,504 - event.py - Subscribed to broadcast event: subscribe.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 11:17:48,505 - event.py - Subscribed to broadcast event: subscribe.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 11:17:48,505 - event.py - Subscribed to broadcast event: system.error - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 11:17:48,505 - event.py - Subscribed to broadcast event: metadata.scrape - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 11:17:48,507 - event.py - Subscribed to broadcast event: module.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:20:29,098 - event.py - Subscribed to broadcast event: plugin.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:20:29,098 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:20:29,099 - event.py - Subscribed to broadcast event: plugin.triggered - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:20:29,099 - event.py - Subscribed to broadcast event: command.excute - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:20:29,099 - event.py - Subscribed to broadcast event: site.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:20:29,099 - event.py - Subscribed to broadcast event: site.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:20:29,100 - event.py - Subscribed to broadcast event: site.refreshed - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:20:29,100 - event.py - Subscribed to broadcast event: transfer.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:20:29,100 - event.py - Subscribed to broadcast event: download.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:20:29,101 - event.py - Subscribed to broadcast event: history.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:20:29,101 - event.py - Subscribed to broadcast event: downloadfile.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:20:29,101 - event.py - Subscribed to broadcast event: download.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:20:29,101 - event.py - Subscribed to broadcast event: user.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:20:29,102 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:20:29,102 - event.py - Subscribed to broadcast event: notice.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:20:29,102 - event.py - Subscribed to broadcast event: subscribe.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:20:29,103 - event.py - Subscribed to broadcast event: subscribe.modified - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:20:29,103 - event.py - Subscribed to broadcast event: subscribe.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:20:29,103 - event.py - Subscribed to broadcast event: subscribe.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:20:29,103 - event.py - Subscribed to broadcast event: system.error - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:20:29,104 - event.py - Subscribed to broadcast event: metadata.scrape - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:20:29,104 - event.py - Subscribed to broadcast event: module.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:57:39,487 - event.py - Subscribed to broadcast event: plugin.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:57:39,487 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:57:39,487 - event.py - Subscribed to broadcast event: plugin.triggered - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:57:39,488 - event.py - Subscribed to broadcast event: command.excute - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:57:39,488 - event.py - Subscribed to broadcast event: site.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:57:39,488 - event.py - Subscribed to broadcast event: site.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:57:39,489 - event.py - Subscribed to broadcast event: site.refreshed - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:57:39,489 - event.py - Subscribed to broadcast event: transfer.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:57:39,489 - event.py - Subscribed to broadcast event: download.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:57:39,490 - event.py - Subscribed to broadcast event: history.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:57:39,490 - event.py - Subscribed to broadcast event: downloadfile.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:57:39,490 - event.py - Subscribed to broadcast event: download.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:57:39,490 - event.py - Subscribed to broadcast event: user.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:57:39,491 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:57:39,491 - event.py - Subscribed to broadcast event: notice.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:57:39,491 - event.py - Subscribed to broadcast event: subscribe.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:57:39,492 - event.py - Subscribed to broadcast event: subscribe.modified - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:57:39,492 - event.py - Subscribed to broadcast event: subscribe.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:57:39,492 - event.py - Subscribed to broadcast event: subscribe.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:57:39,492 - event.py - Subscribed to broadcast event: system.error - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:57:39,493 - event.py - Subscribed to broadcast event: metadata.scrape - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:57:39,493 - event.py - Subscribed to broadcast event: module.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:58:48,665 - event.py - Subscribed to broadcast event: plugin.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:58:48,665 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:58:48,666 - event.py - Subscribed to broadcast event: plugin.triggered - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:58:48,666 - event.py - Subscribed to broadcast event: command.excute - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:58:48,666 - event.py - Subscribed to broadcast event: site.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:58:48,667 - event.py - Subscribed to broadcast event: site.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:58:48,667 - event.py - Subscribed to broadcast event: site.refreshed - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:58:48,668 - event.py - Subscribed to broadcast event: transfer.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:58:48,668 - event.py - Subscribed to broadcast event: download.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:58:48,669 - event.py - Subscribed to broadcast event: history.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:58:48,669 - event.py - Subscribed to broadcast event: downloadfile.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:58:48,669 - event.py - Subscribed to broadcast event: download.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:58:48,670 - event.py - Subscribed to broadcast event: user.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:58:48,670 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:58:48,671 - event.py - Subscribed to broadcast event: notice.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:58:48,671 - event.py - Subscribed to broadcast event: subscribe.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:58:48,671 - event.py - Subscribed to broadcast event: subscribe.modified - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:58:48,672 - event.py - Subscribed to broadcast event: subscribe.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:58:48,672 - event.py - Subscribed to broadcast event: subscribe.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:58:48,673 - event.py - Subscribed to broadcast event: system.error - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:58:48,673 - event.py - Subscribed to broadcast event: metadata.scrape - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:58:48,673 - event.py - Subscribed to broadcast event: module.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 16:58:48,674 - event.py - Subscribed to broadcast event: config.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 22:26:08,331 - event.py - Subscribed to broadcast event: plugin.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 22:26:08,331 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 22:26:08,332 - event.py - Subscribed to broadcast event: plugin.triggered - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 22:26:08,332 - event.py - Subscribed to broadcast event: command.excute - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 22:26:08,332 - event.py - Subscribed to broadcast event: site.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 22:26:08,332 - event.py - Subscribed to broadcast event: site.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 22:26:08,333 - event.py - Subscribed to broadcast event: site.refreshed - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 22:26:08,333 - event.py - Subscribed to broadcast event: transfer.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 22:26:08,333 - event.py - Subscribed to broadcast event: download.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 22:26:08,334 - event.py - Subscribed to broadcast event: history.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 22:26:08,334 - event.py - Subscribed to broadcast event: downloadfile.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 22:26:08,334 - event.py - Subscribed to broadcast event: download.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 22:26:08,335 - event.py - Subscribed to broadcast event: user.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 22:26:08,335 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 22:26:08,335 - event.py - Subscribed to broadcast event: notice.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 22:26:08,335 - event.py - Subscribed to broadcast event: subscribe.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 22:26:08,336 - event.py - Subscribed to broadcast event: subscribe.modified - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 22:26:08,336 - event.py - Subscribed to broadcast event: subscribe.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 22:26:08,337 - event.py - Subscribed to broadcast event: subscribe.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 22:26:08,337 - event.py - Subscribed to broadcast event: system.error - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 22:26:08,337 - event.py - Subscribed to broadcast event: metadata.scrape - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 22:26:08,338 - event.py - Subscribed to broadcast event: module.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-06 22:26:08,338 - event.py - Subscribed to broadcast event: config.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:26:30,729 - event.py - Subscribed to broadcast event: plugin.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:26:30,730 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:26:30,730 - event.py - Subscribed to broadcast event: plugin.triggered - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:26:30,731 - event.py - Subscribed to broadcast event: command.excute - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:26:30,731 - event.py - Subscribed to broadcast event: site.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:26:30,732 - event.py - Subscribed to broadcast event: site.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:26:30,732 - event.py - Subscribed to broadcast event: site.refreshed - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:26:30,732 - event.py - Subscribed to broadcast event: transfer.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:26:30,732 - event.py - Subscribed to broadcast event: download.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:26:30,733 - event.py - Subscribed to broadcast event: history.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:26:30,733 - event.py - Subscribed to broadcast event: downloadfile.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:26:30,733 - event.py - Subscribed to broadcast event: download.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:26:30,733 - event.py - Subscribed to broadcast event: user.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:26:30,734 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:26:30,734 - event.py - Subscribed to broadcast event: notice.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:26:30,734 - event.py - Subscribed to broadcast event: subscribe.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:26:30,735 - event.py - Subscribed to broadcast event: subscribe.modified - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:26:30,735 - event.py - Subscribed to broadcast event: subscribe.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:26:30,735 - event.py - Subscribed to broadcast event: subscribe.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:26:30,736 - event.py - Subscribed to broadcast event: system.error - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:26:30,736 - event.py - Subscribed to broadcast event: metadata.scrape - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:26:30,736 - event.py - Subscribed to broadcast event: module.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:26:30,736 - event.py - Subscribed to broadcast event: config.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:27:33,368 - event.py - Subscribed to broadcast event: plugin.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:27:33,368 - event.py - Subscribed to broadcast event: plugin.action - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:27:33,368 - event.py - Subscribed to broadcast event: plugin.triggered - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:27:33,369 - event.py - Subscribed to broadcast event: command.excute - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:27:33,369 - event.py - Subscribed to broadcast event: site.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:27:33,369 - event.py - Subscribed to broadcast event: site.updated - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:27:33,370 - event.py - Subscribed to broadcast event: site.refreshed - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:27:33,370 - event.py - Subscribed to broadcast event: transfer.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:27:33,370 - event.py - Subscribed to broadcast event: download.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:27:33,370 - event.py - Subscribed to broadcast event: history.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:27:33,371 - event.py - Subscribed to broadcast event: downloadfile.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:27:33,371 - event.py - Subscribed to broadcast event: download.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:27:33,371 - event.py - Subscribed to broadcast event: user.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:27:33,372 - event.py - Subscribed to broadcast event: webhook.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:27:33,372 - event.py - Subscribed to broadcast event: notice.message - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:27:33,372 - event.py - Subscribed to broadcast event: subscribe.added - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:27:33,373 - event.py - Subscribed to broadcast event: subscribe.modified - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:27:33,373 - event.py - Subscribed to broadcast event: subscribe.deleted - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:27:33,373 - event.py - Subscribed to broadcast event: subscribe.complete - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:27:33,374 - event.py - Subscribed to broadcast event: system.error - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:27:33,374 - event.py - Subscribed to broadcast event: metadata.scrape - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:27:33,374 - event.py - Subscribed to broadcast event: module.reload - app.plugins.webhook.WebHook.send
【DEBUG】2025-06-07 00:27:33,374 - event.py - Subscribed to broadcast event: config.updated - app.plugins.webhook.WebHook.send
