# MoviePilot 快速部署脚本 (PowerShell 版本)
# 作者: alxxxxla
# 用途: 一键部署和配置 MoviePilot 系统

param(
    [string]$Action = "deploy"
)

# 颜色函数
function Write-ColorOutput {
    param(
        [string]$Message,
        [string]$Color = "White"
    )
    Write-Host $Message -ForegroundColor $Color
}

function Write-Info {
    param([string]$Message)
    Write-ColorOutput "[INFO] $Message" "Green"
}

function Write-Warn {
    param([string]$Message)
    Write-ColorOutput "[WARN] $Message" "Yellow"
}

function Write-Error {
    param([string]$Message)
    Write-ColorOutput "[ERROR] $Message" "Red"
}

function Write-Step {
    param([string]$Message)
    Write-ColorOutput "[STEP] $Message" "Cyan"
}

# 获取项目目录
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectDir = Split-Path -Parent $ScriptDir

# 检查系统要求
function Test-Requirements {
    Write-Step "检查系统要求..."
    
    # 检查 Docker
    try {
        docker --version | Out-Null
        Write-Info "Docker 已安装"
    }
    catch {
        Write-Error "Docker 未安装，请先安装 Docker Desktop"
        exit 1
    }
    
    # 检查 Docker Compose
    try {
        docker-compose --version | Out-Null
        Write-Info "Docker Compose 已安装"
    }
    catch {
        try {
            docker compose version | Out-Null
            Write-Info "Docker Compose (v2) 已安装"
        }
        catch {
            Write-Error "Docker Compose 未安装"
            exit 1
        }
    }
    
    # 检查磁盘空间
    $Drive = (Get-Location).Drive
    $FreeSpace = (Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='$($Drive.Name)'").FreeSpace / 1GB
    
    if ($FreeSpace -lt 50) {
        Write-Warn "可用磁盘空间不足 50GB，当前可用: $([math]::Round($FreeSpace, 2))GB"
    }
    else {
        Write-Info "磁盘空间充足: $([math]::Round($FreeSpace, 2))GB"
    }
    
    Write-Info "系统要求检查完成"
}

# 创建目录结构
function New-DirectoryStructure {
    Write-Step "创建目录结构..."
    
    $Directories = @(
        "media\downloads",
        "media\links\华语电影",
        "media\links\外语电影",
        "media\links\动画电影",
        "media\links\国产剧",
        "media\links\欧美剧",
        "media\links\日韩剧",
        "media\links\国漫",
        "media\links\日番",
        "media\links\纪录片",
        "media\links\综艺",
        "media\links\儿童",
        "media\links\未分类",
        "moviepilot\config",
        "moviepilot\core",
        "emby\config",
        "qbittorrent\config",
        "transmission\config",
        "chinesesubfinder\config",
        "chinesesubfinder\browser"
    )
    
    foreach ($Dir in $Directories) {
        $FullPath = Join-Path $ProjectDir $Dir
        if (!(Test-Path $FullPath)) {
            New-Item -ItemType Directory -Path $FullPath -Force | Out-Null
        }
    }
    
    Write-Info "目录结构创建完成"
}

# 生成随机密码
function New-RandomPassword {
    param([int]$Length = 12)
    
    $Chars = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    $Password = ""
    for ($i = 0; $i -lt $Length; $i++) {
        $Password += $Chars[(Get-Random -Maximum $Chars.Length)]
    }
    return $Password
}

# 交互式配置
function Set-InteractiveConfiguration {
    Write-Step "交互式配置..."
    
    Write-Host "请输入以下配置信息（按回车使用默认值）："
    
    # 管理员用户名
    $Superuser = Read-Host "MoviePilot 管理员用户名 [admin]"
    if ([string]::IsNullOrEmpty($Superuser)) { $Superuser = "admin" }
    
    # GitHub Token
    $GitHubToken = Read-Host "GitHub Token（可选，用于插件更新）"
    
    # API Token
    $ApiToken = New-RandomPassword
    $InputApiToken = Read-Host "API Token [$ApiToken]"
    if (![string]::IsNullOrEmpty($InputApiToken)) { $ApiToken = $InputApiToken }
    
    # Transmission 密码
    $TransmissionPass = New-RandomPassword
    $InputTransmissionPass = Read-Host "Transmission 密码 [$TransmissionPass]"
    if (![string]::IsNullOrEmpty($InputTransmissionPass)) { $TransmissionPass = $InputTransmissionPass }
    
    # 用户 ID
    $Puid = Read-Host "用户 ID (PUID) [1000]"
    if ([string]::IsNullOrEmpty($Puid)) { $Puid = "1000" }
    
    $Pgid = Read-Host "组 ID (PGID) [1000]"
    if ([string]::IsNullOrEmpty($Pgid)) { $Pgid = "1000" }
    
    # 写入 .env 文件
    $EnvContent = @"
# MoviePilot 环境变量配置
# 生成时间: $(Get-Date)

# MoviePilot 配置
MOVIEPILOT_SUPERUSER=$Superuser
MOVIEPILOT_GITHUB_TOKEN=$GitHubToken
MOVIEPILOT_API_TOKEN=$ApiToken

# Transmission 配置
TRANSMISSION_USER=admin
TRANSMISSION_PASS=$TransmissionPass

# 时区设置
TZ=Asia/Shanghai

# 用户权限设置
PUID=$Puid
PGID=$Pgid
UMASK=022

# 代理设置（可选）
GITHUB_PROXY=https://ghfast.top/
PIP_PROXY=https://pypi.mirrors.ustc.edu.cn/simple

# TMDB 配置
TMDB_API_DOMAIN=tmdb.movie-pilot.org
TMDB_IMAGE_DOMAIN=static-mdb.v.geilijiasu.com
"@
    
    $EnvPath = Join-Path $ProjectDir ".env"
    $EnvContent | Out-File -FilePath $EnvPath -Encoding UTF8
    
    Write-Info "环境变量配置完成"
}

# 启动服务
function Start-Services {
    Write-Step "启动服务..."
    
    Set-Location $ProjectDir
    
    # 选择配置文件
    $ComposeFile = "docker-compose.yml"
    $OptimizedFile = "docker-compose.optimized.yml"
    
    if (Test-Path $OptimizedFile) {
        $UseOptimized = Read-Host "使用优化后的配置文件？[Y/n]"
        if ([string]::IsNullOrEmpty($UseOptimized) -or $UseOptimized -match "^[Yy]") {
            $ComposeFile = $OptimizedFile
            Write-Info "使用优化后的配置文件"
        }
    }
    
    # 拉取镜像
    Write-Info "拉取 Docker 镜像..."
    docker-compose -f $ComposeFile pull
    
    # 启动服务
    Write-Info "启动服务..."
    docker-compose -f $ComposeFile up -d
    
    # 等待服务启动
    Write-Info "等待服务启动..."
    Start-Sleep -Seconds 30
    
    # 检查服务状态
    docker-compose -f $ComposeFile ps
}

# 验证部署
function Test-Deployment {
    Write-Step "验证部署..."
    
    $Services = @(
        @{Name="MoviePilot"; Port=3000},
        @{Name="Emby"; Port=8096},
        @{Name="qBittorrent"; Port=8099},
        @{Name="Transmission"; Port=9091}
    )
    
    $Failed = 0
    
    foreach ($Service in $Services) {
        try {
            $Connection = Test-NetConnection -ComputerName "localhost" -Port $Service.Port -WarningAction SilentlyContinue
            if ($Connection.TcpTestSucceeded) {
                Write-Info "$($Service.Name) 服务正常 (端口 $($Service.Port))"
            }
            else {
                Write-Error "$($Service.Name) 服务异常 (端口 $($Service.Port))"
                $Failed++
            }
        }
        catch {
            Write-Error "$($Service.Name) 服务检查失败 (端口 $($Service.Port))"
            $Failed++
        }
    }
    
    if ($Failed -eq 0) {
        Write-Info "所有服务部署成功！"
        return $true
    }
    else {
        Write-Error "$Failed 个服务部署失败"
        return $false
    }
}

# 显示访问信息
function Show-AccessInfo {
    Write-Step "访问信息"
    
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host "🎉 MoviePilot 部署完成！" -ForegroundColor Green
    Write-Host "========================================" -ForegroundColor Cyan
    Write-Host ""
    Write-Host "📱 Web 访问地址:" -ForegroundColor Yellow
    Write-Host "  MoviePilot:    http://localhost:3000"
    Write-Host "  Emby:          http://localhost:8096"
    Write-Host "  qBittorrent:   http://localhost:8099"
    Write-Host "  Transmission:  http://localhost:9091"
    Write-Host "  字幕下载器:     http://localhost:19035"
    Write-Host ""
    Write-Host "🔑 默认登录信息:" -ForegroundColor Yellow
    Write-Host "  MoviePilot:    用户名见 .env 文件"
    Write-Host "  Transmission:  admin / 见 .env 文件"
    Write-Host "  qBittorrent:   admin / adminadmin"
    Write-Host ""
    Write-Host "📁 重要目录:" -ForegroundColor Yellow
    Write-Host "  媒体库:        $ProjectDir\media\links\"
    Write-Host "  下载目录:      $ProjectDir\media\downloads\"
    Write-Host "  配置文件:      $ProjectDir\.env"
    Write-Host ""
    Write-Host "🔧 管理命令:" -ForegroundColor Yellow
    Write-Host "  系统监控:      .\scripts\monitor.ps1"
    Write-Host "  查看日志:      docker-compose logs -f"
    Write-Host ""
    Write-Host "📚 更多信息请查看 README.md 文件" -ForegroundColor Yellow
    Write-Host "========================================" -ForegroundColor Cyan
}

# 主函数
function Main {
    Write-Host "MoviePilot 快速部署脚本 (PowerShell)" -ForegroundColor Cyan
    Write-Host "维护者: alxxxxla" -ForegroundColor Cyan
    Write-Host "========================================" -ForegroundColor Cyan
    
    switch ($Action.ToLower()) {
        "deploy" {
            Test-Requirements
            New-DirectoryStructure
            Set-InteractiveConfiguration
            Start-Services
            if (Test-Deployment) {
                Show-AccessInfo
            }
            else {
                Write-Error "部署过程中出现问题，请检查日志"
                exit 1
            }
        }
        "update" {
            Write-Step "更新系统..."
            Set-Location $ProjectDir
            docker-compose pull
            docker-compose up -d
            Test-Deployment
        }
        "reset" {
            Write-Warn "这将删除所有配置和数据！"
            $Confirm = Read-Host "确认重置系统？[y/N]"
            if ($Confirm -match "^[Yy]") {
                Set-Location $ProjectDir
                docker-compose down -v
                Remove-Item -Path "*\config\" -Recurse -Force -ErrorAction SilentlyContinue
                Remove-Item -Path ".env" -Force -ErrorAction SilentlyContinue
                Write-Info "系统已重置"
            }
        }
        default {
            Write-Host "用法: .\deploy.ps1 [deploy|update|reset]"
            Write-Host "  deploy - 完整部署（默认）"
            Write-Host "  update - 更新系统"
            Write-Host "  reset  - 重置系统"
            exit 1
        }
    }
}

# 执行主函数
Main
