#!/bin/bash

# MoviePilot 快速部署脚本
# 作者: alxxxxla
# 用途: 一键部署和配置 MoviePilot 系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

# 检查系统要求
check_requirements() {
    log_step "检查系统要求..."
    
    # 检查 Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    # 检查 Docker Compose
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    # 检查磁盘空间
    local available_space=$(df "$PROJECT_DIR" | tail -1 | awk '{print $4}')
    local required_space=$((50 * 1024 * 1024)) # 50GB in KB
    
    if [ "$available_space" -lt "$required_space" ]; then
        log_warn "可用磁盘空间不足 50GB，建议清理磁盘空间"
    fi
    
    # 检查内存
    local total_mem=$(free -m | grep '^Mem:' | awk '{print $2}')
    if [ "$total_mem" -lt 4096 ]; then
        log_warn "系统内存少于 4GB，可能影响性能"
    fi
    
    log_info "系统要求检查完成"
}

# 创建目录结构
create_directories() {
    log_step "创建目录结构..."
    
    # 创建必要的目录
    mkdir -p "$PROJECT_DIR"/{media/{downloads,links},scripts}
    mkdir -p "$PROJECT_DIR"/media/links/{华语电影,外语电影,动画电影,国产剧,欧美剧,日韩剧,国漫,日番,纪录片,综艺,儿童,未分类}
    mkdir -p "$PROJECT_DIR"/{moviepilot,emby,qbittorrent,transmission,chinesesubfinder}/config
    mkdir -p "$PROJECT_DIR"/chinesesubfinder/browser
    mkdir -p "$PROJECT_DIR"/moviepilot/core
    
    # 设置权限
    chmod -R 755 "$PROJECT_DIR"/media
    chmod -R 755 "$PROJECT_DIR"/scripts
    
    log_info "目录结构创建完成"
}

# 配置环境变量
setup_environment() {
    log_step "配置环境变量..."
    
    if [ ! -f "$PROJECT_DIR/.env" ]; then
        if [ -f "$PROJECT_DIR/.env.example" ]; then
            cp "$PROJECT_DIR/.env.example" "$PROJECT_DIR/.env"
            log_info "已创建 .env 文件，请编辑配置"
        else
            log_warn ".env.example 文件不存在，跳过环境变量配置"
        fi
    else
        log_info ".env 文件已存在"
    fi
}

# 生成随机密码
generate_password() {
    openssl rand -base64 12 2>/dev/null || date +%s | sha256sum | base64 | head -c 12
}

# 交互式配置
interactive_setup() {
    log_step "交互式配置..."
    
    echo "请输入以下配置信息（按回车使用默认值）："
    
    # 管理员用户名
    read -p "MoviePilot 管理员用户名 [admin]: " superuser
    superuser=${superuser:-admin}
    
    # GitHub Token
    read -p "GitHub Token（可选，用于插件更新）: " github_token
    
    # API Token
    api_token=$(generate_password)
    read -p "API Token [$api_token]: " input_api_token
    api_token=${input_api_token:-$api_token}
    
    # Transmission 密码
    transmission_pass=$(generate_password)
    read -p "Transmission 密码 [$transmission_pass]: " input_transmission_pass
    transmission_pass=${input_transmission_pass:-$transmission_pass}
    
    # 用户 ID
    read -p "用户 ID (PUID) [1000]: " puid
    puid=${puid:-1000}
    
    read -p "组 ID (PGID) [1000]: " pgid
    pgid=${pgid:-1000}
    
    # 写入 .env 文件
    cat > "$PROJECT_DIR/.env" << EOF
# MoviePilot 环境变量配置
# 生成时间: $(date)

# MoviePilot 配置
MOVIEPILOT_SUPERUSER=$superuser
MOVIEPILOT_GITHUB_TOKEN=$github_token
MOVIEPILOT_API_TOKEN=$api_token

# Transmission 配置
TRANSMISSION_USER=admin
TRANSMISSION_PASS=$transmission_pass

# 时区设置
TZ=Asia/Shanghai

# 用户权限设置
PUID=$puid
PGID=$pgid
UMASK=022

# 代理设置（可选）
GITHUB_PROXY=https://ghfast.top/
PIP_PROXY=https://pypi.mirrors.ustc.edu.cn/simple

# TMDB 配置
TMDB_API_DOMAIN=tmdb.movie-pilot.org
TMDB_IMAGE_DOMAIN=static-mdb.v.geilijiasu.com
EOF
    
    log_info "环境变量配置完成"
}

# 启动服务
start_services() {
    log_step "启动服务..."
    
    cd "$PROJECT_DIR"
    
    # 选择配置文件
    local compose_file="docker-compose.yml"
    if [ -f "docker-compose.optimized.yml" ]; then
        read -p "使用优化后的配置文件？[Y/n]: " use_optimized
        if [[ $use_optimized =~ ^[Yy]$ ]] || [[ -z $use_optimized ]]; then
            compose_file="docker-compose.optimized.yml"
            log_info "使用优化后的配置文件"
        fi
    fi
    
    # 拉取镜像
    log_info "拉取 Docker 镜像..."
    docker-compose -f "$compose_file" pull
    
    # 启动服务
    log_info "启动服务..."
    docker-compose -f "$compose_file" up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 30
    
    # 检查服务状态
    docker-compose -f "$compose_file" ps
}

# 验证部署
verify_deployment() {
    log_step "验证部署..."
    
    local services=("moviepilot-v2:3000" "emby:8096" "qbittorrent:8099" "transmission:9091")
    local failed=0
    
    for service in "${services[@]}"; do
        local name=$(echo "$service" | cut -d: -f1)
        local port=$(echo "$service" | cut -d: -f2)
        
        if nc -z localhost "$port" 2>/dev/null; then
            log_info "$name 服务正常 (端口 $port)"
        else
            log_error "$name 服务异常 (端口 $port)"
            ((failed++))
        fi
    done
    
    if [ "$failed" -eq 0 ]; then
        log_info "所有服务部署成功！"
        return 0
    else
        log_error "$failed 个服务部署失败"
        return 1
    fi
}

# 显示访问信息
show_access_info() {
    log_step "访问信息"
    
    echo "========================================"
    echo "🎉 MoviePilot 部署完成！"
    echo "========================================"
    echo
    echo "📱 Web 访问地址:"
    echo "  MoviePilot:    http://localhost:3000"
    echo "  Emby:          http://localhost:8096"
    echo "  qBittorrent:   http://localhost:8099"
    echo "  Transmission:  http://localhost:9091"
    echo "  字幕下载器:     http://localhost:19035"
    echo
    echo "🔑 默认登录信息:"
    echo "  MoviePilot:    用户名见 .env 文件"
    echo "  Transmission:  admin / 见 .env 文件"
    echo "  qBittorrent:   admin / adminadmin"
    echo
    echo "📁 重要目录:"
    echo "  媒体库:        $PROJECT_DIR/media/links/"
    echo "  下载目录:      $PROJECT_DIR/media/downloads/"
    echo "  配置文件:      $PROJECT_DIR/.env"
    echo
    echo "🔧 管理命令:"
    echo "  系统监控:      bash scripts/monitor.sh"
    echo "  系统优化:      bash scripts/optimize.sh"
    echo "  查看日志:      docker-compose logs -f"
    echo
    echo "📚 更多信息请查看 README.md 文件"
    echo "========================================"
}

# 主函数
main() {
    echo "MoviePilot 快速部署脚本"
    echo "维护者: alxxxxla"
    echo "========================================"
    
    cd "$PROJECT_DIR"
    
    case "${1:-deploy}" in
        "deploy")
            check_requirements
            create_directories
            setup_environment
            interactive_setup
            start_services
            if verify_deployment; then
                show_access_info
            else
                log_error "部署过程中出现问题，请检查日志"
                exit 1
            fi
            ;;
        "update")
            log_step "更新系统..."
            docker-compose pull
            docker-compose up -d
            verify_deployment
            ;;
        "reset")
            log_warn "这将删除所有配置和数据！"
            read -p "确认重置系统？[y/N]: " confirm
            if [[ $confirm =~ ^[Yy]$ ]]; then
                docker-compose down -v
                rm -rf */config/
                rm -f .env
                log_info "系统已重置"
            fi
            ;;
        *)
            echo "用法: $0 [deploy|update|reset]"
            echo "  deploy - 完整部署（默认）"
            echo "  update - 更新系统"
            echo "  reset  - 重置系统"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
