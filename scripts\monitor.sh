#!/bin/bash

# MoviePilot 系统监控脚本
# 作者: alxxxxla
# 用途: 监控系统状态和服务健康状况

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"
LOG_FILE="$PROJECT_DIR/monitor.log"

# 日志函数
log_with_timestamp() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
    log_with_timestamp "[INFO] $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
    log_with_timestamp "[WARN] $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
    log_with_timestamp "[ERROR] $1"
}

# 检查服务状态
check_service_status() {
    local service_name=$1
    local container_name=$2
    
    if docker ps --format "table {{.Names}}" | grep -q "^${container_name}$"; then
        log_info "${service_name} 运行正常"
        return 0
    else
        log_error "${service_name} 未运行"
        return 1
    fi
}

# 检查端口连通性
check_port() {
    local service_name=$1
    local port=$2
    local host=${3:-localhost}
    
    if nc -z "$host" "$port" 2>/dev/null; then
        log_info "${service_name} 端口 ${port} 可访问"
        return 0
    else
        log_error "${service_name} 端口 ${port} 不可访问"
        return 1
    fi
}

# 检查磁盘空间
check_disk_space() {
    local threshold=${1:-90}
    local usage=$(df "$PROJECT_DIR" | tail -1 | awk '{print $5}' | sed 's/%//')
    
    if [ "$usage" -gt "$threshold" ]; then
        log_error "磁盘空间不足: ${usage}% (阈值: ${threshold}%)"
        return 1
    elif [ "$usage" -gt 80 ]; then
        log_warn "磁盘空间紧张: ${usage}%"
        return 0
    else
        log_info "磁盘空间充足: ${usage}%"
        return 0
    fi
}

# 检查内存使用
check_memory_usage() {
    local threshold=${1:-90}
    
    # 获取系统内存使用率
    local mem_usage=$(free | grep Mem | awk '{printf "%.0f", $3/$2 * 100.0}')
    
    if [ "$mem_usage" -gt "$threshold" ]; then
        log_error "内存使用率过高: ${mem_usage}% (阈值: ${threshold}%)"
        return 1
    elif [ "$mem_usage" -gt 80 ]; then
        log_warn "内存使用率较高: ${mem_usage}%"
        return 0
    else
        log_info "内存使用率正常: ${mem_usage}%"
        return 0
    fi
}

# 检查容器资源使用
check_container_resources() {
    log_info "检查容器资源使用情况..."
    
    # 获取容器统计信息
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.MemPerc}}" | while read line; do
        if [[ "$line" != *"CONTAINER"* ]]; then
            echo "$line" | tee -a "$LOG_FILE"
        fi
    done
}

# 检查网络连接
check_network_connectivity() {
    log_info "检查网络连接..."
    
    # 检查外网连接
    if ping -c 1 8.8.8.8 > /dev/null 2>&1; then
        log_info "外网连接正常"
    else
        log_error "外网连接异常"
    fi
    
    # 检查 TMDB 连接
    if curl -s --connect-timeout 5 "https://api.themoviedb.org" > /dev/null; then
        log_info "TMDB API 连接正常"
    else
        log_warn "TMDB API 连接异常"
    fi
}

# 生成健康报告
generate_health_report() {
    local report_file="$PROJECT_DIR/health_report_$(date +%Y%m%d_%H%M%S).txt"
    
    {
        echo "MoviePilot 系统健康报告"
        echo "生成时间: $(date)"
        echo "维护者: alxxxxla"
        echo "========================================"
        echo
        
        echo "服务状态:"
        docker-compose ps
        echo
        
        echo "磁盘使用情况:"
        df -h "$PROJECT_DIR"
        echo
        
        echo "内存使用情况:"
        free -h
        echo
        
        echo "容器资源使用:"
        docker stats --no-stream
        echo
        
        echo "最近的错误日志:"
        tail -20 "$LOG_FILE" | grep -i error || echo "无错误日志"
        
    } > "$report_file"
    
    log_info "健康报告已生成: $report_file"
}

# 自动修复常见问题
auto_fix_issues() {
    log_info "尝试自动修复常见问题..."
    
    # 重启异常的容器
    local failed_containers=$(docker ps -a --filter "status=exited" --format "{{.Names}}" | grep -E "(moviepilot|emby|qbittorrent|transmission|chinesesubfinder)")
    
    if [ -n "$failed_containers" ]; then
        log_warn "发现异常容器，尝试重启: $failed_containers"
        echo "$failed_containers" | xargs -r docker restart
    fi
    
    # 清理过期的日志文件
    find "$PROJECT_DIR"/*/config/logs -name "*.log" -mtime +7 -delete 2>/dev/null || true
    
    log_info "自动修复完成"
}

# 主监控函数
main_monitor() {
    log_info "开始系统监控检查..."
    
    local issues=0
    
    # 检查各个服务
    check_service_status "MoviePilot" "moviepilot-v2" || ((issues++))
    check_service_status "Emby" "emby" || ((issues++))
    check_service_status "qBittorrent" "qbittorrent" || ((issues++))
    check_service_status "Transmission" "transmission" || ((issues++))
    check_service_status "ChineseSubFinder" "chinesesubfinder" || ((issues++))
    check_service_status "Watchtower" "watchtower" || ((issues++))
    
    # 检查端口连通性
    check_port "MoviePilot Web" 3000 || ((issues++))
    check_port "MoviePilot API" 3001 || ((issues++))
    check_port "Emby" 8096 || ((issues++))
    check_port "qBittorrent" 8099 || ((issues++))
    check_port "Transmission" 9091 || ((issues++))
    
    # 检查系统资源
    check_disk_space 90 || ((issues++))
    check_memory_usage 90 || ((issues++))
    
    # 检查网络
    check_network_connectivity
    
    # 显示容器资源使用
    check_container_resources
    
    if [ "$issues" -eq 0 ]; then
        log_info "所有检查通过，系统运行正常"
    else
        log_warn "发现 $issues 个问题"
        
        # 如果启用了自动修复
        if [ "${AUTO_FIX:-false}" = "true" ]; then
            auto_fix_issues
        fi
    fi
    
    return $issues
}

# 主函数
main() {
    cd "$PROJECT_DIR"
    
    case "${1:-monitor}" in
        "monitor")
            main_monitor
            ;;
        "report")
            generate_health_report
            ;;
        "fix")
            auto_fix_issues
            ;;
        "continuous")
            log_info "启动连续监控模式 (每5分钟检查一次)..."
            while true; do
                main_monitor
                sleep 300
            done
            ;;
        *)
            echo "用法: $0 [monitor|report|fix|continuous]"
            echo "  monitor    - 执行一次监控检查（默认）"
            echo "  report     - 生成详细健康报告"
            echo "  fix        - 尝试自动修复问题"
            echo "  continuous - 连续监控模式"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
