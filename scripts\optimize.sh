#!/bin/bash

# MoviePilot 系统优化脚本
# 作者: alxxxxla
# 用途: 优化系统性能和清理无用文件

set -e

echo "🚀 开始 MoviePilot 系统优化..."

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 是否运行
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker 未运行，请先启动 Docker"
        exit 1
    fi
    log_info "Docker 状态检查通过"
}

# 清理 Docker 缓存
cleanup_docker() {
    log_info "清理 Docker 缓存..."
    
    # 清理未使用的镜像
    docker image prune -f
    
    # 清理未使用的容器
    docker container prune -f
    
    # 清理未使用的网络
    docker network prune -f
    
    # 清理未使用的卷
    docker volume prune -f
    
    log_info "Docker 缓存清理完成"
}

# 清理日志文件
cleanup_logs() {
    log_info "清理过期日志文件..."
    
    # 清理超过 7 天的日志文件
    find ./*/config/logs -name "*.log" -mtime +7 -delete 2>/dev/null || true
    find ./*/config/logs -name "*.txt" -mtime +7 -delete 2>/dev/null || true
    
    # 清理空的日志目录
    find ./*/config/logs -type d -empty -delete 2>/dev/null || true
    
    log_info "日志文件清理完成"
}

# 优化数据库
optimize_database() {
    log_info "优化数据库..."
    
    # 检查 MoviePilot 数据库
    if [ -f "./moviepilot/config/user.db" ]; then
        # 备份数据库
        cp "./moviepilot/config/user.db" "./moviepilot/config/user.db.backup.$(date +%Y%m%d_%H%M%S)"
        
        # 优化数据库（需要 sqlite3 工具）
        if command -v sqlite3 > /dev/null; then
            sqlite3 "./moviepilot/config/user.db" "VACUUM;"
            sqlite3 "./moviepilot/config/user.db" "REINDEX;"
            log_info "数据库优化完成"
        else
            log_warn "未找到 sqlite3 工具，跳过数据库优化"
        fi
    fi
}

# 检查磁盘空间
check_disk_space() {
    log_info "检查磁盘空间..."
    
    # 获取当前目录磁盘使用情况
    DISK_USAGE=$(df . | tail -1 | awk '{print $5}' | sed 's/%//')
    
    if [ "$DISK_USAGE" -gt 90 ]; then
        log_error "磁盘空间不足！当前使用率: ${DISK_USAGE}%"
        log_warn "建议清理下载目录中的旧文件"
    elif [ "$DISK_USAGE" -gt 80 ]; then
        log_warn "磁盘空间紧张，当前使用率: ${DISK_USAGE}%"
    else
        log_info "磁盘空间充足，当前使用率: ${DISK_USAGE}%"
    fi
}

# 更新容器镜像
update_images() {
    log_info "更新容器镜像..."
    
    # 拉取最新镜像
    docker-compose pull
    
    log_info "镜像更新完成"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    
    # 停止所有服务
    docker-compose down
    
    # 启动所有服务
    docker-compose up -d
    
    log_info "服务重启完成"
}

# 显示系统状态
show_status() {
    log_info "系统状态:"
    echo "----------------------------------------"
    docker-compose ps
    echo "----------------------------------------"
    log_info "磁盘使用情况:"
    df -h .
    echo "----------------------------------------"
}

# 主函数
main() {
    echo "MoviePilot 系统优化工具"
    echo "维护者: alxxxxla"
    echo "========================================"
    
    check_docker
    
    case "${1:-all}" in
        "docker")
            cleanup_docker
            ;;
        "logs")
            cleanup_logs
            ;;
        "db")
            optimize_database
            ;;
        "update")
            update_images
            ;;
        "restart")
            restart_services
            ;;
        "status")
            show_status
            ;;
        "all")
            cleanup_docker
            cleanup_logs
            optimize_database
            check_disk_space
            show_status
            ;;
        *)
            echo "用法: $0 [docker|logs|db|update|restart|status|all]"
            echo "  docker  - 清理 Docker 缓存"
            echo "  logs    - 清理过期日志"
            echo "  db      - 优化数据库"
            echo "  update  - 更新镜像"
            echo "  restart - 重启服务"
            echo "  status  - 显示状态"
            echo "  all     - 执行所有优化操作（默认）"
            exit 1
            ;;
    esac
    
    log_info "优化完成！"
}

# 执行主函数
main "$@"
