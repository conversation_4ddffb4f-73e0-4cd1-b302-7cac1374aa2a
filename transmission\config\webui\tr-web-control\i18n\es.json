{"name": "es", "author": "<PERSON><PERSON><PERSON>, ma<PERSON><PERSON>, vodek3", "system": {"title": "Transmission WEB Control", "status": {"connect": "Conectando...", "connected": "Conectado", "queue": "Cola:", "queuefinish": "Cola(s) finalizada(s).", "notfinal": "No final", "checked": "%n data checked:"}}, "error": {"data-error": "<PERSON><PERSON><PERSON> de da<PERSON>.", "data-post-error": "Error en<PERSON>do <PERSON>.", "rename-error": "<PERSON>rror reno<PERSON><PERSON>o"}, "config": {"save-path": "Carpeta descargas"}, "toolbar": {"start": "Iniciar", "pause": "Pausar", "recheck": "Comprobar", "start-all": "In<PERSON>ar to<PERSON>", "pause-all": "<PERSON><PERSON><PERSON> todos", "remove": "Eliminar", "remove-all": "Eliminar todos", "remove-data": "Eli<PERSON>r datos", "add-torrent": "<PERSON><PERSON><PERSON> torrent", "attribute": "Atributo", "alt-speed": "Velocidad alternativa", "system-config": "Configuración del servidor", "system-reload": "Actualizar", "about": "Acerca", "reload-time": "Actualizar cada:", "reload-time-unit": "segundos", "autoreload-disabled": "Desactivado", "autoreload-enabled": "Activado", "search-prompt": "Búsqueda local", "tracker-replace": "Reemplazar trackers", "queue": "Cola", "ui-mobile": "IU Móvil", "ui-original": "IU Original", "ui-computer": "IU Escritorio", "plugin": "Extensiones/plugins", "rename": "Renombrar", "copy-path-to-clipboard": "Copy download location to clipboard", "tip": {"start": "Iniciar torrents seleccionados", "pause": "Pausar torrents seleccionados", "recheck": "Comprobar torrents seleccionados", "recheck-confirm": "¿Seguro que deseas comprobar los torrents seleccionado? ¡Podría tardar bastante tiempo!", "start-all": "Iniciar todos los torrents", "pause-all": "Pausar todos los torrents", "remove": "Eliminar torrents seleccionados", "delete-all": "Eliminar todos", "delete-data": "Eli<PERSON>r datos", "add-torrent": "<PERSON><PERSON><PERSON> torrent(s)", "attribute": "Atributo", "alt-speed": "Velocidad alternativa", "system-config": "Configuración del servidor", "system-reload": "Recargar", "about": "Acerca de", "autoreload-disabled": "Desactivar actualización automática", "autoreload-enabled": "Activar actualización automática", "tracker-replace": "Reemplazar trackers", "change-download-dir": "Establecer ubicación", "ui-mobile": "IU Móviles", "ui-original": "IU Original", "more-peers": "Solicitar más peers al trackers", "rename": "Renombrando ruta del Torrent", "copy-path-to-clipboard": "Copy download location to clipboard"}}, "menus": {"queue": {"move-top": "Mover al inicio", "move-up": "Mover a<PERSON>ba", "move-down": "Mover abajo", "move-bottom": "Move al final"}, "plugin": {"auto-match-data-folder": "Elegir automáticamente directorio de datos"}, "setLabels": "Set User Labels", "copyMagnetLink": "Copy magnetLink to clipboard"}, "title": {"left": "Navegación", "list": "Torrents", "attribute": "Parámetros del torrent", "status": "Estado"}, "tree": {"all": "Todos", "active": "Activos", "paused": "Pausados", "downloading": "Descargando", "sending": "Subiendo", "error": "Errores", "warning": "Avisos", "actively": "Activos", "check": "Comprobando", "wait": "<PERSON><PERSON><PERSON><PERSON>", "search-result": "Búsqueda", "status": {"loading": "Cargando..."}, "statistics": {"title": "Estadísticas", "cumulative": "<PERSON><PERSON><PERSON><PERSON>", "current": "Sesión actual", "uploadedBytes": "Subido:", "downloadedBytes": "<PERSON><PERSON><PERSON>:", "filesAdded": "Archivos:", "sessionCount": "Sesiones:", "secondsActive": "Tiempo activo:"}, "servers": "Trackers", "folders": "Carpetas", "toolbar": {"nav": {"folders": "Carpetas"}}, "labels": "User Labels"}, "statusbar": {"downloadspeed": "Vel. descarga:", "uploadspeed": "Vel. subida:", "version": "Versión:"}, "dialog": {"torrent-add": {"download-dir": "Carpeta descarga:", "used-download-dir": "Carpeta de descarga usada:", "torrent-url": "URL(s):", "tip-torrent-url": "Consejo：<PERSON>uedes introducir más de un archivo usando varias líneas", "autostart": "Iniciar des<PERSON><PERSON>:", "set-default-download-dir": "Establecer como carpeta predeterminada", "upload-file": "Archivos(s) torrent:", "nosource": "Sin torrent o URL.", "tip-title": "Los archivos subidos tienen prioridad sobre las URL's"}, "system-config": {"title": "Configuración del servidor", "tabs": {"base": "Básica", "network": "Red", "limit": "Límites", "alt-speed": "Vel. alternativa", "dictionary-folders": "Diccionario de carpetas", "more": "Más", "labels": "Etiquetas"}, "config-dir": "Carpeta de configuración de Transmission:", "download-dir": "Carpeta de descarga por defecto:", "download-dir-free-space": "Espacio libre:", "incomplete-dir-enabled": "Usar carpeta 'En progreso'", "cache-size-mb": "Tamaño del cache en disco:", "rename-partial-files": "Añadir '.part' a los archivos en progreso", "start-added-torrents": "Inicio automático de torrents añadidos", "download-queue-enabled": "Cola de descarga activada, elementos máximos:", "seed-queue-enabled": "Cola de subida activada, elementos máximos:", "peer-port-random-on-start": "Usar puerto aleatorio al iniciar", "port-forwarding-enabled": "Forwarding activado", "test-port": "Comprobar", "port-is-open-true": "El puerto está abierto", "port-is-open-false": "El puerto está cerrado", "testing": "Probando...", "encryption": "Cifrado:", "encryption-type": {"required": "Requerido", "preferred": "Preferido", "tolerated": "<PERSON><PERSON><PERSON>"}, "utp-enabled": "µTP (UPnP) activado", "dht-enabled": "DHT activado", "lpd-enabled": "LPD activado", "pex-enabled": "PEX activado", "peer-limit-global": "Número máximo de peers global:", "peer-limit-per-torrent": "Número máximo de peers por torrent:", "speed-limit-down-enabled": "Velocidad máxima de descarga:", "speed-limit-up-enabled": "Velocidad máxima de subida:", "alt-speed-enabled": "Usar velocidad alternativa", "alt-speed-down": "Velocidad máxima de descarga:", "alt-speed-up": "Velocidad máxima de subida:", "alt-speed-time-enabled": "Usar programación", "alt-speed-time": "Hora：", "weekday": {"1": "<PERSON><PERSON>", "2": "<PERSON><PERSON>", "3": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "4": "<PERSON><PERSON>", "5": "Viernes", "6": "Sábado", "0": "Domingo"}, "blocklist-enabled": "Usar blocklist", "blocklist-size": "Blocklist tiene %n reglas.", "seedRatioLimited": "Ratio de compartición máx. por torrent:", "queue-stalled-enabled": "Considerar o no los torrents inactivos como parados:", "idle-seeding-limit-enabled": "Los torrents compartiendo se detendrán si se encuentran inactivos más de:", "minutes": "<PERSON><PERSON><PERSON>", "nochange": "Sin cambio", "saving": "Guardando...", "show-bt-servers": "Mostrar 'servidores BT' en Trackers:", "restore-default-settings": "Restaurar configuración UI", "language": "Idioma:", "loading": "Cargando...", "hide-subfolders": "Al hace click en el directorio de datos, ocultará la subcarpetas de la lista:", "simple-check-mode": "Se marca un solo torrent cuando se hace click derecho en la lista:", "nav-contents": "Mostrar barra de navegación de contenido:", "labels-manage": {"name": "Nombre de etiqueta", "description": "Descripción", "color": "Color", "actions": "Acciones", "import-confirm": "¿Quieres importar las etiquetas? Esto cambiará la configuración actual."}, "import-config": "Importar fichero de configuración", "export-config": "Exportar configuración actual", "import-config-confirm": "¿Quieres importar estas configuraciones? Esto cambiará la configuración actual.", "script-torrent-done-enabled": "Ejecutar el siguiente script al completar una descarga:", "ipinfo": "ipinfo.io Token"}, "public": {"button-ok": "Aceptar", "button-cancel": "<PERSON><PERSON><PERSON>", "button-reload": "Actualizar", "button-save": "Guardar", "button-close": "<PERSON><PERSON><PERSON>", "button-update": "Actualizar", "button-config": "Configurar", "button-addnew": "<PERSON><PERSON><PERSON>", "button-edit": "<PERSON><PERSON>", "button-delete": "Bo<PERSON>r", "button-export": "Exportar", "button-import": "Importar"}, "about": {"infos": "Autor：culturist<br/>Declaración：Most of the icons used in this program from the network, if any violation of your rights, please contact me delete.", "check-update": "Comprobar actualización", "home": "Project Home", "help": "Wiki", "donate": "Donate", "pt-plugin": "PT Plugin"}, "torrent-remove": {"title": "Confirmar eliminar", "confirm-text": "¿Desea eliminar los torrents seleccionados?", "remove-data": "Borrar datos locales", "remove-error": "¡Borrado fallido!"}, "torrent-changeDownloadDir": {"title": "<PERSON><PERSON><PERSON> n<PERSON>", "old-download-dir": "Carpeta antigua:", "new-download-dir": "Carpeta nueva:", "move-data": "Si se activa se moverán desde la ubicación anterior, en caso contrario busca archivos en 'Carpeta nueva'.", "set-error": "¡Error al establecer la carpeta!", "recheck-data": "Recomprobar datos."}, "system-replaceTracker": {"title": "Reemplazar Trackers", "old-tracker": "Antiguo tracker：", "new-tracker": "Nuevo tracker：", "tip": "Esta función buscará trackers en <b>todos los torrents</b>.", "not-found": "Tracker no encontrado."}, "auto-match-data-folder": {"title": "Elegir automáticamente directorio de datos", "torrent-count": "Recuento de Torrent:", "folder-count": "Recuento de carpeta:", "dictionary": "Diccionario de Carpetas", "time-begin": "Hora de inicio:", "time-now": "Ahora:", "status": "Estado:", "ignore": "<PERSON><PERSON><PERSON>", "working-close-confirm": "<PERSON><PERSON><PERSON> trabajando, ¿está seguro de cer<PERSON>lo?", "time-interval": "Intervalo de tiempo (segundos):", "work-mode-title": "Modo:", "work-mode": {"1": "Elegido individualmente por torrent", "2": "Elegido individualmente por folder"}}, "torrent-rename": {"title": "Renombrando ruta <PERSON>", "oldname": "Antigua", "newname": "Nueva"}, "torrent-attribute-add-tracker": {"title": "<PERSON><PERSON><PERSON>", "tip": "<PERSON> lina, <PERSON> Tracker"}, "torrent-setLabels": {"title": "Etiquetas de Usuario", "available": "Disponible:", "selected": "Seleccionada:"}, "export-config": {"title": "Selecciona los datos a exportar", "option-all": "Toda la configuración", "option-system": "Configuración de Web Control", "option-dictionary": "Diccionario de carpetas definidas", "option-server": "Configuración de Transmission (Directorio de descarga, cache, limites de velocidad, etc.)"}, "import-config": {"title": "Selecciona los datos a importar", "invalid-file": "Perfil no válido"}}, "torrent": {"fields": {"id": "#", "name": "Nombre", "hashString": "HASH", "downloadDir": "Carpeta desc.", "totalSize": "<PERSON><PERSON><PERSON>", "status": "Estado", "percentDone": "Progreso", "remainingTime": "Tiempo restante", "addedDate": "<PERSON><PERSON><PERSON><PERSON>", "completeSize": "<PERSON><PERSON><PERSON>", "rateDownload": "Veloc. desc.", "rateUpload": "Veloc. sub.", "leecherCount": "Leechers", "seederCount": "Seeders", "uploadedEver": "Subido", "uploadRatio": "Proporción", "queuePosition": "Cola", "activityDate": "Fecha de Actividad", "trackers": "Trackers", "labels": "User Labels", "doneDate": "Completed Date"}, "status-text": {"0": "<PERSON><PERSON><PERSON>", "1": "Espera comprob.", "2": "Comprobando", "3": "<PERSON>sper<PERSON> descarga", "4": "Descargando", "5": "Espera subida", "6": "Subiendo"}, "attribute": {"tabs": {"base": "Básico", "servers": "Trackers", "files": "Archivos", "users": "Peers", "config": "Config."}, "files-fields": {"name": "Nombre", "length": "<PERSON><PERSON><PERSON>", "percentDone": "Progreso", "bytesCompleted": "Completado", "wanted": "<PERSON><PERSON><PERSON>", "priority": "Prioridad"}, "servers-fields": {"announce": "<PERSON><PERSON><PERSON>", "announceState": "Estado", "lastAnnounceResult": "<PERSON><PERSON><PERSON><PERSON>", "lastAnnounceSucceeded": "Exitoso", "lastAnnounceTime": "<PERSON><PERSON>", "lastAnnounceTimedOut": "<PERSON><PERSON> ago<PERSON>o", "downloadCount": "Descargas", "nextAnnounceTime": "Próxi<PERSON> an<PERSON>cio", "leecherCount": "<PERSON><PERSON><PERSON>", "seederCount": "<PERSON><PERSON><PERSON>", "announceStateText": {"0": "Inactivo", "1": "<PERSON><PERSON><PERSON><PERSON>", "2": "En cola", "3": "Activo"}}, "peers-fields": {"address": "Dirección IP", "port": "Puerto", "isUTP": "UTP activado", "clientName": "Cliente", "flagStr": "Bandera", "progress": "Progreso", "rateToClient": "<PERSON><PERSON> a cliente", "rateToPeer": "<PERSON><PERSON> a peer"}, "status": {"true": "Sí", "false": "No"}, "priority": {"0": "Normal", "1": "Alta", "-1": "Baja"}, "filter-template-text": {"1": "Todo", "2": "fichero de relleno Bit<PERSON>omet", "3": "Archivo innecesario"}, "label": {"name": "Nombre:", "addedDate": "Añadido:", "totalSize": "Tamaño:", "completeSize": "Completado:", "leftUntilDone": "Restante:", "hashString": "HASH:", "downloadDir": "<PERSON><PERSON>:", "status": "Estado:", "rateDownload": "Vel. descarga:", "rateUpload": "Vel. subida:", "leecherCount": "Leechers:", "seederCount": "Seeders:", "uploadedEver": "Subido:", "uploadRatio": "Ratio subida:", "creator": "Creador:", "dateCreated": "Fecha creación:", "comment": "Comentario:", "errorString": "Desc. error:", "downloadLimited": "Vel. máxi<PERSON> descar<PERSON>：", "uploadLimited": "Vel. subida máxima：", "peer-limit": "Número máximo de peers por torrent：", "seedRatioMode": "Ratio de subida por torrent：", "seedIdleMode": "Detener compartidos inactivos tras：", "doneDate": "Finish Time:", "seedTime": "Seed Time:"}, "tip": {"button-allow": "Descargar archivos(s) marcados", "button-deny": "Omitir archivo(s) marcados", "button-priority": "Establecer prioridad", "button-filter": "Buscar archivos con una expresion regular", "button-tracker-add": "<PERSON><PERSON><PERSON> nue<PERSON> Tracker", "button-tracker-edit": "<PERSON><PERSON>", "button-tracker-remove": "Eliminar Tracker"}, "other": {"tracker-remove-confim": "¿Está seguro de que desea eliminar este tracker?"}}}, "torrent-head": {"buttons": {"autoExpandAttribute": "Auto expandir atributos"}}, "public": {"text-unknown": "Desconocido", "text-drop-title": "Arrastre y suelte el archivo en la región para añadir a Transmission.", "text-saved": "Guardado", "text-nochange": "No guardado", "text-info": "Información", "text-confirm": "¿E<PERSON>á seguro?", "text-browsers-not-support-features": "El navegador actual no soporta esta función!", "text-download-update": "Descargar esta actualización", "text-have-update": "Hay una actualización disponible", "text-on": "ON", "text-off": "OFF", "text-how-to-update": "How to update?", "text-ignore-this-version": "Ignore this version", "text-json-file-parsing-failed": "JSON file parsing failed!"}}