{"name": "pl", "author": "<PERSON>, <PERSON><PERSON><PERSON>, vodek<PERSON>, <PERSON> aka <PERSON><PERSON>", "system": {"title": "Transmission WEB Control", "status": {"connect": "Łączenie...", "connected": "Połącz<PERSON>", "queue": "Kolejka:", "queuefinish": "Zakończono kolejkę.", "notfinal": "<PERSON><PERSON>", "checked": "Zaznaczone pozycje: %n"}}, "error": {"data-error": "Błąd pobierania danych!", "data-post-error": "Błąd wysyłania danych!", "rename-error": "Błąd zmiany nazwy!"}, "config": {"save-path": "Katalog pobierania"}, "toolbar": {"start": "Start", "pause": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "recheck": "Sprawdź", "start-all": "Pobieraj wszystkie", "pause-all": "Wstrzymaj wszystkie", "remove": "Usuń", "remove-all": "Usuń wszystkie", "remove-data": "<PERSON><PERSON><PERSON> dane", "add-torrent": "<PERSON><PERSON><PERSON> to<PERSON>", "attribute": "Szczegóły", "alt-speed": "Alternatywna prędkość", "system-config": "Panel konfiguracyjny Transmission", "system-reload": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "about": "O webgui", "reload-time": "Auto odświeżanie:", "reload-time-unit": "sekund", "autoreload-disabled": "Wyłączone", "autoreload-enabled": "Włą<PERSON><PERSON>", "search-prompt": "S<PERSON>j w torrentach", "tracker-replace": "Zamień trackery", "queue": "<PERSON><PERSON><PERSON><PERSON>", "ui-mobile": "UI Mobilne", "ui-original": "UI Oryginalne", "ui-computer": "UI Desktopowe", "plugin": "Rozszerzenia/wtyczki", "rename": "Zmień nazwę", "copy-path-to-clipboard": "Skopiuj katalog pobierania do schowka", "tip": {"start": "<PERSON><PERSON><PERSON> wybrane torrenty", "pause": "Wstrzymaj wybrane torrenty", "recheck": "Sprawdź ponownie wybrane torrenty", "recheck-confirm": "<PERSON><PERSON><PERSON> p<PERSON>, że chcesz ponownie sprawdzić wybrane torrenty? To może zająć trochę czasu!", "start-all": "Pobieraj wszystkie", "pause-all": "Wstrzymaj wszystkie", "remove": "Usuń", "delete-all": "Usuń wszystkie", "delete-data": "<PERSON><PERSON><PERSON> dane", "add-torrent": "<PERSON><PERSON><PERSON> to<PERSON>", "attribute": "Szczegóły", "alt-speed": "Limituj prędkość pobierania i wysyłania", "system-config": "Panel konfiguracyjny Transmission", "system-reload": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "about": "O ap<PERSON>ji", "autoreload-disabled": "Wyłącz auto-odświeżanie", "autoreload-enabled": "Włącz auto-odświeżanie", "tracker-replace": "Zamień trackery", "change-download-dir": "Zmień katalog pobierania", "ui-mobile": "Mobilne UI", "ui-original": "Oryginalne UI", "more-peers": "Zapytaj tracker o więcej peerów", "rename": "Zmiana nazwy katalogu", "copy-path-to-clipboard": "Kopiuj katalog pobierania do schowka"}}, "menus": {"queue": {"move-top": "Przesuń na górę", "move-up": "Przesuń wyżej", "move-down": "Przesuń niżej", "move-bottom": "Przesuń na dół"}, "plugin": {"auto-match-data-folder": "Automatyczne dopasowanie katalogu"}, "setLabels": "Ustaw własne etykiety", "copyMagnetLink": "Skopiuj magnetLink do schowka"}, "title": {"left": "<PERSON><PERSON><PERSON><PERSON>", "list": "Torrenty", "attribute": "Szczegóły", "status": "Status"}, "tree": {"all": "Wszystkie torrenty", "active": "Aktywne", "paused": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "downloading": "<PERSON><PERSON><PERSON>", "sending": "W<PERSON><PERSON>łane", "error": "Błędy", "warning": "Ostrzeżenia", "actively": "Aktywne", "check": "Sprawdzane", "wait": "Oczekiwanie", "search-result": "Wyniki wyszukiwania", "status": {"loading": "Ładowanie..."}, "statistics": {"title": "Statystyki", "cumulative": "Ogólne", "current": "Bieżące", "uploadedBytes": "Wysłano:", "downloadedBytes": "Pobrano:", "filesAdded": "Dodane pliki:", "sessionCount": "<PERSON><PERSON><PERSON><PERSON> sesji:", "secondsActive": "<PERSON><PERSON> aktywności:"}, "servers": "Trackery", "folders": "Katalogi", "toolbar": {"nav": {"folders": "Katalogi"}}, "labels": "<PERSON><PERSON><PERSON><PERSON> etykiety"}, "statusbar": {"downloadspeed": "Pr<PERSON><PERSON><PERSON>ść pobierania:", "uploadspeed": "Pręd<PERSON>ść wysyłania:", "version": "Wersja:"}, "dialog": {"torrent-add": {"download-dir": "Katalog pobierania:", "used-download-dir": "Używany katalog pobierania:", "torrent-url": "Link do torrenta:", "tip-torrent-url": "Wskazówka: każdy w nowej linii (oddziel klawiszem ENTER)", "autostart": "Uruchom pobieranie:", "tip-autostart": "", "set-default-download-dir": "Zapisz jako katalog domyślny", "upload-file": "Pliki torrent:", "nosource": "Brak pliku torrent bądź linka.", "tip-title": "Dodaj plik torrent i rozpocznij pobieranie"}, "system-config": {"title": "Konfiguracja ser<PERSON>a", "tabs": {"base": "Ogólne", "network": "<PERSON><PERSON><PERSON>", "limit": "Limitowanie prędkości", "alt-speed": "Harmonogram", "dictionary-folders": "Słownik folderów", "more": "<PERSON><PERSON><PERSON><PERSON>j", "labels": "Etykiety"}, "config-dir": "Lokalizacja katalogu z konfiguracją Transmission:", "download-dir": "Domyślny katalog pobierania torrentów:", "download-dir-free-space": "Wolne miejsce na dysku:", "incomplete-dir-enabled": "Użyj katalogu dla niedokończonych pobrań", "cache-size-mb": "Wielkość cache'u dysku:", "rename-partial-files": "Dodaj rozszerzenie '.part' do nieukończonych pobrań", "start-added-torrents": "Automatycznie rozpoczynaj pobieranie dodawanych torrentów", "download-queue-enabled": "Włącz kolejkę pobierania, maks<PERSON>alna ilość kolejek:", "seed-queue-enabled": "Włącz kolejkę wysyłania, maks<PERSON>alna ilość kolejek:", "peer-port-random-on-start": "Użyj losowego portu podczas uruchamiania", "port-forwarding-enabled": "Włącz przekierowywanie portów", "test-port": "Testuj port", "port-is-open-true": "Port jest otwarty", "port-is-open-false": "Port jest zamknięty", "testing": "Testowanie...", "encryption": "Szyfrowanie:", "encryption-type": {"required": "<PERSON><PERSON><PERSON><PERSON>", "preferred": "Preferowane", "tolerated": "Tolerowane"}, "utp-enabled": "Włącz µTP (UPnP)", "dht-enabled": "Włącz DHT", "lpd-enabled": "Włącz LPD", "pex-enabled": "Włącz PEX", "peer-limit-global": "Maksymalna ogólna ilość peerów:", "peer-limit-per-torrent": "Maksymalna ilość peerów na torrent:", "speed-limit-down-enabled": "Maksymalna prędkość pobierania:", "speed-limit-up-enabled": "Maksymalna prędkość wysyłania:", "alt-speed-enabled": "Używaj ograniczenia prędkości", "alt-speed-down": "Maksymalna prędkość pobierania:", "alt-speed-up": "Maksymalna prędkość wysyłania:", "alt-speed-time-enabled": "Użyj harmonogramu", "alt-speed-time": "Godziny：", "weekday": {"1": "Ponied<PERSON>łek", "2": "<PERSON><PERSON><PERSON>", "3": "Środa", "4": "<PERSON><PERSON><PERSON><PERSON>", "5": "Piątek", "6": "<PERSON><PERSON><PERSON>", "0": "<PERSON><PERSON><PERSON><PERSON>"}, "blocklist-enabled": "Używaj blocklist'y (czarna lista z adresami IP organizacji antypirackich)", "blocklist-size": "Na czarnej liście jest %n reguł", "seedRatioLimited": "Domyślne proporcje dla torrentów:", "queue-stalled-enabled": "Bezczynne torrenty będą blokowane po czasie:", "idle-seeding-limit-enabled": "Wysyłanie pobranych torrentów zostanie zatrzymane, jeż<PERSON> nikt nie będzie ich pobierał przez:", "minutes": "Minut", "nochange": "<PERSON><PERSON> <PERSON>", "saving": "Zapisywanie...", "show-bt-servers": "Pokazuj serwery BT na liście Trackerów:", "restore-default-settings": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ustawienia domyślne", "language": "Język:", "loading": "Ładowanie...", "hide-subfolders": "Ukrywaj zawartość folderów nadrzędnych podczas klikania w katalogu z danymi:", "simple-check-mode": "Podczas prawo-kliku na liście torrentów zaznaczaj tylko jedną pozycję:", "nav-contents": "Wyświetlane elementy pasków nawigacji:", "labels-manage": {"name": "Nazwa <PERSON>yk<PERSON>y", "description": "Opis", "color": "<PERSON><PERSON>", "actions": "<PERSON><PERSON><PERSON><PERSON>", "import-confirm": "Czy na pewno chcesz zaimportować etykiety? Spowoduje to nadpisanie istniejącej konfiguracji."}, "import-config": "Import konfiguracji z pliku", "export-config": "Eksport aktualnej konfiguracji", "import-config-confirm": "Czy na pewno chcesz importować nowe ustawienia? Spowoduje to nieodwracalne nadpisanie aktualnej konfiguracji.", "script-torrent-done-enabled": "Gdy pobieranie torrenta zostanie ukończone uruchamiaj następujący skrypt:", "ipinfo": "Token ipinfo.io"}, "public": {"button-ok": "OK", "button-cancel": "<PERSON><PERSON><PERSON>", "button-reload": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "button-save": "<PERSON><PERSON><PERSON><PERSON>", "button-close": "Zamknij", "button-update": "Aktualizuj", "button-config": "Konfiguracja", "button-addnew": "<PERSON><PERSON><PERSON>", "button-edit": "<PERSON><PERSON><PERSON><PERSON>", "button-delete": "Usuń", "button-export": "Eksportuj", "button-import": "Import<PERSON>j"}, "about": {"infos": "Autor: culturist, <PERSON><PERSON><PERSON><PERSON><PERSON>e: <PERSON><PERSON><PERSON><PERSON><br/>Poprawki + nowe tłumaczenia: v<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><br/>Oświadczenie: <PERSON><PERSON><PERSON> któraś ikona narusza twoje prawa, skontaktuj się z autorem.", "check-update": "Sprawdź aktualizację", "home": "Strona domowa", "help": "Wiki", "donate": "<PERSON><PERSON><PERSON>", "pt-plugin": "Plugin PT"}, "torrent-remove": {"title": "Potwierdź", "confirm-text": "<PERSON>zy na pewno chcesz usunąć wybrane torrenty?", "remove-data": "<PERSON><PERSON><PERSON> lokalne dane", "remove-error": "Usuwanie nie powiodło się!"}, "torrent-changeDownloadDir": {"title": "Ustaw nowy katalog pobierania dla tego torrenta", "old-download-dir": "Stary katalog:", "new-download-dir": "<PERSON><PERSON> katalog:", "move-data": "Przenieś pobrane pliki do nowej lokalizacji", "set-error": "Błąd!", "recheck-data": "Ponownie sprawdź pobrane dane"}, "system-replaceTracker": {"title": "Zmiana trackerów", "old-tracker": "Stary tracker:", "new-tracker": "Nowy tracker:", "tip": "Ta funkcja znajdzie i zamieni trackery we <b>wszyst<PERSON>ch torrentach!</b>", "not-found": "Tracker nie został znaleziony."}, "auto-match-data-folder": {"title": "Automatyczne dopasowanie katalogu z danymi", "torrent-count": "Liczba torrentów:", "folder-count": "Liczba folderów:", "dictionary": "Plik słownika", "time-begin": "<PERSON>zas rozpoczęcia:", "time-now": "Teraz:", "status": "Stan:", "ignore": "Ignoruj", "working-close-confirm": "Operacja pobierania w trakcie, czy na pewno chcesz ją zatrzymać?", "time-interval": "Odstę<PERSON> (sekundy):", "work-mode-title": "Tryb:", "work-mode": {"1": "Dopasowa<PERSON> według torrenta", "2": "Dopasowa<PERSON> według folder<PERSON>"}}, "torrent-rename": {"title": "Zmiana nazwy katalogu", "oldname": "Stary:", "newname": "Nowy:"}, "torrent-attribute-add-tracker": {"title": "Dodaj trackery", "tip": "<PERSON><PERSON> l<PERSON> - j<PERSON><PERSON>"}, "torrent-setLabels": {"title": "Ustaw własne etykiety", "available": "Dostępne:", "selected": "Wybrane:"}, "export-config": {"title": "Proszę wybrać elementy przeznaczone do wyeksportowania", "option-all": "Pełna konfiguracja", "option-system": "Konfiguracja aplikacji TR Web Control", "option-dictionary": "Zdefiniowane słowniki folderów", "option-server": "Konfiguracja Transmission (Katalog pobierania, cache, limity pr<PERSON>, itd.)"}, "import-config": {"title": "Proszę wybrać elementy przeznaczone do zaimportowania", "invalid-file": "Wskazano niewłaściwy plik ustawień"}}, "torrent": {"fields": {"id": "#", "name": "Nazwa", "hashString": "HASH", "downloadDir": "Katalog pobierania", "totalSize": "Rozmiar", "status": "Status", "percentDone": "Ukończono", "remainingTime": "Pozostały czas", "addedDate": "Data dodania", "completeSize": "Pobrano", "rateDownload": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ć pobierania", "rateUpload": "P<PERSON><PERSON><PERSON><PERSON><PERSON>ć wysyłania", "leecherCount": "Peerów", "seederCount": "Seedów", "uploadedEver": "Wysłano", "uploadRatio": "Propor<PERSON>je", "queuePosition": "<PERSON><PERSON><PERSON><PERSON>", "activityDate": "Ostatnia aktywność", "trackers": "Trackery", "labels": "<PERSON><PERSON><PERSON><PERSON> etykiety", "doneDate": "Pobieranie <PERSON>ńczono"}, "status-text": {"0": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "1": "Oczekiwanie na sprawdzenie", "2": "Sprawdzanie", "3": "Oczekiwanie na pobieranie", "4": "Pobieranie", "5": "Oczekiwanie na seeda", "6": "Wysyłanie"}, "attribute": {"tabs": {"base": "Ogólne", "servers": "Trackery", "files": "Pliki", "users": "<PERSON><PERSON><PERSON>", "config": "Konfiguracja"}, "files-fields": {"name": "Nazwa", "length": "Rozmiar", "percentDone": "Ukończono", "bytesCompleted": "Pobrano", "wanted": "<PERSON><PERSON><PERSON><PERSON>?", "priority": "Priorytet"}, "servers-fields": {"announce": "Tracker", "announceState": "Status", "lastAnnounceResult": "Status połączenia z trackerem", "lastAnnounceSucceeded": "Udane połączenie", "lastAnnounceTime": "Godzina połączenia", "lastAnnounceTimedOut": "Timeout", "downloadCount": "Ilość pobrań", "nextAnnounceTime": "Następne połączenie", "leecherCount": "Liczba pobierających", "seederCount": "Liczba <PERSON>", "announceStateText": {"0": "Nieaktywny", "1": "Ocz<PERSON><PERSON><PERSON><PERSON><PERSON>", "2": "W kolejce", "3": "Aktywny"}}, "peers-fields": {"address": "Adres IP", "port": "Port", "isUTP": "Włączone UTP", "clientName": "Klient", "flagStr": "Flag<PERSON>", "progress": "%", "rateToClient": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ć pobierania", "rateToPeer": "P<PERSON><PERSON><PERSON><PERSON><PERSON>ć wysyłania"}, "status": {"true": "Tak", "false": " - "}, "priority": {"0": "Normalny", "1": "<PERSON><PERSON><PERSON>", "-1": "<PERSON><PERSON>"}, "filter-template-text": {"1": "Wszystkie", "2": "Plik 'padding' BitComet", "3": "Niepotrzebne pliki"}, "label": {"name": "Nazwa:", "addedDate": "Data dodania:", "totalSize": "Rozmiar:", "completeSize": "Pobrano:", "leftUntilDone": "Pozostało do pobrania:", "hashString": "HASH:", "downloadDir": "Katalog pobierania:", "status": "Status:", "rateDownload": "Pr<PERSON><PERSON><PERSON>ść pobierania:", "rateUpload": "Pręd<PERSON>ść wysyłania:", "leecherCount": "Il<PERSON>ść peerów:", "seederCount": "<PERSON><PERSON><PERSON><PERSON> seedów:", "uploadedEver": "Wysłano:", "uploadRatio": "Proporcje:", "creator": "Autor:", "dateCreated": "Data utworzenia:", "comment": "Komentarz:", "errorString": "Błąd:", "downloadLimited": "Maksymalna prędkość pobierania:", "uploadLimited": "Maksymalna prędkość wysyłania:", "peer-limit": "Maksymalna ilość peerów na torrent:", "seedRatioMode": "Proporcje do wyseedowania dla tego torrenta:", "seedIdleMode": "Wysyłanie torrenta zostanie zatrzymane, je<PERSON><PERSON> nie będzie wysyłany przez (minut):", "doneDate": "Czas zakończenia:", "seedTime": "<PERSON>zas 'wysiewu':"}, "tip": {"button-allow": "Pobierz zaznaczone pliki", "button-deny": "Pomiń zaznaczone pliki", "button-priority": "Ustaw priorytet", "button-filter": "Wyszukaj pliki według wyrażeń regularnych", "button-tracker-add": "<PERSON><PERSON><PERSON>y tracker", "button-tracker-edit": "<PERSON><PERSON><PERSON><PERSON> tracker", "button-tracker-remove": "<PERSON><PERSON><PERSON> tracker"}, "other": {"tracker-remove-confim": "<PERSON><PERSON> na pewno chcesz usunąć ten tracker?"}}}, "torrent-head": {"buttons": {"autoExpandAttribute": "Automatycznie rozwijaj szczegóły"}}, "public": {"text-unknown": "<PERSON><PERSON><PERSON><PERSON>", "text-drop-title": "Przerzuć z komputera plik o rozszerzeniu *.torrent metodą 'przeciągnij i upuść' (drag&drop) w podświetlony obszar jakbyś kopiował pliki pomiędzy katalogami na komputerze", "text-saved": "Zapisano", "text-nochange": "<PERSON><PERSON> <PERSON>", "text-info": "Informacje", "text-confirm": "<PERSON><PERSON><PERSON> pewny?", "text-browsers-not-support-features": "Ta przeglądarka nie obsługuje tej funkcji!", "text-download-update": "Pobierz aktualizację", "text-have-update": "Dostępna jest aktualizacja", "text-on": "WŁ", "text-off": "WYŁ", "text-how-to-update": "J<PERSON> wykonać aktualizację?", "text-ignore-this-version": "<PERSON><PERSON><PERSON><PERSON> tę wersję", "text-json-file-parsing-failed": "Parsowanie pliku JSON nie powiodło się!"}}