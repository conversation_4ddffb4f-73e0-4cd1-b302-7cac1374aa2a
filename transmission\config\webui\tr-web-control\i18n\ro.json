{"name": "ro", "author": "<PERSON><PERSON>, vodek3", "system": {"title": "Transmission WEB Control", "status": {"connect": "Conectare în curs...", "connected": "Conectat", "queue": "Coadă:", "queuefinish": "Lista este finalizată.", "notfinal": "Nefinalizat", "checked": "%n data checked:"}}, "error": {"data-error": "<PERSON><PERSON>re de date.", "data-post-error": "<PERSON><PERSON><PERSON> de postare date.", "rename-error": "Error renaming file/folder!"}, "config": {"save-path": "Director <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>"}, "toolbar": {"start": "Start", "pause": "Pauză", "recheck": "Reverifică", "start-all": "<PERSON><PERSON><PERSON><PERSON> toate", "pause-all": "<PERSON><PERSON><PERSON> pentru toate", "remove": "Şterge", "remove-all": "<PERSON><PERSON><PERSON> toate", "remove-data": "<PERSON><PERSON><PERSON> datele", "add-torrent": "<PERSON><PERSON><PERSON> fişier torrent", "attribute": "Atribute", "alt-speed": "Viteza alternativă", "system-config": "Configureaz<PERSON>", "system-reload": "Re<PERSON><PERSON><PERSON><PERSON><PERSON>", "about": "<PERSON><PERSON><PERSON>", "reload-time": "Reîncărcare automată:", "reload-time-unit": "secunde", "autoreload-disabled": "Activat", "autoreload-enabled": "Dezactivat", "search-prompt": "Caută torrent local", "tracker-replace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tracker", "queue": "Queue", "ui-mobile": "Mobile UI", "ui-original": "Original UI", "ui-computer": "Desktop UI", "plugin": "Extensions/plugins", "rename": "<PERSON><PERSON>", "copy-path-to-clipboard": "Copy download location to clipboard", "tip": {"start": "Porneşte sarcinile bifate", "pause": "Pauză pentru sarcinile bifate", "recheck": "Reverifică sarcinile bifate", "recheck-confirm": "Vrei să verifici sarcinile bifate ? Poate dura ceva timp ...", "start-all": "<PERSON><PERSON><PERSON><PERSON> toate", "pause-all": "<PERSON><PERSON><PERSON> toate", "remove": "Şterge", "delete-all": "<PERSON><PERSON><PERSON> toate", "delete-data": "<PERSON><PERSON><PERSON> datele", "add-torrent": "Adaugă fişiere torrent", "attribute": "Atribut", "alt-speed": "Viteza alternativă", "system-config": "Configureaz<PERSON>", "system-reload": "Re<PERSON><PERSON><PERSON><PERSON><PERSON>", "about": "Despre aplicaţie", "autoreload-disabled": "Dezactivează reîncarcarea automată", "autoreload-enabled": "Activează reîncarcarea automată", "tracker-replace": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> tracker", "change-download-dir": "Setează directorul de descărcare", "ui-mobile": "Interfaţă mobile", "ui-original": "Interfaţă originală", "more-peers": "Interoghează pentru mai multe legături", "rename": "Renaming a Torrent's Path", "copy-path-to-clipboard": "Copy download location to clipboard"}}, "menus": {"queue": {"move-top": "Move to top", "move-up": "Move up", "move-down": "Move down", "move-bottom": "Move to bottom"}, "plugin": {"auto-match-data-folder": "Automatically matches data directory"}, "setLabels": "Set User Labels", "copyMagnetLink": "Copy magnetLink to clipboard"}, "title": {"left": "Navigare", "list": "<PERSON>nte", "attribute": "Atribut", "status": "Status"}, "tree": {"all": "Toate", "active": "Active", "paused": "În pauză", "downloading": "În descărcare", "sending": "În transmitere", "error": "<PERSON><PERSON><PERSON>", "warning": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "actively": "Active", "check": "În verificare", "wait": "Aştept", "search-result": "Rezultate căutare", "status": {"loading": "Încărcare..."}, "statistics": {"title": "Statistici", "cumulative": "<PERSON><PERSON><PERSON><PERSON>", "current": "Actual", "uploadedBytes": "Total octeţi( upload ): ", "downloadedBytes": "Total octeţi( download ): ", "filesAdded": "Fişiere adăugate: ", "sessionCount": "Sesiuni: ", "secondsActive": "Activ de: "}, "servers": "<PERSON><PERSON>", "folders": "Directoare", "toolbar": {"nav": {"folders": "Directoare"}}, "labels": "User Labels"}, "statusbar": {"downloadspeed": "Viteza de descărcare:", "uploadspeed": "<PERSON><PERSON>a de încărcare:", "version": "Versiune:"}, "dialog": {"torrent-add": {"download-dir": "Directorul de descărcare:", "torrent-url": "Torrent URL:", "tip-torrent-url": "Sfat: Separate cu 'Enter'", "autostart": "Auto Start:", "set-default-download-dir": "Configurează ca director implicit", "upload-file": "Fişiere <PERSON>nt :", "nosource": "<PERSON><PERSON><PERSON> fi<PERSON>ier torrent sau URL.", "tip-title": "Încarcărea fişierului torrent file este prioritară comparativ cu torrent URL"}, "system-config": {"title": "Configurează Serverul", "tabs": {"base": "Principal", "network": "Reţea", "limit": "Limită", "alt-speed": "Programate", "dictionary-folders": "Folders Dictionary", "more": "More", "labels": "Labels"}, "config-dir": "Locaţia fişierului de configurare al daemonului (settings.json):", "download-dir": "Calea implicită către directorul de descărcare:", "download-dir-free-space": "Spaţiu liber:", "incomplete-dir-enabled": "Foloseşte directorul pentru descărcare nefinalizată", "cache-size-mb": "Mărime cache:", "rename-partial-files": "Ataşează extensia '.part' fişierelor nefinalizate", "start-added-torrents": "Porneşte automat torrenții adăugaţi", "download-queue-enabled": "Număr maxim descărcari simultane:", "seed-queue-enabled": "Număr maxim încărcări simultane:", "peer-port-random-on-start": "Folosește port aleator de conectare la pornire", "port-forwarding-enabled": "Activează forwarding", "test-port": "Testează port", "port-is-open-true": "Portul este deschis", "port-is-open-false": "Portul este închis", "testing": "Testare...", "encryption": "Encriptare:", "encryption-type": {"required": "Obligatorie", "preferred": "Preferată", "tolerated": "Tolerată"}, "utp-enabled": "Activează µTP (UPnP)", "dht-enabled": "Activează DHT", "lpd-enabled": "Activează LPD", "pex-enabled": "Activează PEX", "peer-limit-global": "<PERSON><PERSON><PERSON><PERSON> maxim de legaturi (peers):", "peer-limit-per-torrent": "Număr maxim de peers/torrent:", "speed-limit-down-enabled": "Viteză maximă cumulată de descărcare:", "speed-limit-up-enabled": "Viteză maximă cumulată de încărcare:", "alt-speed-enabled": "Activează viteza alternativă", "alt-speed-down": "Viteză maximă cumulată de descărcare:", "alt-speed-up": "Viteză maximă cumulată de încărcare:", "alt-speed-time-enabled": "Folosește programare", "alt-speed-time": "Timp：", "weekday": {"1": "<PERSON><PERSON>", "2": "<PERSON><PERSON><PERSON>", "3": "<PERSON><PERSON><PERSON><PERSON>", "4": "<PERSON><PERSON>", "5": "<PERSON><PERSON>", "6": "Sâmbătă", "0": "<PERSON><PERSON><PERSON><PERSON>"}, "blocklist-enabled": "Folosește blocklist", "blocklist-size": "Blocklist has %n rules.", "seedRatioLimited": "Rația implicită pentru încărcare:", "queue-stalled-enabled": "Torentele inactive sunt considerete blocate după:", "idle-seeding-limit-enabled": "Torentele la încărcare vor fi oprite dacă sunt inactive pentru:", "minutes": "Minute", "nochange": "<PERSON><PERSON>", "saving": "<PERSON><PERSON>...", "show-bt-servers": "Show 'BT servers' on Trackers:", "restore-default-settings": "Restore UI Default Settings", "language": "Language:", "loading": "Loading...", "hide-subfolders": "When clicking on the data directory, hide subfolders content in the list:", "simple-check-mode": "Checked only one torrent when you right-click on the torrent list:", "nav-contents": "Navigation bar Display content:", "labels-manage": {"name": "Label Name", "description": "Description", "color": "Color", "actions": "Actions", "import-confirm": "Do you want to import labels? This overrides the current configuration."}}, "public": {"button-ok": "OK", "button-cancel": "<PERSON><PERSON><PERSON><PERSON>", "button-reload": "Re<PERSON><PERSON><PERSON><PERSON><PERSON>", "button-save": "Salvează", "button-close": "<PERSON><PERSON><PERSON>", "button-update": "Update", "button-config": "Config", "button-addnew": "Add", "button-edit": "Edit", "button-delete": "Delete", "button-export": "Export", "button-import": "Import"}, "about": {"infos": "Autor：culturist<br/>Declarație：Dacă dețineți drepturi de autor pentru pictogramele folosite aici, vă rog, contactați-mă.", "check-update": "Check Update", "home": "Project Home", "help": "Wiki", "donate": "Donate", "pt-plugin": "PT Plugin"}, "torrent-remove": {"title": "Confirmă ș<PERSON>ea", "confirm-text": "Confirmă ștergerea torrenților selectați?", "remove-data": "Șterge datele salvate", "remove-error": "Ștergere eș<PERSON>ă!"}, "torrent-changeDownloadDir": {"title": "Confirmă noul director", "old-download-dir": "<PERSON><PERSON><PERSON>l director:", "new-download-dir": "Noul director:", "move-data": "Dacă bifat se va muta din locația anterioară sau setează director nou pentru <PERSON>.", "set-error": "eroare !", "recheck-data": "Recheck data."}, "system-replaceTracker": {"title": "Înlocuiește trackerul", "old-tracker": "Vech<PERSON>l tracker：", "new-tracker": "Noul tracker：", "tip": "Această funcție va găsi <b>TOATE</b> track<PERSON>le.", "not-found": "Tracker nu poate fi găsit."}, "auto-match-data-folder": {"title": "Automatically matches data directory", "torrent-count": "Torrent count:", "folder-count": "Folder count:", "dictionary": "Folders Dictionary", "time-begin": "Begin time:", "time-now": "Now:", "status": "Status:", "ignore": "Ignore", "working-close-confirm": "Torrents are currently downloading, are sure you want to close Transmission?", "time-interval": "Time interval (seconds):", "work-mode-title": "Mode:", "work-mode": {"1": "Individually matched by torrent", "2": "Individually matched by folder"}}, "torrent-rename": {"title": "Renaming a Torrent's Path", "oldname": "Old", "newname": "New"}, "torrent-attribute-add-tracker": {"title": "Add Trackers", "tip": "One Line, One Tracker"}, "torrent-setLabels": {"title": "Set User Labels", "available": "Available:", "selected": "Selected:"}}, "torrent": {"fields": {"id": "#", "name": "Nume", "hashString": "HASH", "downloadDir": "Director <PERSON><PERSON><PERSON><PERSON><PERSON>", "totalSize": "Mărime totală", "status": "Status", "percentDone": "Procent finalizat", "remainingTime": "<PERSON><PERSON>", "addedDate": "Data adăugare", "completeSize": "<PERSON><PERSON>", "rateDownload": "Viteză <PERSON>", "rateUpload": "<PERSON>itez<PERSON>", "leecherCount": "<PERSON><PERSON><PERSON>", "seederCount": "<PERSON><PERSON><PERSON>", "uploadedEver": "Total încărcat", "uploadRatio": "Rația", "queuePosition": "Queue", "activityDate": "Activity Date", "trackers": "Trackers", "labels": "User Labels"}, "status-text": {"0": "Pauză", "1": "Așteaptă pentru verificare", "2": "Verificare", "3": "Așteapta pentru descărcare", "4": "Des<PERSON><PERSON><PERSON>", "5": "Așteaptă pentru încărcare", "6": "Trimite date"}, "attribute": {"tabs": {"base": "Principal", "servers": "<PERSON><PERSON>", "files": "Fișiere", "users": "Peers", "config": "<PERSON><PERSON><PERSON>"}, "files-fields": {"name": "Nume", "length": "Mărime", "percentDone": "Procent finalizat", "bytesCompleted": "Mărime finalizat", "wanted": "<PERSON><PERSON><PERSON><PERSON>", "priority": "Prioritate"}, "servers-fields": {"announce": "Link tracker", "announceState": "Status", "lastAnnounceResult": "Info", "lastAnnounceSucceeded": "Succes", "lastAnnounceTime": "<PERSON><PERSON> pentru verificare tracker", "lastAnnounceTimedOut": "<PERSON><PERSON><PERSON> la conectare tracker ?", "downloadCount": "<PERSON><PERSON><PERSON><PERSON>", "nextAnnounceTime": "Următoarea verificare tracker"}, "peers-fields": {"address": "Adresa IP", "clientName": "Client", "flagStr": "Flag", "progress": "Progres", "rateToClient": "Viteza descărcare a conectatului", "rateToPeer": "Viteza de transfer către conectat"}, "status": {"true": "<PERSON><PERSON><PERSON><PERSON>", "false": "Fals"}, "priority": {"0": "Normală", "1": "Mare", "-1": "<PERSON><PERSON><PERSON>"}, "label": {"name": "Nume:", "addedDate": "Data adăugării:", "totalSize": "Mărime totală:", "completeSize": "Mărime descărcat:", "leftUntilDone": "Rămas de finalizat:", "hashString": "HASH:", "downloadDir": "Director pen<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>:", "status": "Status:", "rateDownload": "Viteză descărcare:", "rateUpload": "Viteză <PERSON>:", "leecherCount": "<PERSON><PERSON>:", "seederCount": "Seeder:", "uploadedEver": "Total încărcat:", "uploadRatio": "Rația încărcat:", "creator": "Creator:", "dateCreated": "Data creării:", "comment": "Comentariu:", "errorString": "Eroare în șirȘ", "downloadLimited": "Viteza maximă de descărcare：", "uploadLimited": "Viteza maximă de încărcare：", "peer-limit": "Număr maxim de peers/torrent：", "seedRatioMode": "Rația pentru încărcare：", "seedIdleMode": "Torrentele la încărcat vor fi oprite dacă sunt inactive pentru：", "doneDate": "Finish Time:", "seedTime": "Seed Time:"}, "tip": {"button-allow": "Descărcarea fi<PERSON>elo<PERSON> bifate", "button-deny": "<PERSON>u des<PERSON><PERSON><PERSON> fi<PERSON>ele :", "button-priority": "Prioritate", "button-tracker-add": "Add New Tracker", "button-tracker-edit": "Edit Tracker", "button-tracker-remove": "<PERSON><PERSON><PERSON> Tracker"}, "other": {"tracker-remove-confim": "Confirmi ștergerea acestui tracker ?"}}}, "torrent-head": {"buttons": {"autoExpandAttribute": "Afișează automat atributele"}}, "public": {"text-unknown": "Necunoscut", "text-drop-title": "Fă 'drag and drop' pentru ad<PERSON>ugare în Transmission.", "text-saved": "<PERSON><PERSON>", "text-nochange": "<PERSON><PERSON>", "text-info": "Informții", "text-confirm": "Ești sigur ?", "text-browsers-not-support-features": "Browserul folosit nu suportă această funcție !", "text-download-update": "Descarcă această actualizare", "text-have-update": "O actualizare este disponibilă", "text-on": "ON", "text-off": "OFF", "text-how-to-update": "How to update?", "text-ignore-this-version": "Ignore this version"}}