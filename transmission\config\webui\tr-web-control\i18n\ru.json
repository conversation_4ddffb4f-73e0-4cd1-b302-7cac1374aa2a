{"name": "ru", "author": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DarkAlexWang, vodek3", "system": {"title": "Transmission WEB Control", "status": {"connect": "Соединение...", "connected": "Соединено", "queue": "Очередь:", "queuefinish": "Очередь загрузки завершена.", "notfinal": "Не завершена", "checked": "Отмечено %n торрент(а/ов): "}}, "error": {"data-error": "Ошибка данных.", "data-post-error": "Ошибка отправки данных.", "rename-error": "Ошибка переименования файла/папки!"}, "config": {"save-path": "Папка для загрузки"}, "toolbar": {"start": "Старт", "pause": "Пауза", "recheck": "Перепроверить", "start-all": "Начать все", "pause-all": "Пауза все", "remove": "Удалить", "remove-all": "Удалить все", "remove-data": "Удалить данные", "add-torrent": "Добавить торрент", "attribute": "Атрибут", "alt-speed": "Альтернативная скорость", "system-config": "Настройки", "system-reload": "Обновить", "about": "О программе", "reload-time": "Автообновление:", "reload-time-unit": "с/раз", "autoreload-disabled": "Отключено", "autoreload-enabled": "Включено", "search-prompt": "Поиск по торрентам", "tracker-replace": "Заменить трекеры", "queue": "Очередь", "ui-mobile": "Мобильный UI", "ui-original": "Оригинальный UI", "ui-computer": "Стандартный UI", "plugin": "Плагины", "rename": "Переименовать", "copy-path-to-clipboard": "Скопировать путь загрузки в буфер обмена", "tip": {"start": "Запустить выбранные торренты", "pause": "Приостановить выбранные торренты", "recheck": "Перепроверить выбранные торренты", "recheck-confirm": "Вы уверены что необходимо перепроверить выбранные торренты? Это займет некоторое время!", "start-all": "Запустить все", "pause-all": "Приостановить все", "remove": "Удалить выбранные торренты", "delete-all": "Удалить все", "delete-data": "Удалить данные", "add-torrent": "Добавить торрент", "attribute": "Атрибут", "alt-speed": "Альтернативная скорость", "system-config": "Настройки", "system-reload": "Обновить", "about": "О программе", "autoreload-disabled": "Отключить автообновление", "autoreload-enabled": "Включить автообновление", "tracker-replace": "Заменить трекеры", "change-download-dir": "Изменить папку загрузки", "ui-mobile": "Мобильный UI", "ui-original": "Оригинальный UI", "more-peers": "Запросить у трекера больше пиров", "rename": "Переименовать торрент", "copy-path-to-clipboard": "Скопировать путь загрузки в буфер обмена"}}, "menus": {"queue": {"move-top": "Сдвинуть в начало", "move-up": "Сдвинуть выше", "move-down": "Сдвинуть ниже", "move-bottom": "Сдвинуть в конец"}, "plugin": {"auto-match-data-folder": "Автосопоставление путей"}, "setLabels": "Присвоить метки", "copyMagnetLink": "Скопировать magnet-ссылку в буфер обмена"}, "title": {"left": "Навигация", "list": "Торренты", "attribute": "Атрибуты", "status": "Статус"}, "tree": {"all": "Все", "active": "Активные", "paused": "Приостановлены", "downloading": "Загружаются", "sending": "Раздаются", "error": "С ошибкой", "warning": "С предупреждением", "actively": "Активные", "check": "Проверяются", "wait": "Ожидание", "search-result": "Результаты поиска", "status": {"loading": "Загрузка..."}, "statistics": {"title": "Статистика", "cumulative": "Общая", "current": "Текущая", "uploadedBytes": "Всего отдано: ", "downloadedBytes": "Всего загружено: ", "filesAdded": "Файлов: ", "sessionCount": "Сессий: ", "secondsActive": "Время работы: "}, "servers": "Трекеры", "folders": "Папки", "toolbar": {"nav": {"folders": "Папки"}}, "labels": "Метки"}, "statusbar": {"downloadspeed": "Скорость загрузки:", "uploadspeed": "Скорость отдачи:", "version": "Версия:"}, "dialog": {"torrent-add": {"download-dir": "Папка загрузки:", "used-download-dir": "Используемая папка загрузки:", "torrent-url": "Ссылка на .torrent или magnet:", "tip-torrent-url": "Новая ссылка с новой строки", "autostart": "Начать загрузку:", "tip-autostart": "", "set-default-download-dir": "Выбрать как папку по умолчанию", "upload-file": "Файлы .torrent:", "nosource": "Указанный источник не является файлом .torrent.", "tip-title": "Приоритет для загрузок указанных через URL"}, "system-config": {"title": "Настройка сервера", "tabs": {"base": "Базовые", "network": "Сеть", "limit": "Ограничения", "alt-speed": "Пла<PERSON><PERSON><PERSON><PERSON>щик", "dictionary-folders": "Список путей", "more": "Дополнительно", "labels": "Метки"}, "config-dir": "Папка конфигурации Transmission (settings.json):", "download-dir": "Папка для загрузки по умолчанию:", "download-dir-free-space": "Свободно места: ", "incomplete-dir-enabled": "Папка для не полностью загруженных файлов:", "cache-size-mb": "Размер дискового кеша:", "rename-partial-files": "Добавлять расширение .part к не полностью загруженным файлам", "start-added-torrents": "Автостарт для добавленных торрентов", "download-queue-enabled": "Включить очередь загрузки, одновременно:", "seed-queue-enabled": "Включить очередь отдачи, одновременно:", "peer-port-random-on-start": "Использовать случайный порт при запуске", "port-forwarding-enabled": "Включить проброс портов (UPnP)", "test-port": "Проверить", "port-is-open-true": "Порт открыт", "port-is-open-false": "Порт закрыт", "testing": "Тестирую...", "encryption": "Шифрование:", "encryption-type": {"required": "Требуется", "preferred": "Включено", "tolerated": "Отключено"}, "utp-enabled": "Включить µTP (UPnP)", "dht-enabled": "Включить DHT", "lpd-enabled": "Включить Local Peer Discovery", "pex-enabled": "Включить обмен пирами", "peer-limit-global": "Максимум пиров на все торренты:", "peer-limit-per-torrent": "Максимум пиров на торрент:", "speed-limit-down-enabled": "Максимальная скорость загрузки:", "speed-limit-up-enabled": "Максимальная скорость отдачи:", "alt-speed-enabled": "Включить альтернативные скорости", "alt-speed-down": "Альтернативная скорость загрузки:", "alt-speed-up": "Альтернативная скорость отдачи:", "alt-speed-time-enabled": "Использовать планировщик", "alt-speed-time": "Время:", "weekday": {"1": "Понедельник", "2": "Вторник", "3": "Среда", "4": "Четверг", "5": "Пятница", "6": "Суббота", "0": "Воскресенье"}, "blocklist-enabled": "Использовать черный список", "blocklist-size": "Черный список содержит %n правил.", "seedRatioLimited": "Раздача будет остановлена на рейтинге:", "queue-stalled-enabled": "Считать активные торрренты зависшими, если нет активности:", "idle-seeding-limit-enabled": "Раздача будет остановлена если нет активности:", "minutes": "<PERSON>и<PERSON><PERSON><PERSON>", "nochange": "Без изменений", "saving": "Сохранение...", "show-bt-servers": "Показать 'BT-серверы' в окне 'Навигация' -> 'Трекеры'.", "restore-default-settings": "Настойки по-умолчанию", "language": "Язык:", "loading": "Загрузка...", "hide-subfolders": "При перемещению по дереву папок (окно 'Навигация', раздел 'Папки') в окне 'Торренты' раздачи из подпапок не отображаются:", "simple-check-mode": "Правый клик по торренту в списке (окно 'Торренты') выделяет только один торрент:", "nav-contents": "Содержание окна 'Навигация'", "labels-manage": {"name": "Имя метки", "description": "Описание", "color": "Цвет", "actions": "Действия", "import-confirm": "Вы действительно хотите импортировать метки? Данное действие перезапишет текущие настройки меток."}, "import-config": "Импорт настроек из файла", "export-config": "Экспорт текущих настроек ", "import-config-confirm": "Вы действительно хотите импортировать настройки? Данное действие перезапишет текущие настройки.", "script-torrent-done-enabled": "Выполнить скрипт после завершения загрузки торрента:", "ipinfo": "Маркер доступа IPinfo.io (IPinfo.io access token):"}, "public": {"button-ok": "OK", "button-cancel": "Отмена", "button-reload": "Перегрузить", "button-save": "Сохранить", "button-close": "Закрыть", "button-update": "Обновить", "button-config": "Настройка", "button-addnew": "Добавить", "button-edit": "Редактировать", "button-delete": "Удалить", "button-export": "Экспорт", "button-import": "Импорт"}, "about": {"infos": "Автор: culturist<br/>Дисклеймер: Большинство используемых изображений найдены в сети, если они нарушают Ваши авторские права, сообщите автору для удаления.", "check-update": "Проверить обновления", "home": "Домашняя страница проекта", "help": "Wiki", "donate": "Поддержать проект", "pt-plugin": "PT Plugin"}, "torrent-remove": {"title": "Удалить торрент", "confirm-text": "Вы согласны удалить выбранные торренты?", "remove-data": "Удалить данные", "remove-error": "Удаление прошло неудачно!"}, "torrent-changeDownloadDir": {"title": "Изменить папку загрузки", "old-download-dir": "Старая:", "new-download-dir": "Новая:", "move-data": "Перенести данные из старой папки в новую.", "set-error": "ошибка!", "recheck-data": "Перепроверить данные."}, "system-replaceTracker": {"title": "Заменить трекеры", "old-tracker": "Cтарый трекер:", "new-tracker": "Новой трекер:", "tip": "Эта функция будет искать трекер <b>во всех торрентах</b>.", "not-found": "Трекер не найден."}, "auto-match-data-folder": {"title": "Автоматическое сопоставление путей", "torrent-count": "Количество торрентов:", "folder-count": "Количество папок:", "dictionary": "Список путей", "time-begin": "Время начала:", "time-now": "Текущее время:", "status": "Статус:", "ignore": "Игнорировать", "working-close-confirm": "Работа выполняется, закрыть?", "time-interval": "Интервал (секунд):", "work-mode-title": "Режим:", "work-mode": {"1": "Соответствие по торренту", "2": "Соответствие по папке"}}, "torrent-rename": {"title": "Переименова<PERSON><PERSON> Torrent", "oldname": "Старый", "newname": "Новый"}, "torrent-attribute-add-tracker": {"title": "Добавить трекеры", "tip": "Одна строка, один трекер"}, "torrent-setLabels": {"title": "Присвоить метки", "available": "Доступные:", "selected": "Выбранные:"}, "export-config": {"title": "Выберите какие настройки экспортировать", "option-all": "Все настройки", "option-system": "Настройки Web Control", "option-dictionary": "Список путей (Папки загрузки)", "option-server": "Настройки Transmission (Папки загрузки, кэш, ограничения скорости, и т. п.)"}, "import-config": {"title": "Выберите какие настройки импортировать", "invalid-file": "Файл настроек поврежден"}}, "torrent": {"fields": {"id": "#", "name": "Название", "hashString": "Хэш", "downloadDir": "Папка загрузки", "totalSize": "Размер", "status": "Состояние", "percentDone": "Готово %", "remainingTime": "Осталось", "addedDate": "Добавлен", "completeSize": "Загружено", "rateDownload": "Загрузка", "rateUpload": "Отдача", "leecherCount": "<PERSON>и<PERSON>и", "seederCount": "Сиды", "uploadedEver": "Отдано", "uploadRatio": "<PERSON>ей<PERSON>инг", "queuePosition": "Очередь", "activityDate": "Последняя активность", "trackers": "Трекеры", "labels": "Метки", "doneDate": "Загрузка завершена"}, "status-text": {"0": "Пауза", "1": "Ожидает проверки", "2": "Проверка", "3": "Ожи<PERSON><PERSON><PERSON>т загрузку", "4": "Загрузка", "5": "Ожидает раздачу", "6": "Раздача"}, "attribute": {"tabs": {"base": "Общие", "servers": "Трекеры", "files": "Файлы", "users": "Пиры", "config": "Настройки"}, "files-fields": {"name": "Название", "length": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "percentDone": "Загружено %", "bytesCompleted": "Загружено", "wanted": "Загружать", "priority": "Приоритет"}, "servers-fields": {"announce": "<PERSON>н<PERSON><PERSON><PERSON>", "announceState": "Статус", "lastAnnounceResult": "Результат", "lastAnnounceSucceeded": "Успешно", "lastAnnounceTime": "Время анонса", "lastAnnounceTimedOut": "Тайм-аут", "downloadCount": "Количество загрузок", "nextAnnounceTime": "Следующий анонс", "leecherCount": "Количество личеров", "seederCount": "Количество сидеров", "announceStateText": {"0": "Неактивны", "1": "<PERSON>ж<PERSON><PERSON><PERSON><PERSON>т", "2": "В очереди", "3": "Активны"}}, "peers-fields": {"address": "IP адрес", "port": "Порт", "isUTP": "UTP включен", "clientName": "Кли<PERSON><PERSON>т", "flagStr": "Страна", "progress": "Прогресс", "rateToClient": "Скорость приема", "rateToPeer": "Скорость отдачи"}, "status": {"true": "Да", "false": "Нет"}, "priority": {"0": "Нормальный", "1": "Высокий", "-1": "Низкий"}, "filter-template-text": {"1": "Все", "2": "Мусорные файлы (паддинги) BitComet", "3": "Ненужные файлы"}, "label": {"name": "Название:", "addedDate": "Добавлено:", "totalSize": "Общий объем:", "completeSize": "Загружено:", "leftUntilDone": "Осталось:", "hashString": "Хэш:", "downloadDir": "Папка:", "status": "Статус:", "rateDownload": "Скорость загрузки:", "rateUpload": "Скорость отдачи:", "leecherCount": "Личеры:", "seederCount": "Сидеры:", "uploadedEver": "Отдано всего:", "uploadRatio": "Рейтинг отдачи:", "creator": "Создан:", "dateCreated": "Дата создания:", "comment": "Комментарий:", "errorString": "Ошибка:", "downloadLimited": "Максимальная скорость приема: ", "uploadLimited": "Максимальная скорость отдачи: ", "peer-limit": "Максимум пиров на торрент: ", "seedRatioMode": "Раздача будет остановлена на рейтинге: ", "seedIdleMode": "Раздача будет остановлена если нет активности: ", "doneDate": "Время окончания:", "seedTime": "Время сидирования:"}, "tip": {"button-allow": "Загрузить выбранные файлы", "button-deny": "Пропустить выбранные файлы", "button-priority": "Установить приоритет", "button-filter": "Введите регулярные выражения и нажмите кнопку paging, чтобы обновить", "button-tracker-add": "Добавить новый трекер", "button-tracker-edit": "Редактировать трекер", "button-tracker-remove": "Удалить трекер"}, "other": {"tracker-remove-confim": "Вы уверены, что хотите удалить этот Трекер?"}}}, "torrent-head": {"buttons": {"autoExpandAttribute": "Отобража<PERSON>ь атрибуты"}}, "public": {"text-unknown": "Неизвестно", "text-drop-title": "Перетащите .torrent файл в область \"Загрузки\" чтобы добавить задачу.", "text-saved": "Сохранено", "text-nochange": "Без изменений", "text-info": "Информация", "text-confirm": "Вы уверены?", "text-browsers-not-support-features": "Текущий браузер не поддерживает этот функционал!", "text-download-update": "Загрузить это обновление", "text-have-update": "Доступно новое обновление", "text-on": "<PERSON><PERSON><PERSON>", "text-off": "Вы<PERSON>л", "text-how-to-update": "Как обновить?", "text-ignore-this-version": "Игнорировать данную версию", "text-json-file-parsing-failed": "Ошибка обработки файла JSON!"}}