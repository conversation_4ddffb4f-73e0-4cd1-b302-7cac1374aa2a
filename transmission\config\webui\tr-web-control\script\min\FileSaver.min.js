var saveAs=saveAs||function(b){var h=b.document,q=b.URL||b.webkitURL||b,m=h.createElementNS("http://www.w3.org/1999/xhtml","a"),y="download"in m,z=function(a){var e=h.createEvent("MouseEvents");e.initMouseEvent("click",!0,!1,b,0,0,0,0,0,!1,!1,!1,!1,0,null);return a.dispatchEvent(e)},t=b.webkitRequestFileSystem,u=b.requestFileSystem||t||b.mozRequestFileSystem,A=function(a){(b.setImmediate||b.setTimeout)(function(){throw a;},0)},v=0,k=[],r=function(a,e,d){e=[].concat(e);for(var b=e.length;b--;){var c=
a["on"+e[b]];if("function"===typeof c)try{c.call(a,d||a)}catch(n){A(n)}}},l=function(a,e){var d=this,c=a.type,h=!1,n=function(a){a=(b.URL||b.webkitURL||b).createObjectURL(a);k.push(a);return a},l=function(){r(d,["writestart","progress","write","writeend"])},f=function(){if(h||!p)p=n(a);w.location.href=p;d.readyState=d.DONE;l()},g=function(a){return function(){if(d.readyState!==d.DONE)return a.apply(this,arguments)}},x={create:!0,exclusive:!1};d.readyState=d.INIT;e||(e="download");if(y){var p=n(a);
m.href=p;m.download=e;if(z(m)){d.readyState=d.DONE;l();return}}if(b.chrome&&c&&"application/octet-stream"!==c){var q=a.slice||a.webkitSlice;a=q.call(a,0,a.size,"application/octet-stream");h=!0}var w="application/octet-stream"===c||t?b:b.open();u?(v+=a.size,u(b.TEMPORARY,v,g(function(b){b.root.getDirectory("saved",x,g(function(b){var c=function(){b.getFile(e,x,g(function(b){b.createWriter(g(function(c){c.onwriteend=function(a){w.location.href=b.toURL();k.push(b);d.readyState=d.DONE;r(d,"writeend",
a)};c.onerror=function(){var a=c.error;a.code!==a.ABORT_ERR&&f()};["writestart","progress","write","abort"].forEach(function(a){c["on"+a]=d["on"+a]});c.write(a);d.abort=function(){c.abort();d.readyState=d.DONE};d.readyState=d.WRITING}),f)}),f)};b.getFile(e,{create:!1},g(function(a){a.remove();c()}),g(function(a){a.code===a.NOT_FOUND_ERR?c():f()}))}),f)}),f)):f()},c=l.prototype;c.abort=function(){this.readyState=this.DONE;r(this,"abort")};c.readyState=c.INIT=0;c.WRITING=1;c.DONE=2;c.error=c.onwritestart=
c.onprogress=c.onwrite=c.onabort=c.onerror=c.onwriteend=null;b.addEventListener("unload",function(){for(var a=k.length;a--;){var b=k[a];"string"===typeof b?q.revokeObjectURL(b):b.remove()}k.length=0},!1);return function(a,b){return new l(a,b)}}(self);
