String.prototype.getQueryString=function(a,b){if(b==undefined)b="&";var c;if(c=this.match(RegExp("(^|"+b+"|\\?)"+a+"=([^"+b+"]*)("+b+"|$)")))return unescape(c[2]);return null};String.prototype.right=function(a){return this.substr(-a)};String.prototype.getHostName=function(){var a=this,b=this.match(/^\w+\:\/\/([^\/]*).*/);if(typeof b!="undefined"&&null!=b){a=b[1];if(a.indexOf(":")!=-1)a=a.split(":")[0]}return a};
Number.prototype.formatNumber=function(a){this.fStr=function(c,d,g){if(c==""||c==undefined)return d==""||d==undefined?"":d;var e=s=r="",f;if(!g){c=c.split("").reverse().join("");d=d.split("").reverse().join("")}for(var h=j=0;h<d.length;h++,j++){s=c.charAt(j);if(s!=undefined){e=d.charAt(h);switch(e){case "#":r+=s;f=h;break;case "0":r=s||s==e?r+s:r+0;f=h;break;case ".":r+=s==e?s:(j--,e);break;case ",":r+=s==e?s:(j--,e);break;default:r+=e;j--}}}if(j!=c.length&&d.charAt(d.length-1)!="0"&&f!=d.length&&
d.charAt(f)!="0")r=r.substr(0,f+1)+c.substr(j)+r.substr(f+1);r=(g?r:r.split("").reverse().join("")).replace(/(^,)|(,$)|(,,+)/g,"");if(r.substr(0,1)==",")r=r.substr(1);if(r.substr(0,2)=="-,")r="-"+r.substr(2);return r};var b=this.toString();if(b.length==0)return"";if(a==undefined)return this;a=a.split(".");b=b.split(".");return a.length>1?this.fStr(b[0],a[0])+"."+this.fStr(b[1],a[1],1):this.fStr(b[0],a[0])};
String.prototype.getRGB=function(){var a=/^#([0-9a-f]{3}|[0-9a-f]{6})$/,b=this.toLowerCase();if(b&&a.test(b)){if(b.length===4){var c="#";for(a=1;a<4;a+=1)c+=b.slice(a,a+1).concat(b.slice(a,a+1));b=c}c=[];for(a=1;a<7;a+=2)c.push(parseInt("0x"+b.slice(a,a+2)));return{R:c[0],G:c[1],B:c[2]}}else return this};function getGrayLevel(a){a||(a={R:0,G:0,B:0});if(typeof a==="string")a=a.getRGB();return(0.299*a.R+0.587*a.G+0.114*a.B)/255}
function getLocalTime(a){return(new Date(parseInt(a)*1E3)).toLocaleString().replace(/年|月/g,"-").replace(/日/g," ")}function formatLongTime(a){a=new Date(parseInt(a)*1E3);return formatDate(a)}
function formatDate(a,b){b||(b="yyyy-mm-dd hh:nn:ss");if(a instanceof Date){var c=a.getFullYear(),d=c.toString().substring(2),g=a.getMonth()+1,e=g<10?"0"+g:g,f=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"][g],h=a.getDate(),i=h<10?"0"+h:h,k=a.getHours(),n=k<10?"0"+k:k,l=a.getMinutes(),o=l<10?"0"+l:l,m=a.getSeconds(),p=m<10?"0"+m:m;b=b.replace(/yyyy/i,c);b=b.replace(/yy/i,d);b=b.replace(/mmm/i,f);b=b.replace(/mm/i,e);b=b.replace(/m/i,g);b=b.replace(/dd/i,i);b=b.replace(/d/i,
h);b=b.replace(/hh/i,n);b=b.replace(/h/i,k);b=b.replace(/nn/i,o);b=b.replace(/n/i,l);b=b.replace(/ss/i,p);return b=b.replace(/s/i,m)}else return""}
function formatSize(a,b,c){if(a==0)return b==true?"":c=="speed"?"0.00 KB/s":"0.00";b="";var d="KB";if(a<1024E3){b=a/1024;d="KB"}else if(a<1048576E3){b=a/1048576;d="MB"}else if(a<1073741824E3){b=a/1073741824;d="GB"}else if(a<1099511627776E3){b=a/1099511627776;d="TB"}else{b=a/1125899906842624;d="PB"}if(c=="speed")d+="/s";return b.formatNumber("###,###,###,###.00 ")+d}function getHoursFromMinutes(a){return("00"+parseInt(a/60,10)).right(2)+":"+("00"+a%60).right(2)}
function getMinutesFromHours(a){return parseInt(a.split(":")[0],10)*60+parseInt(a.split(":")[1],10)}function getTotalTime(a,b){b||(b="%dd %hh %mm %ss ");var c=Math.floor(a/864E5),d=a%864E5,g=Math.floor(d/36E5),e=d%36E5;d=Math.floor(e/6E4);e=Math.round(e%6E4/1E3);var f=b;f=c==0?f.replace(/(%d+\s)/,""):f.replace("%d",c);f=g==0?f.replace(/(%h+\s)/,""):f.replace("%h",g);f=d==0?f.replace(/(%m+\s)/,""):f.replace("%m",d);return f=e==0?f.replace(/(%s+\s)/,""):f.replace("%s",e)}
function arrayObjectSort(a,b){return function(c,d){var g=c[a],e=d[a];return g<e?b=="desc"?1:-1:g>e?b=="desc"?-1:1:0}}function timedChunk(a,b,c,d,g){var e=a.concat();if(d==undefined)d=25;setTimeout(function(){var f=+new Date;do b.call(c,e.shift());while(e.length>0&&+new Date-f<100);if(e.length>0)setTimeout(arguments.callee,d);else g&&g(a)},d)}
(function(a){a.fn.fadeInAndOut=function(b,c,d){b={speed:b,easing:c,fn:d};a.extend(b,a.fn.fadeInAndOut.defaults);this.fadeIn(b.speed).delay(b.speed).fadeOut(b.speed,b.easing,b.fn)};a.fn.fadeInAndOut.defaults={speed:1E3,easing:"swing",fn:null}})(jQuery);function uniq(a){var b={};return a.filter(function(c){return b.hasOwnProperty(c)?false:b[c]=true})}
function loadFileContent(a,b){$("<input id='file-loadContent' type='file' style='display:none;' multiple='true'/>").on("change",function(){var c=this;if(c.files.length>0&&c.files[0].name.length>0){var d=c.files,g=d.length,e=0,f=new FileReader;f.onload=function(i){b&&b.call(system,i.target.result);h()};f.onerror=function(){alert("文件加载失败");console.log("文件加载失败");h()};var h=function(i){if(e==g){$(c).remove();c.value=""}else{i=d[e];var k=i.name.lastIndexOf(".");k=i.name.substr(k+1);e++;if(a)if(k!=a){alert("文件类型错误");
return}f.readAsText(i)}};h()}}).click()}function saveFileAs(a,b){try{var c=window.Blob||window.WebKitBlob,d=false;if(c)try{new c([],{type:"text/plain"});d=true}catch(g){}var e=null;if(d)e=new c([b],{type:"text/plain"});else{var f=new (window.BlobBuilder||window.WebKitBlobBuilder);f.append(b);e=f.getBlob("text/plain")}saveAs(e,a)}catch(h){console.log(h.toString())}};
