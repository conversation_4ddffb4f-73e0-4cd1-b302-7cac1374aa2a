var system={version:"1.6.1",rootPath:"tr-web-control/",codeupdate:"20200913",configHead:"transmission-web-control",config:{autoReload:!0,reloadStep:5e3,pageSize:30,pagination:!0,pageList:[10,20,30,40,50,100,150,200,250,300,5e3],defaultSelectNode:null,autoExpandAttribute:!1,defaultLang:"",foldersShow:!1,theme:"default",showBTServers:!1,ipInfoToken:"",ui:{status:{tree:{},layout:{main:{},body:{},left:{}},panel:{},size:{nav:{},attribute:{}}}},hideSubfolders:!1,simpleCheckMode:!1,nav:{servers:!0,folders:!0,statistics:!0,labels:!1},labels:[],labelMaps:{},ignoreVersion:[]},storageKeys:{dictionary:{folders:"dictionary.folders"}},dictionary:{folders:null},checkUpdateScript:"https://api.github.com/repos/ronggang/transmission-web-control/releases/latest",contextMenus:{},panel:null,lang:null,reloading:!1,autoReloadTimer:null,downloadDir:"",islocal:!1,B64:new Base64,currentTorrentId:0,flags:[],control:{tree:null,torrentlist:null},userConfig:{torrentList:{fields:[],sortName:null,sortOrder:"asc"}},serverConfig:null,serverSessionStats:null,templates:{},checkedRows:[],uiIsInitialized:!1,popoverCount:0,currentListDir:"",setlang:function(lang,callback){lang||(lang=this.config.defaultLang?this.config.defaultLang:navigator.language||navigator.browserLanguage),lang||(lang="zh-CN"),-1!=lang.indexOf("-")&&(lang=lang.split("-")[0].toLocaleLowerCase()+"-"+lang.split("-")[1].toLocaleUpperCase()),this.languages[lang]||(lang="en"),lang=lang.replace("-","_"),$.getJSON(system.rootPath+"i18n/"+lang+".json",(function(result){result&&(system.lang=$.extend(!0,system.defaultLang,result)),system.resetLangText(),$.getScript(system.rootPath+"script/easyui/locale/easyui-lang-"+lang+".js").done((function(script,textStatus){callback&&callback()})).fail((function(jqxhr,settings,exception){$.getScript(system.rootPath+"script/easyui/locale/easyui-lang-en.js",(function(){callback&&callback()}))}))}))},init:function(lang,islocal,devicetype){this.readConfig(),this.lastUIStatus=JSON.parse(JSON.stringify(this.config.ui.status)),this.islocal=1==islocal,this.panel={main:$("#main"),top:$("#m_top"),toolbar:$("#m_toolbar"),left_layout:$("#m_left_layout"),left:$("#m_left"),body:$("#m_body"),layout_body:$("#layout_body"),layout_left:$("#layout_left"),list:$("#m_list"),attribute:$("#m_attribute"),bottom:$("#m_bottom"),title:$("#m_title"),status:$("#m_status"),statusbar:$("#m_statusbar"),status_text:$("#status_text"),droparea:$("#dropArea")},null==this.lang?this.setlang(lang,(function(){system.initdata()})):this.initdata(),this.initThemes(),this.clipboard=new ClipboardJS("#toolbar_copyPath")},resetLangText:function(parent){parent||(parent=$);var items=parent.find("*[system-lang]");$.each(items,(function(key,item){var name=$(item).attr("system-lang");"["==name.substr(0,1)?$(item).html(eval("system.lang"+name)):$(item).html(eval("system.lang."+name))})),items=parent.find("*[system-tip-lang]"),$.each(items,(function(key,item){var name=$(item).attr("system-tip-lang");"["==name.substr(0,1)?$(item).attr("title",eval("system.lang"+name)):$(item).attr("title",eval("system.lang."+name))}))},initdata:function(){$(document).attr("title",this.lang.system.title+" "+this.version),$.fn.switchbutton.defaults.onText=this.lang.public["text-on"],$.fn.switchbutton.defaults.offText=this.lang.public["text-off"];var buttons=new Array,title="<span>"+this.lang.title.left+"</span>";if(buttons.length>1)for(var key in title+=buttons.join(""),this.panel.left_layout.panel("setTitle",title),this.lang.tree.toolbar.nav)switch($("#tree-toolbar-nav-"+key).linkbutton(),key){case"folders":system.config.foldersShow?$("tree-toolbar-nav-"+key).linkbutton({iconCls:"icon-enabled"}).data("status",1):$("tree-toolbar-nav-"+key).linkbutton({iconCls:"icon-disabled"}).data("status",0)}else this.panel.left_layout.panel("setTitle",title);if(title="<span>"+this.lang.title.list+"</span>",buttons.length=0,buttons.length>1)for(var key in title+=buttons.join(""),this.panel.body.panel("setTitle",title),this.lang["torrent-head"].buttons)switch($("#torrent-head-buttons-"+key).linkbutton(),key){case"autoExpandAttribute":system.config.autoExpandAttribute?$("#torrent-head-buttons-"+key).linkbutton({iconCls:"icon-enabled"}).data("status",1):$("#torrent-head-buttons-"+key).linkbutton({iconCls:"icon-disabled"}).data("status",0)}else this.panel.body.panel("setTitle",title);this.panel.status.panel("setTitle",this.lang.title.status),this.panel.attribute.panel({title:this.lang.title.attribute,onExpand:function(){0!=system.currentTorrentId&&$(this).data("isload")?system.getTorrentInfos(system.currentTorrentId):system.clearTorrentAttribute()},onLoad:function(){$(this).data("isload")||($(this).data("isload",!0),0!=system.currentTorrentId&&setTimeout((function(){system.getTorrentInfos(system.currentTorrentId)}),500))}}),$.each(this.languages,(function(key,value){$("<option/>").text(value).val(key).attr("selected",key==system.lang.name).appendTo(system.panel.top.find("#lang"))})),this.panel.top.find("#lang").change((function(){location.href="?lang="+this.value})),this.panel.toolbar.attr("class","panel-header"),this.initTree(),this.initToolbar(),this.initStatusBar(),this.initTorrentTable(),this.connect(),this.initEvent(),this.checkUpdate()},initEvent:function(){$(window).resize((function(){$("#main").layout("resize")})),this.panel.droparea[0].addEventListener("dragover",(function(e){e.stopPropagation(),e.preventDefault(),system.debug("#dropArea.dragover")}),!1),this.panel.list[0].addEventListener("dragover",(function(e){e.stopPropagation(),e.preventDefault(),system.panel.droparea.show(),system.debug("dragover")}),!1),this.panel.droparea[0].addEventListener("drop",(function(e){e.stopPropagation(),e.preventDefault(),system.panel.droparea.hide(),system.debug("drop.e.dataTransfer:",e.dataTransfer),system.checkDropFiles(e.dataTransfer.files)}),!1),this.panel.droparea[0].addEventListener("dragleave",(function(e){e.stopPropagation(),e.preventDefault(),system.panel.droparea.hide(),system.debug("dragleave")}),!1),$("#text-drop-title").html(this.lang.public["text-drop-title"]),$("#button-cancel-checked").on("click",(function(){system.control.torrentlist.datagrid("uncheckAll")})),this.panel.left.tree({onExpand:function(node){system.config.ui.status.tree[node.id]=node.state,system.saveConfig()},onCollapse:function(node){system.config.ui.status.tree[node.id]=node.state,system.saveConfig()}}),this.panel.layout_body.layout({onExpand:function(region){system.config.ui.status.layout.body[region]="open",system.saveConfig()},onCollapse:function(region){system.config.ui.status.layout.body[region]="closed",system.saveConfig()}}),this.panel.layout_left.layout({onExpand:function(region){system.config.ui.status.layout.left[region]="open",system.saveConfig()},onCollapse:function(region){system.config.ui.status.layout.left[region]="closed",system.saveConfig()}}),this.panel.main.layout({onExpand:function(region){system.config.ui.status.layout.main[region]="open",system.saveConfig()},onCollapse:function(region){system.config.ui.status.layout.main[region]="closed",system.saveConfig()}})},layoutResize:function(target,size){system.uiIsInitialized&&system.config.ui.status.size[target]&&(system.config.ui.status.size[target]=size,system.saveConfig())},navToolbarClick:function(source){var key=source.id,status=$(source).data("status"),treenode=null;switch(key){case"tree-toolbar-nav-folders":treenode=this.panel.left.tree("find","folders"),this.config.foldersShow=1!=status;break;case"tree-toolbar-nav-statistics":treenode=this.panel.left.tree("find","statistics");break;case"torrent-head-buttons-autoExpandAttribute":(treenode={}).target=null,this.config.autoExpandAttribute=1!=status}treenode&&(1==status?($(source).linkbutton({iconCls:"icon-disabled"}),$(treenode.target).parent().hide(),status=0):($(source).linkbutton({iconCls:"icon-enabled"}),$(treenode.target).parent().show(),status=1),$(source).data("status",status),this.saveConfig())},checkDropFiles:function(sources){if(sources&&sources.length){for(var files=new Array,i=0;i<sources.length;i++){var file=sources[i];"torrent"==file.name.split(".").pop().toLowerCase()&&files.push(file)}files.length>0&&system.openDialogFromTemplate({id:"dialog-torrent-addfile",options:{title:system.lang.toolbar["add-torrent"],width:620,height:system.config.nav.labels?500:300,resizable:!0},datas:{files:files}})}},initTree:function(){var items=[{id:"torrent-all",iconCls:"iconfont tr-icon-home",text:this.lang.tree.all+" ("+this.lang.tree.status.loading+")",children:[{id:"downloading",text:this.lang.tree.downloading,iconCls:"iconfont tr-icon-download"},{id:"paused",text:this.lang.tree.paused,iconCls:"iconfont tr-icon-pause2"},{id:"sending",text:this.lang.tree.sending,iconCls:"iconfont tr-icon-upload"},{id:"check",text:this.lang.tree.check,iconCls:"iconfont tr-icon-data-check"},{id:"actively",text:this.lang.tree.actively,iconCls:"iconfont tr-icon-actively"},{id:"error",text:this.lang.tree.error,iconCls:"iconfont tr-icon-errors"},{id:"warning",text:this.lang.tree.warning,iconCls:"iconfont tr-icon-warning"}]}],navContents={servers:{id:"servers",text:this.lang.tree.servers,state:"closed",iconCls:"iconfont tr-icon-servers",children:[{id:"servers-loading",text:this.lang.tree.status.loading,iconCls:"tree-loading"}]},folders:{id:"folders",text:this.lang.tree.folders,iconCls:"iconfont tr-icon-folder",state:"closed",children:[{id:"folders-loading",text:this.lang.tree.status.loading,iconCls:"tree-loading"}]},statistics:{id:"statistics",text:this.lang.tree.statistics.title,state:"closed",iconCls:"iconfont tr-icon-shuju",children:[{id:"cumulative-stats",text:this.lang.tree.statistics.cumulative,iconCls:"iconfont tr-icon-folder",children:[{id:"uploadedBytes",text:this.lang.tree.statistics.uploadedBytes,iconCls:"iconfont tr-icon-empty"},{id:"downloadedBytes",text:this.lang.tree.statistics.downloadedBytes,iconCls:"iconfont tr-icon-empty"},{id:"filesAdded",text:this.lang.tree.statistics.filesAdded,iconCls:"iconfont tr-icon-empty"},{id:"sessionCount",text:this.lang.tree.statistics.sessionCount,iconCls:"iconfont tr-icon-empty"},{id:"secondsActive",text:this.lang.tree.statistics.secondsActive,iconCls:"iconfont tr-icon-empty"}]},{id:"current-stats",text:this.lang.tree.statistics.current,iconCls:"iconfont tr-icon-folder",children:[{id:"current-uploadedBytes",text:this.lang.tree.statistics.uploadedBytes,iconCls:"iconfont tr-icon-empty"},{id:"current-downloadedBytes",text:this.lang.tree.statistics.downloadedBytes,iconCls:"iconfont tr-icon-empty"},{id:"current-filesAdded",text:this.lang.tree.statistics.filesAdded,iconCls:"iconfont tr-icon-empty"},{id:"current-sessionCount",text:this.lang.tree.statistics.sessionCount,iconCls:"iconfont tr-icon-empty"},{id:"current-secondsActive",text:this.lang.tree.statistics.secondsActive,iconCls:"iconfont tr-icon-empty"}]}]},labels:{id:"labels",text:this.lang.tree.labels,iconCls:"iconfont tr-icon-labels"}};for(var key in this.config.nav){var value=this.config.nav[key],data=navContents[key];data&&value&&items.push(data)}this.panel.left.tree({data:items,onSelect:function(node){system.loadTorrentToList({node:node}),system.currentListDir=node.downDir},lines:!0})},initUIStatus:function(){if(!this.uiIsInitialized){system.uiIsInitialized=!0;var status=this.lastUIStatus.tree,node;for(var key in status){var node;(node=this.panel.left.tree("find",key))&&node.target&&("open"==status[key]?this.panel.left.tree("expand",node.target):this.panel.left.tree("collapse",node.target))}if(this.config.defaultSelectNode)(node=this.panel.left.tree("find",this.config.defaultSelectNode))&&(this.config.foldersShow||-1==this.config.defaultSelectNode.indexOf("folders"))?this.panel.left.tree("select",node.target):(node=this.panel.left.tree("find","torrent-all"),this.panel.left.tree("select",node.target));for(var key in this.lastUIStatus.size.nav&&this.lastUIStatus.size.nav.width&&(this.panel.main.layout("panel","west").panel("resize",{width:this.lastUIStatus.size.nav.width+5}),this.panel.main.layout("resize")),this.lastUIStatus.size.attribute&&this.lastUIStatus.size.attribute.height&&(this.panel.layout_body.layout("panel","south").panel("resize",{height:this.lastUIStatus.size.attribute.height}),this.panel.layout_body.layout("resize")),status=this.lastUIStatus.layout.body)"open"==status[key]?this.panel.layout_body.layout("expand",key):this.panel.layout_body.layout("collapse",key);for(var key in status=this.lastUIStatus.layout.left)"open"==status[key]?this.panel.layout_left.layout("expand",key):this.panel.layout_left.layout("collapse",key);for(var key in status=this.lastUIStatus.layout.main)"open"==status[key]?this.panel.main.layout("expand",key):this.panel.main.layout("collapse",key)}},initTorrentTable:function(){this.control.torrentlist=$("<table/>").attr("class","torrent-list").appendTo(this.panel.list);var headContextMenu=null,selectedIndex=-1;function createHeadContextMenu(){headContextMenu&&$(headContextMenu).remove(),(headContextMenu=$("<div/>").appendTo("body")).menu({onClick:function(item){"icon-ok"==item.iconCls?(system.control.torrentlist.datagrid("hideColumn",item.name),headContextMenu.menu("setIcon",{target:item.target,iconCls:"icon-empty"})):(system.control.torrentlist.datagrid("showColumn",item.name),headContextMenu.menu("setIcon",{target:item.target,iconCls:"icon-ok"})),system.resetTorrentListFieldsUserConfig(system.control.torrentlist.datagrid("options").columns[0]),system.saveUserConfig()}});for(var fields=system.control.torrentlist.datagrid("getColumnFields"),i=0;i<fields.length;i++){var field=fields[i],col=system.control.torrentlist.datagrid("getColumnOption",field);0!=col.allowCustom&&"false"!=col.allowCustom&&headContextMenu.menu("appendItem",{text:col.title,name:field,iconCls:col.hidden?"icon-empty":"icon-ok"})}}$.get(system.rootPath+"template/torrent-fields.json?time="+new Date,(function(data){for(var fields=data.fields,_fields={},i=0;i<fields.length;i++){var item;_fields[(item=fields[i]).field]=item}for(var key in 0!=system.userConfig.torrentList.fields.length&&(fields=$.extend(fields,system.userConfig.torrentList.fields)),system.userConfig.torrentList.fields=fields,fields){var item,_field=_fields[(item=fields[key]).field];_field&&_field.formatter?item.formatter=_field.formatter:item.formatter&&delete item.formatter,_field&&_field.sortable?item.sortable=_field.sortable:item.sortable&&delete item.sortable,item.title=system.lang.torrent.fields[item.field]||item.field,system.setFieldFormat(item)}system.control.torrentlist.datagrid({autoRowHeight:!1,pagination:system.config.pagination,rownumbers:!0,remoteSort:!1,checkOnSelect:!1,pageSize:system.config.pageSize,pageList:system.config.pageList,idField:"id",fit:!0,striped:!0,sortName:system.userConfig.torrentList.sortName,sortOrder:system.userConfig.torrentList.sortOrder,drophead:!0,columns:[fields],onCheck:function(rowIndex,rowData){system.checkTorrentRow(rowIndex,rowData)},onUncheck:function(rowIndex,rowData){system.checkTorrentRow(rowIndex,rowData)},onCheckAll:function(rows){system.checkTorrentRow("all",!1)},onUncheckAll:function(rows){system.checkTorrentRow("all",!0)},onSelect:function(rowIndex,rowData){-1!=selectedIndex&&system.control.torrentlist.datagrid("unselectRow",selectedIndex),system.getTorrentInfos(rowData.id),selectedIndex=rowIndex},onUnselect:function(rowIndex,rowData){system.currentTorrentId=0,selectedIndex=-1},onBeforeLoad:function(param){system.currentTorrentId=0},onSortColumn:function(field,order){var field_func=field,datas=system.control.torrentlist.datagrid("getData").originalRows.sort(arrayObjectSort(field_func,order));system.control.torrentlist.datagrid("loadData",datas),system.resetTorrentListFieldsUserConfig(system.control.torrentlist.datagrid("options").columns[0]),system.userConfig.torrentList.sortName=field,system.userConfig.torrentList.sortOrder=order,system.saveUserConfig()},onRowContextMenu:function(e,rowIndex,rowData){system.config.simpleCheckMode&&system.control.torrentlist.datagrid("uncheckAll"),0==system.checkedRows.length&&system.control.torrentlist.datagrid("checkRow",rowIndex),e.preventDefault(),system.showContextMenu("torrent-list",e)},onHeadDrop:function(sourceField,targetField){system.resetTorrentListFieldsUserConfig(system.control.torrentlist.datagrid("options").columns[0]),system.saveUserConfig()},onResizeColumn:function(field,width){system.resetTorrentListFieldsUserConfig(system.control.torrentlist.datagrid("options").columns[0]),system.saveUserConfig()},onHeaderContextMenu:function(e,field){e.preventDefault(),headContextMenu||createHeadContextMenu(),headContextMenu.menu("show",{left:e.pageX,top:e.pageY})}})}),"json"),this.control.torrentlist.refresh=function(){system.control.torrentlist.datagrid("getPager").find(".pagination-load").click()}},resetTorrentListFieldsUserConfig:function(columns){var fields={};$.each(this.userConfig.torrentList.fields,(function(index,item){fields[item.field]=item})),this.userConfig.torrentList.fields=[],$.each(columns,(function(index,item){var field=$.extend({},fields[item.field]);field.width=item.width,field.hidden=item.hidden,system.userConfig.torrentList.fields.push(field)}))},showContextMenu:function(type,e){var parent=this.contextMenus[type];parent?parent.empty():(parent=$("<div/>").attr("class","easyui-menu").css({"min-width":"180px"}).appendTo(this.panel.main),this.contextMenus[type]=parent,parent.menu());var menus=null;switch(type){case"torrent-list":menus=new Array("start","pause","-","rename","remove","recheck","-","morepeers","changeDownloadDir","copyPath","-","menu-queue-move-top","menu-queue-move-up","menu-queue-move-down","menu-queue-move-bottom","magnetLink"),this.config.nav.labels&&(menus.push("-"),menus.push("setLabels"));var toolbar=this.panel.toolbar;for(var item in menus){var key=menus[item];if("-"==key)$("<div class='menu-sep'></div>").appendTo(parent);else{var menu=toolbar.find("#toolbar_"+key);menu.length>0?parent.menu("appendItem",{text:menu.attr("title"),id:key,iconCls:menu.linkbutton("options").iconCls,disabled:menu.linkbutton("options").disabled,onclick:function(){system.panel.toolbar.find("#toolbar_"+$(this).attr("id")).click()}}):(menu=$("#"+key)).length>0?parent.menu("appendItem",{text:menu.attr("title"),id:key,iconCls:menu.attr("id").replace("menu-queue-move","iconfont tr-icon"),disabled:toolbar.find("#toolbar_queue").linkbutton("options").disabled,onclick:function(){$("#"+$(this).attr("id")).click()}}):(menu=this.getContentMenuWithKey(key,parent))&&parent.menu("appendItem",menu),menu=null}}var btn=$("#copyPath",parent);btn.attr({"data-clipboard-action":"copy","data-clipboard-target":"#clipboard-source"});var clipboard=new ClipboardJS(btn.get(0))}parent.menu("show",{left:e.pageX,top:e.pageY,hideOnUnhover:!1}),parent=null,menus=null},getContentMenuWithKey:function(key,parent){switch(key){case"setLabels":return{id:"setLabels",text:system.lang.menus.setLabels,iconCls:"iconfont tr-icon-labels",disabled:0==this.checkedRows.length,onclick:function(){var rows=system.checkedRows,values=new Array;for(var i in rows)values.push(rows[i].hashString);0!=values.length&&system.openDialogFromTemplate({id:"dialog-torrent-setLabels",options:{title:system.lang.dialog["torrent-setLabels"].title,width:520,height:200},datas:{hashs:values}})}};case"magnetLink":return{id:"magnetLink",text:system.lang.menus.copyMagnetLink,iconCls:"iconfont tr-icon-labels",disabled:0==this.checkedRows.length,onclick:function(){system.getTorrentMagnetLink((function(data){system.copyToClipboard(data),parent.css("display","block")}))}}}},formetTorrentLabels:function(ids,hashString){var box=$("<div style='position: relative;'/>");if(ids){"string"==typeof ids&&(ids=ids.split(","));for(var i=0;i<ids.length;i++){var index=ids[i],item=this.config.labels[index];item&&$("<span class='user-label'/>").html(item.name).css({"background-color":item.color,color:getGrayLevel(item.color)>.5?"#000":"#fff"}).appendTo(box)}}var button=$("<button onclick='javascript:system.setTorrentLabels(this,\""+hashString+'");\' data-options="iconCls:\'iconfont tr-icon-labels\',plain:true" class="easyui-linkbutton user-label-set"/>').appendTo(box);return button.linkbutton(),button.find("span").first().attr({title:system.lang.dialog["torrent-setLabels"].title}),box.get(0).outerHTML},setTorrentLabels:function(button,hashString){system.openDialogFromTemplate({id:"dialog-torrent-setLabels",options:{title:system.lang.dialog["torrent-setLabels"].title,width:520,height:200},datas:{hashs:[hashString]},type:1,source:$(button)})},checkTorrentRow:function(rowIndex,rowData){if(this.checkedRows=this.control.torrentlist.datagrid("getChecked"),this.showCheckedInStatus(),"all"==rowIndex){if(0==this.control.torrentlist.datagrid("getRows").length)return;return $("#toolbar_start, #toolbar_pause, #toolbar_remove, #toolbar_recheck, #toolbar_changeDownloadDir,#toolbar_morepeers,#toolbar_copyPath",this.panel.toolbar).linkbutton({disabled:rowData}),$("#toolbar_rename, #toolbar_morepeers",this.panel.toolbar).linkbutton({disabled:!0}),void this.panel.toolbar.find("#toolbar_queue").menubutton("disable")}if(0==this.checkedRows.length)return $("#toolbar_start, #toolbar_pause, #toolbar_rename, #toolbar_remove, #toolbar_recheck, #toolbar_changeDownloadDir,#toolbar_morepeers,#toolbar_copyPath",this.panel.toolbar).linkbutton({disabled:!0}),void this.panel.toolbar.find("#toolbar_queue").menubutton("disable");var torrent;if(1==this.checkedRows.length)switch($("#toolbar_remove, #toolbar_rename, #toolbar_changeDownloadDir,#toolbar_copyPath",this.panel.toolbar).linkbutton({disabled:!1}),this.panel.toolbar.find("#toolbar_queue").menubutton("enable"),transmission.torrents.all[rowData.id].status){case transmission._status.stopped:this.panel.toolbar.find("#toolbar_start, #toolbar_recheck").linkbutton({disabled:!1}),this.panel.toolbar.find("#toolbar_pause, #toolbar_morepeers").linkbutton({disabled:!0});break;case transmission._status.check:case transmission._status.checkwait:this.panel.toolbar.find("#toolbar_start, #toolbar_pause, #toolbar_recheck, #toolbar_morepeers").linkbutton({disabled:!0});break;default:this.panel.toolbar.find("#toolbar_start, #toolbar_recheck").linkbutton({disabled:!0}),this.panel.toolbar.find("#toolbar_pause, #toolbar_morepeers").linkbutton({disabled:!1})}else $("#toolbar_start, #toolbar_pause, #toolbar_remove, #toolbar_recheck, #toolbar_changeDownloadDir,#toolbar_copyPath",this.panel.toolbar).linkbutton({disabled:!1}),$("#toolbar_rename, #toolbar_morepeers",this.panel.toolbar).linkbutton({disabled:!0}),this.panel.toolbar.find("#toolbar_queue").menubutton("disable")},showCheckedInStatus:function(){if(this.checkedRows.length>0){this.panel.status_text.empty(),this.showStatus(void 0,0);var items=[],text=this.lang.system.status.checked.replace("%n",this.checkedRows.length),paths=[];$("<div style='padding: 5px;'/>").html(text).appendTo(this.panel.status_text);for(var index=0;index<this.checkedRows.length;index++){var item=this.checkedRows[index];items.push({value:index,text:index+1+". "+item.name}),-1===$.inArray(item.downloadDir,paths)&&paths.push(item.downloadDir)}$("<div/>").appendTo(this.panel.status_text).datalist({data:items}),$(".datalist>.panel-body",this.panel.status_text).css({border:0}),$("#button-cancel-checked").show(),$("#clipboard-source").val(paths.join("\n"))}else $("#button-cancel-checked").hide(),this.panel.status_text.empty(),$("#clipboard-source").val("")},copyToClipboard:function(text){var id="copy_to_clipboard_textarea",aux=document.getElementById(id);aux||(aux=document.createElement("textarea")),aux.id=id,aux.style.display="block",aux.value=text,document.body.appendChild(aux),aux.select(),document.execCommand("copy"),aux.style.display="none"},initToolbar:function(){this.panel.toolbar.find("#toolbar_label_reload_time").html(this.lang.toolbar["reload-time"]),this.panel.toolbar.find("#toolbar_label_reload_time_unit").html(this.lang.toolbar["reload-time-unit"]),this.panel.toolbar.find("#toolbar_reload_time").numberspinner({value:this.config.reloadStep/1e3,min:3,disabled:!this.config.autoReload,onChange:function(){var value=this.value;$.isNumeric(value)&&(system.config.reloadStep=1e3*value,system.saveConfig())}}),this.panel.toolbar.find("#toolbar_autoreload").linkbutton({text:this.config.autoReload?this.lang.toolbar["autoreload-enabled"]:this.lang.toolbar["autoreload-disabled"],iconCls:this.config.autoReload?"icon-enabled":"icon-disabled"}).attr("title",this.config.autoReload?this.lang.toolbar.tip["autoreload-disabled"]:this.lang.toolbar.tip["autoreload-enabled"]).click((function(){system.config.autoReload?(system.config.autoReload=!1,clearTimeout(system.autoReloadTimer),system.panel.toolbar.find("#toolbar_reload_time").numberspinner("disable")):(system.config.autoReload=!0,system.reloadData(),system.panel.toolbar.find("#toolbar_reload_time").numberspinner("enable")),system.saveConfig(),$(this).linkbutton({text:system.config.autoReload?system.lang.toolbar["autoreload-enabled"]:system.lang.toolbar["autoreload-disabled"],iconCls:system.config.autoReload?"icon-enabled":"icon-disabled"}).attr("title",system.config.autoReload?system.lang.toolbar.tip["autoreload-disabled"]:system.lang.toolbar.tip["autoreload-enabled"])})),this.panel.toolbar.find("#toolbar_add_torrents").linkbutton({text:this.lang.toolbar["add-torrent"],disabled:!1}).attr("title",this.lang.toolbar.tip["add-torrent"]).click((function(){system.openDialogFromTemplate({id:"dialog-torrent-add",options:{title:system.lang.toolbar["add-torrent"],width:620,height:system.config.nav.labels?600:400,resizable:!0}})})),this.panel.toolbar.find("#toolbar_start_all").linkbutton({disabled:!1}).attr("title",this.lang.toolbar.tip["start-all"]).click((function(){var button=$(this),icon=button.linkbutton("options").iconCls;button.linkbutton({disabled:!0,iconCls:"icon-loading"}),transmission.exec({method:"torrent-start"},(function(data){button.linkbutton({iconCls:icon,disabled:!1}),button=null}))})),this.panel.toolbar.find("#toolbar_pause_all").linkbutton({disabled:!1}).attr("title",this.lang.toolbar.tip["pause-all"]).click((function(){var button=$(this),icon=button.linkbutton("options").iconCls;button.linkbutton({disabled:!0,iconCls:"icon-loading"}),transmission.exec({method:"torrent-stop"},(function(data){button.linkbutton({iconCls:icon,disabled:!1}),button=null}))})),this.panel.toolbar.find("#toolbar_start").linkbutton({disabled:!0}).attr("title",this.lang.toolbar.tip.start).click((function(){system.changeSelectedTorrentStatus("start",$(this))})),this.panel.toolbar.find("#toolbar_pause").linkbutton({disabled:!0}).attr("title",this.lang.toolbar.tip.pause).click((function(){system.changeSelectedTorrentStatus("stop",$(this))})),this.panel.toolbar.find("#toolbar_recheck").linkbutton({disabled:!0}).attr("title",this.lang.toolbar.tip.recheck).click((function(){var rows=system.control.torrentlist.datagrid("getChecked"),torrent;rows.length>0&&(1==rows.length?transmission.torrents.all[rows[0].id].percentDone>0?confirm(system.lang.toolbar.tip["recheck-confirm"])&&system.changeSelectedTorrentStatus("verify",$(this)):system.changeSelectedTorrentStatus("verify",$(this)):confirm(system.lang.toolbar.tip["recheck-confirm"])&&system.changeSelectedTorrentStatus("verify",$(this)))})),this.panel.toolbar.find("#toolbar_morepeers").linkbutton({disabled:!0}).click((function(){system.changeSelectedTorrentStatus("reannounce",$(this))})),this.panel.toolbar.find("#toolbar_remove").linkbutton({disabled:!0}).attr("title",this.lang.toolbar.tip.remove).click((function(){var rows=system.control.torrentlist.datagrid("getChecked"),ids=new Array;for(var i in rows)ids.push(rows[i].id);0!=ids.length&&system.openDialogFromTemplate({id:"dialog-torrent-remove-confirm",options:{title:system.lang.dialog["torrent-remove"].title,width:350,height:150},datas:{ids:ids}})})),this.panel.toolbar.find("#toolbar_rename").linkbutton({disabled:!0}).click((function(){var rows=system.control.torrentlist.datagrid("getChecked");0!=rows.length&&system.openDialogFromTemplate({id:"dialog-torrent-rename",options:{title:system.lang.dialog["torrent-rename"].title,width:520,height:200,resizable:!0},datas:{id:rows[0].id}})})),this.panel.toolbar.find("#toolbar_changeDownloadDir").linkbutton({disabled:!0}).attr("title",this.lang.toolbar.tip["change-download-dir"]).click((function(){var rows=system.control.torrentlist.datagrid("getChecked"),ids=new Array;for(var i in rows)ids.push(rows[i].id);0!=ids.length&&system.openDialogFromTemplate({id:"dialog-torrent-changeDownloadDir",options:{title:system.lang.dialog["torrent-changeDownloadDir"].title,width:520,height:200},datas:{ids:ids}})})),this.panel.toolbar.find("#toolbar_alt_speed").linkbutton().attr("title",this.lang.toolbar.tip["alt-speed"]).click((function(){var button=$(this),options=button.linkbutton("options"),enabled=!1;"iconfont tr-icon-rocket"==options.iconCls&&(enabled=!0),transmission.exec({method:"session-set",arguments:{"alt-speed-enabled":enabled}},(function(data){"success"==data.result&&(system.serverConfig["alt-speed-enabled"]=enabled,button.linkbutton({iconCls:"iconfont tr-icon-"+(enabled?"woniu":"rocket")}),enabled?$("#status_alt_speed").show():$("#status_alt_speed").hide())})),button.linkbutton({iconCls:"icon-loading"})})),this.panel.toolbar.find("#toolbar_config").linkbutton().attr("title",this.lang.toolbar.tip["system-config"]).click((function(){system.openDialogFromTemplate({id:"dialog-system-config",options:{title:system.lang.toolbar["system-config"],width:680,height:500,resizable:!0}})})),this.panel.toolbar.find("#toolbar_reload").linkbutton().attr("title",this.lang.toolbar.tip["system-reload"]).click((function(){system.reloadData()})),this.panel.toolbar.find("#toolbar_search").searchbox({searcher:function(value){system.searchTorrents(value)},prompt:this.lang.toolbar["search-prompt"]}),this.panel.toolbar.find("#toolbar_copyPath").linkbutton().attr("title",this.lang.toolbar.tip["copy-path-to-clipboard"])},initStatusBar:function(){this.panel.statusbar.find("#status_title_downloadspeed").html(this.lang.statusbar.downloadspeed),this.panel.statusbar.find("#status_title_uploadspeed").html(this.lang.statusbar.uploadspeed)},connect:function(){this.showStatus(this.lang.system.status.connect,0),transmission.on.torrentCountChange=function(){system.reloadTorrentBaseInfos()},transmission.on.postError=function(){},transmission.init({islocal:!0},(function(){system.reloadSession(!0),system.getServerStatus()}))},reloadSession:function(isinit){transmission.getSession((function(result){system.serverConfig=result,$("#status_version").html("Transmission "+system.lang.statusbar.version+result.version+", RPC: "+result["rpc-version"]+", WEB Control: "+system.version+"("+system.codeupdate+")"),1==result["alt-speed-enabled"]?(system.panel.toolbar.find("#toolbar_alt_speed").linkbutton({iconCls:"iconfont tr-icon-woniu"}),$("#status_alt_speed").show()):(system.panel.toolbar.find("#toolbar_alt_speed").linkbutton({iconCls:"iconfont tr-icon-rocket"}),$("#status_alt_speed").hide()),system.downloadDir=result["download-dir"],0==transmission.downloadDirs.length&&transmission.downloadDirs.push(system.downloadDir),parseInt(system.serverConfig["rpc-version"])>=15?transmission.getFreeSpace(system.downloadDir,(function(datas){system.serverConfig["download-dir-free-space"]=datas.arguments["size-bytes"],system.showFreeSpace(datas.arguments["size-bytes"])})):system.showFreeSpace(system.serverConfig["download-dir-free-space"]),isinit&&system.showStatus(system.lang.system.status.connected)}))},showFreeSpace:function(size){var tmp=size;tmp=-1==tmp?system.lang.public["text-unknown"]:formatSize(tmp),$("#status_freespace").text(system.lang.dialog["system-config"]["download-dir-free-space"]+" "+tmp)},reloadTorrentBaseInfos:function(ids,moreFields){if(!this.reloading){clearTimeout(this.autoReloadTimer),this.reloading=!0;var oldInfos={trackers:transmission.trackers,folders:transmission.torrents.folders};transmission.torrents.getallids((function(resultTorrents){var ignore=new Array;for(var index in resultTorrents){var item=resultTorrents[index];ignore.push(item.id)}var errorIds=transmission.torrents.getErrorIds(ignore,!0);errorIds.length>0?transmission.torrents.getallids((function(){system.resetTorrentInfos(oldInfos)}),errorIds):system.resetTorrentInfos(oldInfos)}),ids,moreFields)}},resetTorrentInfos:function(oldInfos){this.resetNavTorrentStatus(),this.resetNavServers(oldInfos),this.resetNavStatistics(),this.resetNavFolders(oldInfos),this.resetNavLabels(),"Firefox"==$.ua.browser.name&&$.ua.browser.major<60&&system.panel.left.find("span.nav-total-size").css({"margin-top":"-19px"})},resetNavTorrentStatus:function(){var currentTorrentId=this.currentTorrentId,node;if(transmission.torrents.status[transmission._status.stopped]?system.updateTreeNodeText("paused",system.lang.tree.paused+this.showNodeMoreInfos(transmission.torrents.status[transmission._status.stopped].length)):system.updateTreeNodeText("paused",system.lang.tree.paused),transmission.torrents.status[transmission._status.seed]?system.updateTreeNodeText("sending",system.lang.tree.sending+this.showNodeMoreInfos(transmission.torrents.status[transmission._status.seed].length)):system.updateTreeNodeText("sending",system.lang.tree.sending),transmission.torrents.status[transmission._status.seedwait]){var node=system.panel.left.tree("find","sending"),childs=system.panel.left.tree("getChildren",node.target),text=system.lang.tree.wait+this.showNodeMoreInfos(transmission.torrents.status[transmission._status.seedwait].length);childs.length>0?system.updateTreeNodeText(childs[0].id,text):system.appendTreeNode(node,[{id:"seedwait",text:text,iconCls:"iconfont tr-icon-wait"}])}else system.removeTreeNode("seedwait");if(transmission.torrents.status[transmission._status.check]?system.updateTreeNodeText("check",system.lang.tree.check+this.showNodeMoreInfos(transmission.torrents.status[transmission._status.check].length)):system.updateTreeNodeText("check",system.lang.tree.check),transmission.torrents.status[transmission._status.checkwait]){var node=system.panel.left.tree("find","check"),childs=system.panel.left.tree("getChildren",node.target),text=system.lang.tree.wait+this.showNodeMoreInfos(transmission.torrents.status[transmission._status.checkwait].length);childs.length>0?system.updateTreeNodeText(childs[0].id,text):system.appendTreeNode(node,[{id:"checkwait",text:text,iconCls:"iconfont tr-icon-wait"}])}else system.removeTreeNode("checkwait");if(transmission.torrents.status[transmission._status.download]?system.updateTreeNodeText("downloading",system.lang.tree.downloading+this.showNodeMoreInfos(transmission.torrents.status[transmission._status.download].length)):system.updateTreeNodeText("downloading",system.lang.tree.downloading),transmission.torrents.status[transmission._status.downloadwait]){var node=system.panel.left.tree("find","downloading"),childs=system.panel.left.tree("getChildren",node.target),text=system.lang.tree.wait+this.showNodeMoreInfos(transmission.torrents.status[transmission._status.downloadwait].length);childs.length>0?system.updateTreeNodeText(childs[0].id,text):system.appendTreeNode(node,[{id:"downloadwait",text:text,iconCls:"iconfont tr-icon-wait"}])}else system.removeTreeNode("downloadwait");if(system.updateTreeNodeText("actively",system.lang.tree.actively+this.showNodeMoreInfos(transmission.torrents.actively.length)),system.updateTreeNodeText("error",system.lang.tree.error+this.showNodeMoreInfos(transmission.torrents.error.length)),system.updateTreeNodeText("warning",system.lang.tree.warning+this.showNodeMoreInfos(transmission.torrents.warning.length)),null!=(node=system.panel.left.tree("getSelected"))){var p=system.control.torrentlist.datagrid("options").pageNumber;system.loadTorrentToList({node:node,page:p})}0!=currentTorrentId&&system.control.torrentlist.datagrid("selectRecord",currentTorrentId),system.reloading=!1,system.config.autoReload&&(system.autoReloadTimer=setTimeout((function(){system.reloadData()}),system.config.reloadStep)),system.updateTreeNodeText("torrent-all",system.lang.tree.all+this.showNodeMoreInfos(transmission.torrents.count,transmission.torrents.totalSize))},resetNavServers:function(oldInfos){var serversNode=this.panel.left.tree("find","servers");if(this.config.nav.servers){if(serversNode){var serversNode_collapsed=serversNode.state;this.removeTreeNode("servers-loading")}else this.appendTreeNode(null,[{id:"servers",text:this.lang.tree.servers,state:"closed",iconCls:"iconfont tr-icon-servers"}]),serversNode=this.panel.left.tree("find","servers");var datas=new Array,BTServersNode=this.panel.left.tree("find","btservers"),BTServersNodeState=BTServersNode?BTServersNode.state:"close";for(var index in!BTServersNode&&system.config.showBTServers&&(this.appendTreeNode(serversNode,[{id:"btservers",text:"BT",state:"open",iconCls:"iconfont tr-icon-bt"}]),BTServersNode=this.panel.left.tree("find","btservers")),transmission.trackers){var tracker;if(!(tracker=transmission.trackers[index]).isBT||system.config.showBTServers){var node=system.panel.left.tree("find",tracker.nodeid),text=tracker.name+this.showNodeMoreInfos(tracker.count,tracker.size);node?system.updateTreeNodeText(tracker.nodeid,text,tracker.connected?"iconfont tr-icon-server":"iconfont tr-icon-server-error"):system.appendTreeNode(tracker.isBT?BTServersNode:serversNode,[{id:tracker.nodeid,text:text,iconCls:tracker.connected?"iconfont tr-icon-server":"iconfont tr-icon-server-error"}]),oldInfos.trackers[tracker.nodeid]=null}}for(var index in"closed"==serversNode_collapsed&&this.panel.left.tree("collapse",serversNode.target),system.config.showBTServers&&BTServersNode&&"closed"==BTServersNodeState&&this.panel.left.tree("collapse",BTServersNode.target),oldInfos.trackers){var tracker;(tracker=oldInfos.trackers[index])&&system.removeTreeNode(tracker.nodeid)}}else serversNode&&this.panel.left.tree("remove",serversNode.target)},resetNavStatistics:function(){if(this.config.nav.statistics){var items="uploadedBytes,downloadedBytes,filesAdded,sessionCount,secondsActive".split(",");$.each(items,(function(key,item){switch(item){case"uploadedBytes":case"downloadedBytes":system.updateTreeNodeText(item,system.lang.tree.statistics[item]+" "+formatSize(system.serverSessionStats["cumulative-stats"][item])),system.updateTreeNodeText("current-"+item,system.lang.tree.statistics[item]+" "+formatSize(system.serverSessionStats["current-stats"][item]));break;case"secondsActive":system.updateTreeNodeText(item,system.lang.tree.statistics[item]+" "+getTotalTime(1e3*system.serverSessionStats["cumulative-stats"][item])),system.updateTreeNodeText("current-"+item,system.lang.tree.statistics[item]+" "+getTotalTime(1e3*system.serverSessionStats["current-stats"][item]));break;default:system.updateTreeNodeText(item,system.lang.tree.statistics[item]+" "+system.serverSessionStats["cumulative-stats"][item]),system.updateTreeNodeText("current-"+item,system.lang.tree.statistics[item]+" "+system.serverSessionStats["current-stats"][item])}}))}else{var node=this.panel.left.tree("find","statistics");node&&this.panel.left.tree("remove",node.target)}},resetNavFolders:function(oldInfos){if(this.config.nav.folders){for(var index in transmission.torrents.folders){var item=transmission.torrents.folders[index];oldInfos.folders[item.nodeid]=null}this.loadFolderList(oldInfos.folders)}else{this.initUIStatus();var node=this.panel.left.tree("find","folders");node&&this.panel.left.tree("remove",node.target)}},resetNavLabels:function(clear){if(this.config.nav.labels){if(clear)for(var items=this.panel.left.tree("getChildren",this.panel.left.tree("find","labels").target),index=0;index<items.length;index++)this.panel.left.tree("remove",items[index].target);for(var prefix="label-",index=0;index<this.config.labels.length;index++){var item=this.config.labels[index],key=prefix+this.getValidTreeKey(item.name),node;(node=this.panel.left.tree("find",key))||(this.appendTreeNode("labels",[{id:key,text:item.name,labelIndex:index,iconCls:"iconfont tr-icon-label"}]),node=this.panel.left.tree("find",key),$(".tree-icon",node.target).css({color:item.color}),$(".tree-title",node.target).addClass("user-label").css({"background-color":item.color,color:getGrayLevel(item.color)>.5?"#000":"#fff"}))}}else{var node;(node=this.panel.left.tree("find","labels"))&&this.panel.left.tree("remove",node.target)}},showNodeMoreInfos:function(count,size){var result="";return count>0&&(result=" <span class='nav-torrents-number'>("+count+")</span>"),size>0&&(result+="<span class='nav-total-size'>["+formatSize(size)+"]</span>"),result},getServerStatus:function(){this.reloading||(clearTimeout(this.autoReloadTimer),this.reloading=!0,transmission.getStatus((function(data){if(system.reloading=!1,$("#status_downloadspeed").html(formatSize(data.downloadSpeed,!1,"speed")),$("#status_uploadspeed").html(formatSize(data.uploadSpeed,!1,"speed")),system.serverSessionStats=data,0==data.torrentCount){var serversNode=system.panel.left.tree("find","servers");serversNode&&system.panel.left.tree("remove",serversNode.target),system.updateTreeNodeText("torrent-all",system.lang.tree.all)}})))},showStatus:function(msg,outtime){$("#m_status").panel("options").collapsed&&$("#layout_left").layout("expand","south"),this.panel.status_text.show(),msg&&this.panel.status_text.html(msg),0!=outtime&&(null==outtime&&(outtime=3e3),this.panel.status_text.fadeOut(outtime,(function(){$("#layout_left").layout("collapse","south")})))},updateTreeNodeText:function(id,text,iconCls){var node=this.panel.left.tree("find",id);if(node){var data={target:node.target,text:text};null!=iconCls&&(data.iconCls=iconCls),this.panel.left.tree("update",data)}node=null},appendTreeNode:function(parentid,data){var parent=null;(parent="string"==typeof parentid?this.panel.left.tree("find",parentid):parentid)?this.panel.left.tree("append",{parent:parent.target,data:data}):this.panel.left.tree("append",{data:data}),parent=null},removeTreeNode:function(id){var node=this.panel.left.tree("find",id);node&&this.panel.left.tree("remove",node.target),node=null},loadTorrentToList:function(config){if(transmission.torrents.all){var def={node:null,page:1};if(jQuery.extend(def,config),config.node){var torrents=null,parent=this.panel.left.tree("getParent",config.node.target)||{id:""},currentNodeId=this.panel.left.data("currentNodeId");switch(currentNodeId!=config.node.id&&(this.control.torrentlist.datagrid("uncheckAll"),this.control.torrentlist.datagrid({pageNumber:1}),currentNodeId=config.node.id),this.panel.left.data("currentNodeId",currentNodeId),parent.id){case"servers":case"btservers":torrents="btservers"==config.node.id?transmission.torrents.btItems:transmission.trackers[config.node.id].torrents;break;default:switch(config.node.id){case"torrent-all":case"servers":torrents=transmission.torrents.all;break;case"paused":torrents=transmission.torrents.status[transmission._status.stopped];break;case"sending":torrents=transmission.torrents.status[transmission._status.seed];break;case"seedwait":torrents=transmission.torrents.status[transmission._status.seedwait];break;case"check":torrents=transmission.torrents.status[transmission._status.check];break;case"checkwait":torrents=transmission.torrents.status[transmission._status.checkwait];break;case"downloading":torrents=transmission.torrents.status[transmission._status.download];break;case"downloadwait":torrents=transmission.torrents.status[transmission._status.downloadwait];break;case"actively":torrents=transmission.torrents.actively;break;case"error":torrents=transmission.torrents.error;break;case"warning":torrents=transmission.torrents.warning;break;case"search-result":torrents=transmission.torrents.searchResult;break;case"btservers":torrents=transmission.torrents.btItems;break;default:if(-1!=config.node.id.indexOf("folders-")){var folder=transmission.torrents.folders[config.node.id];if(folder)if(this.config.hideSubfolders){torrents=[];for(var index=0;index<folder.torrents.length;index++){var element=folder.torrents[index];element.downloadDir.replace(/[\\|\/]/g,"")==config.node.path&&torrents.push(element)}}else torrents=folder.torrents}else if(-1!=config.node.id.indexOf("label-")){var labelIndex=parseInt(config.node.labelIndex);for(var key in torrents=[],transmission.torrents.all){var item=transmission.torrents.all[key],labels;(labels=this.config.labelMaps[item.hashString])&&-1!=$.inArray(labelIndex,labels)&&torrents.push(item)}}}}this.config.defaultSelectNode!=config.node.id&&(this.control.torrentlist.datagrid("loadData",[]),this.config.defaultSelectNode=config.node.id,this.saveConfig());var datas=new Array;for(var index in torrents){if(!torrents[index])return;var status=this.lang.torrent["status-text"][torrents[index].status];0!=torrents[index].error?status="<span class='text-status-error'>"+status+"</span>":torrents[index].warning&&(status="<span class='text-status-warning' title='"+torrents[index].warning+"'>"+status+"</span>");var data={},labels;(data=$.extend(data,torrents[index])).status=status,data.statusCode=torrents[index].status,data.completeSize=Math.max(0,torrents[index].totalSize-torrents[index].leftUntilDone),data.leecherCount=torrents[index].leecher,data.seederCount=torrents[index].seeder,(labels=this.config.labelMaps[data.hashString])&&(data.labels=labels),datas.push(data)}this.updateTorrentCurrentPageDatas(datas),this.initShiftCheck()}}},initShiftCheck:function(){var items=$("#m_list div.datagrid-cell-check input:checkbox"),eventName="click.Shift";items.off(eventName);var lastChecked=null,torrentlist=this.control.torrentlist;items.on(eventName,(function(e){if(lastChecked){if(e.shiftKey)for(var start=items.index(this),end=items.index(lastChecked),checked=lastChecked.checked,startIndex=Math.min(start,end),endIndex=Math.max(start,end)+1,index=startIndex;index<endIndex;index++)checked?torrentlist.datagrid("checkRow",index):torrentlist.datagrid("uncheckRow",index);lastChecked=this}else lastChecked=this}))},updateTorrentCurrentPageDatas:function(currentTypeDatas){var rows=this.control.torrentlist.datagrid("getRows");if(0==currentTypeDatas.length&&rows.length>0)this.control.torrentlist.datagrid("loadData",[]);else{var _options=this.control.torrentlist.datagrid("options"),orderField=null;if(_options.sortName){var orderField_func=orderField=_options.sortName;currentTypeDatas=currentTypeDatas.sort(arrayObjectSort(orderField_func,_options.sortOrder))}if(0==rows.length||currentTypeDatas.length!=this.control.torrentlist.datagrid("getData").total&&currentTypeDatas.length>_options.pageSize)this.control.torrentlist.datagrid({loadFilter:pagerFilter,pageNumber:_options.pageNumber,sortName:orderField,sortOrder:_options.sortOrder}).datagrid("loadData",currentTypeDatas);else{this.control.torrentlist.datagrid("getData").originalRows=currentTypeDatas;var start=(_options.pageNumber-1)*parseInt(_options.pageSize),end=start+parseInt(_options.pageSize);currentTypeDatas=currentTypeDatas.slice(start,end);var recently={},datas={};for(var index in transmission.torrents.recently){var item;recently[(item=transmission.torrents.recently[index]).id]=!0,item=null}for(var index in currentTypeDatas){var item;datas[(item=currentTypeDatas[index]).id]=item,item=null}for(var addedDatas={},index=rows.length-1;index>=0;index--){var item,data=datas[(item=rows[index]).id];data?recently[item.id]?(this.control.torrentlist.datagrid("updateRow",{index:index,row:data}),addedDatas[item.id]=item):transmission.torrents.removed&&transmission.torrents.removed.length>0&&-1!=$.inArray(item.id,transmission.torrents.removed)?this.control.torrentlist.datagrid("deleteRow",index):addedDatas[item.id]=item:this.control.torrentlist.datagrid("deleteRow",index),item=null,data=null}for(var index in currentTypeDatas){var item;addedDatas[(item=currentTypeDatas[index]).id]||this.control.torrentlist.datagrid("appendRow",item)}rows=null,recently=null,datas=null}}},getTorrentNameBar:function(torrent){var className="",tip=torrent.name;switch(torrent.status){case transmission._status.stopped:className="iconlabel icon-pause-small";break;case transmission._status.check:className="iconlabel icon-checking";break;case transmission._status.download:className="iconlabel icon-down";break;case transmission._status.seed:className="iconlabel icon-up";break;case transmission._status.seedwait:case transmission._status.downloadwait:case transmission._status.checkwait:className="iconlabel icon-wait"}return tip+="\n"+torrent.downloadDir,torrent.warning&&(className="iconlabel icon-warning-type1",tip+="\n\n"+this.lang.public["text-info"]+": "+torrent.warning),0!=torrent.error&&(className="iconlabel icon-exclamation",tip+="\n\n"+this.lang.public["text-info"]+": "+torrent.errorString),'<span class="'+className+'" title="'+tip+'">'+torrent.name+"</span>"},getTorrentProgressBar:function(progress,torrent){var className="",status=0,percentCheckText,percentCheckView;switch(status="object"==typeof torrent?torrent.status:torrent){case transmission._status.stopped:className="torrent-progress-stop";break;case transmission._status.checkwait:case transmission._status.check:className="torrent-progress-check";break;case transmission._status.downloadwait:case transmission._status.download:className="torrent-progress-download";break;case transmission._status.seedwait:case transmission._status.seed:className="torrent-progress-seed"}return"object"==typeof torrent&&(torrent.warning&&(className="torrent-progress-warning"),0!=torrent.error&&(className="torrent-progress-error")),status==transmission._status.check?'<div class="torrent-progress" title="'+progress+'%"><div class="torrent-progress-text" style="z-index:2;">'+parseFloat(100*torrent.recheckProgress).toFixed(2)+'%</div><div class="torrent-progress-bar torrent-progress-seed" style="width:'+parseFloat(progress*torrent.recheckProgress).toFixed(2)+'%;z-index:1;opacity:0.7;"></div><div class="torrent-progress-bar '+className+'" style="width:'+progress+'%;"></div></div>':'<div class="torrent-progress" title="'+(progress+="%")+'"><div class="torrent-progress-text">'+progress+'</div><div class="torrent-progress-bar '+className+'" style="width:'+progress+';"></div></div>'},addTorrentsToServer:function(urls,count,autostart,savepath,labels){var index=count-urls.length,url=urls.shift();if(!url)return this.showStatus(this.lang.system.status.queuefinish),this.getServerStatus(),void(null!=labels&&system.saveConfig());this.showStatus(this.lang.system.status.queue+(index+1)+"/"+count+"<br/>"+url,0),transmission.addTorrentFromUrl(url,savepath,autostart,(function(data){system.addTorrentsToServer(urls,count,autostart,savepath,labels),null!=labels&&null!=data.hashString&&system.saveLabelsConfig(data.hashString,labels)}))},changeSelectedTorrentStatus:function(status,button,method){var rows=this.control.torrentlist.datagrid("getChecked"),ids=new Array;for(var i in status||(status="start"),rows)ids.push(rows[i].id);if(method||(method="torrent-"+status),ids.length>0){if(button){var icon=button.linkbutton("options").iconCls;button.linkbutton({disabled:!0,iconCls:"icon-loading"})}transmission.exec({method:method,arguments:{ids:ids}},(function(data){button&&button.linkbutton({iconCls:icon}),system.control.torrentlist.datagrid("uncheckAll"),system.reloadTorrentBaseInfos()}))}},getTorrentMagnetLink:function(callback){var rows=this.control.torrentlist.datagrid("getChecked"),ids=new Array;for(var i in rows)ids.push(rows[i].id);transmission.torrents.getMagnetLink(ids,callback)},searchTorrents:function(key){if(""!=key){var result=transmission.torrents.search(key);if(null!=result&&0!=result.length){var node=this.panel.left.tree("find","search-result"),text=this.lang.tree["search-result"]+" : "+key+" ("+result.length+")";null==node?(this.appendTreeNode("torrent-all",[{id:"search-result",text:text,iconCls:"iconfont tr-icon-search"}]),node=this.panel.left.tree("find","search-result")):this.panel.left.tree("update",{target:node.target,text:text}),this.panel.left.tree("select",node.target)}else this.removeTreeNode("search-result")}},getTorrentInfos:function(id){if(transmission.torrents.all[id]&&!transmission.torrents.all[id].infoIsLoading&&!(this.currentTorrentId>0&&transmission.torrents.all[this.currentTorrentId]&&transmission.torrents.all[this.currentTorrentId].infoIsLoading||(this.currentTorrentId=id,this.panel.attribute.panel("options").collapsed))){var torrent=transmission.torrents.all[id];torrent.infoIsLoading=!0;var fields="fileStats,trackerStats,peers,leftUntilDone,status,rateDownload,rateUpload,uploadedEver,uploadRatio,error,errorString,pieces,pieceCount,pieceSize";torrent.moreInfosTag||(fields+=",files,trackers,comment,dateCreated,creator,downloadDir"),transmission.torrents.getMoreInfos(fields,id,(function(result){torrent.infoIsLoading=!1,null!=result&&(jQuery.extend(torrent,result[0]),0!=system.currentTorrentId&&system.currentTorrentId==id?(torrent.completeSize=torrent.totalSize-torrent.leftUntilDone,"files"in torrent&&torrent.files.length>0&&(torrent.moreInfosTag=!0),system.fillTorrentBaseInfos(torrent),system.fillTorrentFileList(torrent),system.fillTorrentServerList(torrent),system.fillTorrentPeersList(torrent),system.fillTorrentConfig(torrent),transmission.torrents.all[id]=torrent,transmission.torrents.datas[id]=torrent):system.clearTorrentAttribute())}))}},clearTorrentAttribute:function(){system.panel.attribute.find("#torrent-files-table").datagrid("loadData",[]),system.panel.attribute.find("#torrent-servers-table").datagrid("loadData",[]),system.panel.attribute.find("#torrent-peers-table").datagrid("loadData",[]),system.panel.attribute.find("span[id*='torrent-attribute-value']").html("")},updateCurrentPageDatas:function(keyField,datas,sourceTable){var rows=sourceTable.datagrid("getRows"),_options=sourceTable.datagrid("options"),orderField=null;_options.sortName&&(orderField=_options.sortName,datas=datas.sort(arrayObjectSort(orderField,_options.sortOrder)));var isFileTable=-1!=sourceTable.selector.indexOf("#torrent-files-table"),tableData=sourceTable.datagrid("getData"),isFileFilterMode=isFileTable&&!!tableData.filterString&&tableData.torrentId==system.currentTorrentId;if(isFileFilterMode&&(datas=fileFilter(datas,tableData.filterString)),0!=isFileFilterMode||0!=rows.length&&datas.length==tableData.total){sourceTable.datagrid("getData").originalRows=datas;var start=(_options.pageNumber-1)*parseInt(_options.pageSize),end=start+parseInt(_options.pageSize);datas=datas.slice(start,end);var newDatas={};for(var index in datas){var item;newDatas[(item=datas[index])[keyField]]=item,item=null}for(var index=rows.length-1;index>=0;index--){var item,data=newDatas[(item=rows[index])[keyField]];data?sourceTable.datagrid("updateRow",{index:index,row:data}):sourceTable.datagrid("deleteRow",index),data=null,item=null}}else sourceTable.datagrid({loadFilter:pagerFilter,pageNumber:1,sortName:orderField,sortOrder:_options.sortOrder}).datagrid("loadData",datas)},fillTorrentBaseInfos:function(torrent){$.each(torrent,(function(key,value){switch(key){case"rateDownload":case"rateUpload":value=formatSize(value,!0,"speed");break;case"totalSize":case"uploadedEver":case"leftUntilDone":case"completeSize":value=formatSize(value);break;case"addedDate":case"dateCreated":case"doneDate":value=formatLongTime(value);break;case"status":value=system.lang.torrent["status-text"][value];break;case"error":0==value?system.panel.attribute.find("#torrent-attribute-tr-error").hide():system.panel.attribute.find("#torrent-attribute-tr-error").show();break;case"remainingTime":value=value>=31536e8?"∞":getTotalTime(value);break;case"comment":value=system.replaceURI(value)}system.panel.attribute.find("#torrent-attribute-value-"+key).html(value)}));for(var pieces=(new Base64).decode_bytes(torrent.pieces),piece=0,pieceCount=torrent.pieceCount,pieceSize=torrent.pieceSize,piecesFlag=[];piece<pieceCount;)for(var bset=pieces.codePointAt(piece>>3),test=128;test>0&&piece<pieceCount;test>>=1,++piece)piecesFlag.push(!(bset&test));for(var MAXCELLS=500,piecePerCell=parseInt((499+pieceCount)/500),cellSize=formatSize(pieceSize*piecePerCell),cellCount=parseInt((piecePerCell-1+pieceCount)/piecePerCell),cell=0,cells="",cell=0,piece=0;cell<cellCount;++cell){for(var done=piecePerCell,i=0;i<piecePerCell;++i,++piece)piecesFlag[piece]&&--done;var percent=parseInt(100*done/piecePerCell),rate=percent/100,ramp;cells+='<i style="filter:saturate('+parseInt(100*(Math.pow(128,rate)-1)/127)/100+')" title="'+cellSize+" x "+percent+'%"></i>'}system.panel.attribute.find("#torrent-attribute-pieces").html(cells)},fillTorrentFileList:function(torrent){var files=torrent.files,fileStats=torrent.fileStats,datas=new Array,namelength=torrent.name.length+1;for(var index in files){var file=files[index],stats=fileStats[index],percentDone=parseFloat(stats.bytesCompleted/file.length*100).toFixed(2);datas.push({name:file.name==torrent.name?file.name:file.name.substr(namelength),index:index,bytesCompleted:stats.bytesCompleted,percentDone:system.getTorrentProgressBar(percentDone,transmission._status.download),length:file.length,wanted:system.lang.torrent.attribute.status[stats.wanted],priority:'<span class="iconlabel icon-flag-'+stats.priority+'">'+system.lang.torrent.attribute.priority[stats.priority]+"</span>"})}this.updateCurrentPageDatas("index",datas,system.panel.attribute.find("#torrent-files-table"))},fillTorrentServerList:function(torrent){var trackerStats=torrent.trackerStats,datas=new Array;for(var index in trackerStats){var stats=trackerStats[index],rowdata={};for(var key in stats)switch(key){case"downloadCount":case"leecherCount":case"seederCount":rowdata[key]=-1==stats[key]?system.lang.public["text-unknown"]:stats[key];break;case"announceState":rowdata[key]=system.lang.torrent.attribute["servers-fields"].announceStateText[stats[key]];break;case"lastAnnounceTime":case"nextAnnounceTime":rowdata[key]=formatLongTime(stats[key]);break;case"lastAnnounceSucceeded":case"lastAnnounceTimedOut":rowdata[key]=system.lang.torrent.attribute.status[stats[key]];break;default:rowdata[key]=stats[key]}datas.push(rowdata)}transmission.torrents.addTracker(torrent),this.updateCurrentPageDatas("id",datas,system.panel.attribute.find("#torrent-servers-table"))},fillTorrentPeersList:function(torrent){var peers=torrent.peers,datas=new Array;let flag;for(var index in peers){var item=peers[index],rowdata={};for(var key in item)rowdata[key]=item[key];if(""!==system.config.ipInfoToken){let flag="",ip=rowdata.address;if(void 0===this.flags[ip]){let settings={url:"https://ipinfo.io/"+ip+"/country?token="+system.config.ipInfoToken,method:"GET",async:!1};$.ajax(settings).done((function(response){flag=response.toLowerCase().trim()})),this.flags[ip]=flag}else flag=this.flags[ip];rowdata.address='<img src="'+this.rootPath+"/style/flags/"+flag+'.png" alt="'+flag+'" title="'+flag+'"> '+ip}rowdata.isUTP=system.lang.torrent.attribute.status[item.isUTP];var percentDone=parseFloat(100*item.progress).toFixed(2);rowdata.progress=system.getTorrentProgressBar(percentDone,transmission._status.download),datas.push(rowdata)}this.updateCurrentPageDatas("address",datas,system.panel.attribute.find("#torrent-peers-table"))},fillTorrentConfig:function(torrent){4==system.panel.attribute.find("#torrent-attribute-tabs").data("selectedIndex")&&transmission.torrents.getConfig(torrent.id,(function(result){if(null!=result){var torrent=transmission.torrents.all[system.currentTorrentId];jQuery.extend(torrent,result[0]),0!=system.currentTorrentId&&$.each(result[0],(function(key,value){var indeterminate=!1,checked=!1,useTag=!1;switch(key){case"seedIdleMode":case"seedRatioMode":0==value&&(checked=!1,indeterminate=!0),useTag=!0;case"downloadLimited":case"uploadLimited":1!=value&&1!=value||(checked=!0),system.panel.attribute.find("input[enabledof='"+key+"']").prop("disabled",!checked),useTag&&system.panel.attribute.find("#"+key).prop("indeterminate",indeterminate).data("_tag",value),system.panel.attribute.find("#"+key).prop("checked",checked);break;default:system.panel.attribute.find("#"+key).val(value),system.panel.attribute.find("#"+key).numberspinner("setValue",value)}}))}}))},setFieldFormat:function(field){if(field.formatter)switch(field.formatter){case"size":field.formatter=function(value,row,index){return formatSize(value)};break;case"speed":field.formatter=function(value,row,index){return formatSize(value,!0,"speed")};break;case"longtime":field.formatter=function(value,row,index){return formatLongTime(value)};break;case"progress":field.formatter=function(value,row,index){var percentDone=parseFloat(100*value).toFixed(2);return system.getTorrentProgressBar(percentDone,transmission.torrents.all[row.id])};break;case"_usename_":switch(field.field){case"name":field.formatter=function(value,row,index){return system.getTorrentNameBar(transmission.torrents.all[row.id])}}break;case"ratio":field.formatter=function(value,row,index){var className="";return parseFloat(value)<1&&-1!=value&&(className="text-status-warning"),'<span class="'+className+'">'+(-1==value?"∞":value)+"</span>"};break;case"remainingTime":field.formatter=function(value,row,index){return value>=31536e8?"∞":getTotalTime(value)};break;case"labels":field.formatter=function(value,row,index){return system.formetTorrentLabels(value,row.hashString)};break;case"color":field.formatter=function(value,row,index){var box;return $("<span class='user-label'/>").html(value).css({"background-color":value,color:getGrayLevel(value)>.5?"#000":"#fff"}).get(0).outerHTML}}},reloadData:function(){this.popoverCount>0?setTimeout((function(){system.reloadData()}),2e3):(this.reloadSession(),this.reloading=!1,this.getServerStatus(),this.reloading=!1,this.reloadTorrentBaseInfos())},loadFolderList:function(oldFolders){for(var index in this.removeTreeNode("folders-loading"),oldFolders){var item=oldFolders[index];item&&system.removeTreeNode(item.nodeid)}0!=transmission.downloadDirs.length&&timedChunk(transmission.downloadDirs,this.appendFolder,this,10,(function(){"Firefox"==$.ua.browser.name&&$.ua.browser.major<60&&system.panel.left.find("span.nav-total-size").css({"margin-top":"-19px"}),system.initUIStatus()}))},appendFolder:function(fullkey){if(fullkey){var rootkey="folders",parentkey=rootkey,folder=fullkey.replace(/\\/g,"/").split("/"),key="folders-",path="";for(var i in folder){var name=folder[i];if(""!=name){var _key;path+=name,key+=this.B64.encode(name).replace(/[+|\/|=]/g,"0");var node=this.panel.left.tree("find",key),folderinfos=transmission.torrents.folders[key];if(folderinfos){var text=name+this.showNodeMoreInfos(folderinfos.count,folderinfos.size);node?this.updateTreeNodeText(key,text):(this.appendTreeNode(parentkey,[{id:key,path:path,downDir:fullkey,text:text,iconCls:"iconfont tr-icon-file"}]),parentkey!=rootkey&&(node=this.panel.left.tree("find",parentkey),this.panel.left.tree("collapse",node.target))),parentkey=key}else this.debug("appendFolder:key",key),this.debug("appendFolder:name",name),this.debug("appendFolder:node",node)}}}},replaceURI:function(text){var reg=/(http|https|ftp):\/\/([^/:]+)(:\d*)?([^# ]*)/gi;return text.replace(reg,(function(url){return'<a href="'+url+'" target="_blank">'+url+"</a>"}))},readConfig:function(){this.readUserConfig();var config=this.getStorageData(this.configHead+".system");for(var key in config&&(this.config=$.extend(!0,this.config,JSON.parse(config))),this.storageKeys.dictionary)this.dictionary[key]=this.getStorageData(this.storageKeys.dictionary[key])},saveConfig:function(){for(var key in this.setStorageData(this.configHead+".system",JSON.stringify(this.config)),this.storageKeys.dictionary)this.setStorageData(this.storageKeys.dictionary[key],this.dictionary[key]);this.saveUserConfig()},saveLabelsConfig:function(hash,labels){system.config.nav.labels&&(0==labels.length?delete system.config.labelMaps[hash]:system.config.labelMaps[hash]=labels)},readUserConfig:function(){var local=window.localStorage[this.configHead];if(local){var localOptions=JSON.parse(local);this.userConfig=$.extend(!0,this.userConfig,localOptions)}},saveUserConfig:function(){window.localStorage[this.configHead]=JSON.stringify(this.userConfig)},uploadTorrentFile:function(fileInputId,savePath,paused,callback){if(window.FileReader){var files=$("input[id='"+fileInputId+"']")[0].files;$.each(files,(function(index,file){transmission.addTorrentFromFile(file,savePath,paused,callback,files.length)}))}else alert(system.lang.public["text-browsers-not-support-features"])},checkUpdate:function(){$.ajax({url:this.checkUpdateScript,dataType:"json",success:function(result){if(result&&result.tag_name){var update=result.created_at.substr(0,10).replace(/-/g,""),version=result.tag_name;if(-1!=$.inArray(version,system.config.ignoreVersion))return;if(system.codeupdate<update){$("#area-update-infos").show(),$("#msg-updateInfos").html(update+" -> "+result.name);var content=$("<div/>"),html=result.body.replace(/\r\n/g,"<br/>"),toolbar=$("<div style='text-align:right;'/>").appendTo(content);$('<a href="https://github.com/ronggang/transmission-web-control/releases/latest" target="_blank" class="easyui-linkbutton" data-options="iconCls:\'iconfont tr-icon-github\'"/>').html(result.name+" ("+update+")").appendTo(toolbar).linkbutton(),$("<span/>").html(" ").appendTo(toolbar),$('<a href="https://github.com/ronggang/transmission-web-control/wiki" target="_blank" class="easyui-linkbutton" data-options="iconCls:\'iconfont tr-icon-help\'"/>').html(system.lang.public["text-how-to-update"]).appendTo(toolbar).linkbutton(),$("<span/>").html(" ").appendTo(toolbar),$("<button onclick=\"javascript:system.addIgnoreVersion('"+version+'\');" class="easyui-linkbutton" data-options="iconCls:\'iconfont tr-icon-cancel-checked\'"/>').html(system.lang.public["text-ignore-this-version"]).appendTo(toolbar).linkbutton(),$("<hr/>").appendTo(content),$("<div/>").html(html).appendTo(content),$("#button-download-update").webuiPopover({content:content.html(),backdrop:!0})}else $("#area-update-infos").hide()}}})},addIgnoreVersion:function(version){-1==$.inArray(version,system.config.ignoreVersion)&&(this.config.ignoreVersion.push(version),this.saveConfig()),$("#button-download-update").webuiPopover("hide"),$("#area-update-infos").hide()},changeLanguages:function(lang){lang!=this.lang.name&&lang&&(this.config.defaultLang=lang,this.saveConfig(),location.href="?lang="+lang)},getStorageData:function(key,defaultValue){return null==window.localStorage[key]?defaultValue:window.localStorage[key]},setStorageData:function(key,value){window.localStorage[key]=value},openDialogFromTemplate:function(config){var defaultConfig={id:null,options:null,datas:null,type:0};if(null!=(config=$.extend(!0,defaultConfig,config)).id){var dialogId=config.id,options=config.options,datas=config.datas,dialog=$("#"+dialogId);if(dialog.length){if(datas&&$.each(datas,(function(key,value){dialog.data(key,value)})),0==config.type&&dialog.attr("type")==config.type)return dialog.dialog("open"),void dialog.dialog({content:system.templates[dialogId]});if(0!=system.popoverCount)return void setTimeout((function(){system.openDialogFromTemplate(config)}),350);dialog.remove()}var defaultOptions={title:"",width:100,height:100,resizable:!1,cache:!0,content:system.lang.dialog["system-config"].loading,modal:!0};options=$.extend(!0,defaultOptions,options),dialog=$("<div/>").attr({id:dialogId,type:config.type}).appendTo(document.body),0==config.type?dialog.dialog(options):(dialog.css({width:options.width,height:options.height}).data("popoverSource",config.source),$(config.source).webuiPopover({url:"#"+dialogId,title:options.title,width:options.width,height:options.height-18,padding:!1,onHide:function(e){$(config.source).webuiPopover("destroy"),$("#"+dialogId).remove(),$(e).remove(),system.popoverCount--,config.onClose&&config.onClose(config.source)},onShow:function(){system.popoverCount++}})),$.get(system.rootPath+"template/"+dialogId+".html?time="+new Date,(function(data){system.templates[dialogId]=data,datas&&$.each(datas,(function(key,value){$("#"+dialogId).data(key,value)})),0==config.type?$("#"+dialogId).dialog({content:data}):(dialog.html(data),$.parser.parse("#"+dialogId),$(config.source).webuiPopover("show"))}))}},debug:function(label,text){window.console&&window.console.log&&window.console.log(label,text)},initThemes:function(){this.themes&&$("#select-themes").combobox({groupField:"group",data:this.themes,editable:!1,panelHeight:"auto",onChange:function(value){var values=(value+";").split(";"),theme=values[0],logo=values[1]||"logo.png";$("#styleEasyui").attr("href","tr-web-control/script/easyui/themes/"+theme+"/easyui.css"),$("#logo").attr("src","tr-web-control/"+logo),system.config.theme=value,system.saveConfig()},onLoadSuccess:function(){$(this).combobox("setValue",system.config.theme||"default")}})},getValidTreeKey:function(text){return text?this.B64.encode(text).replace(/[+|\/|=]/g,"0"):"";var _key}};function fileFilter(dataRows,filterString){for(var filter=new RegExp(filterString||".*"),rawDataFiltered=new Array,j=0;j<dataRows.length;++j)filter.test(dataRows[j].name)&&rawDataFiltered.push(dataRows[j]);return rawDataFiltered}function restoreFileFilterInputbox(defaultFilter){var langText=system.lang.torrent.attribute["filter-template-text"],filterTemplate=[{id:1,text:langText?langText[1]:"All",desc:".*"},{id:2,text:langText?langText[2]:"BitComet padding file",desc:"____padding_file"},{id:3,text:langText?langText[3]:"Unnecessary files",desc:"(.*\\.(url|lnk)$)|(RARBG_DO_NOT_MIRROR\\.exe)|(____padding_file)"}];$('<input id="torrent-files-filter-string" style="width:300px;">').insertAfter("#torrent-files-filter").combobox({valueField:"desc",textField:"desc",panelWidth:400,panelHeight:"auto",formatter:function(row){var s;return'<span style="font-weight:bold; padding:3px;">'+row.text+'</span><br/><span style="padding-left:10px;">'+row.desc+"</span>"}}).combobox("loadData",filterTemplate).combobox("setValue",defaultFilter)}function pagerFilter(data){var isFileData=!1,filterChanged=!1;if("number"==typeof data.length&&"function"==typeof data.splice&&(data={total:data.length,rows:data}),isFileData="torrent-files-table"==this.id){var fileFilterString=$("#torrent-files-filter-string").val();if(filterChanged=data.filterString!==fileFilterString||data.filterString&&data.originalRows.length==data.unfilteredRows.length){data.torrentId=system.currentTorrentId;var rawData=data.unfilteredRows||data.originalRows||data.rows,rawDataFiltered=fileFilter(rawData,fileFilterString);data.originalRows=rawDataFiltered,data.total=rawDataFiltered.length,data.unfilteredRows||(data.unfilteredRows=rawData),data.filterString=fileFilterString}}var dg=$(this),opts=dg.datagrid("options"),pager=dg.datagrid("getPager"),buttons=dg.data("buttons");pager.pagination({onSelectPage:function(pageNum,pageSize){opts.pageNumber=pageNum,opts.pageSize=pageSize,pager.pagination("refresh",{pageNumber:pageNum,pageSize:pageSize}),dg.datagrid("loadData",data)},buttons:buttons}),data.originalRows||(data.originalRows=data.rows);var start=filterChanged?0:(opts.pageNumber-1)*parseInt(opts.pageSize),end=start+parseInt(opts.pageSize);if(data.rows=data.originalRows.slice(start,end),buttons&&buttons.length)for(var i=0;i<buttons.length;i++){var button=buttons[i];button.id&&button.title&&$("#"+button.id,pager).attr("title",button.title)}return isFileData&&restoreFileFilterInputbox(fileFilterString),data}$(document).ready((function(){$.getJSON(system.rootPath+"i18n/en.json").done((function(result){system.defaultLang=result})),$.getJSON(system.rootPath+"i18n.json").done((function(result){system.languages=result,system.init(location.search.getQueryString("lang"),location.search.getQueryString("local"))}))}));