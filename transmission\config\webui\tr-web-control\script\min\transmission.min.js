var transmission={SessionId:"",isInitialized:!1,host:"",port:"9091",path:"/transmission/rpc",rpcpath:"../rpc",fullpath:"",on:{torrentCountChange:null,postError:null},username:"",password:"",_status:{stopped:0,checkwait:1,check:2,downloadwait:3,download:4,seedwait:5,seed:6,actively:101},_trackerStatus:{inactive:0,waiting:1,queued:2,active:3},options:{getFolders:!0,getTarckers:!0},headers:{},trackers:{},islocal:!1,downloadDirs:new Array,getSessionId:function(t,e){var s={type:"POST",url:this.fullpath,error:function(s,n,r){var a="";409===s.status&&(a=s.getResponseHeader("X-Transmission-Session-Id"))&&(t.isInitialized=!0,t.SessionId=a,t.headers["X-Transmission-Session-Id"]=a,e&&e())},headers:this.headers};jQuery.ajax(s)},init:function(t,e){jQuery.extend(this,t),this.username&&this.password&&(this.headers.Authorization="Basic "+(new Base64).encode(this.username+":"+this.password)),this.fullpath=this.rpcpath,this.getSessionId(this,e)},exec:function(t,e,s){if(!this.isInitialized)return!1;var n={method:"",arguments:{},tag:""};jQuery.extend(n,t);var r={type:"POST",url:this.fullpath,dataType:"json",data:JSON.stringify(n),success:function(t,n){e&&e(t,s)},error:function(t,e,s){var n="";409===t.status&&(n=t.getResponseHeader("X-Transmission-Session-Id"))?(transmission.SessionId=n,transmission.headers["X-Transmission-Session-Id"]=n,jQuery.ajax(r)):transmission.on.postError&&transmission.on.postError(t)},headers:this.headers};jQuery.ajax(r)},getStatus:function(t){this.exec({method:"session-stats"},function(e){"success"==e.result&&(t&&t(e.arguments),transmission.torrents.count==e.arguments.torrentCount&&transmission.torrents.activeTorrentCount==e.arguments.activeTorrentCount&&transmission.torrents.pausedTorrentCount==e.arguments.pausedTorrentCount||(transmission.torrents.count=e.arguments.torrentCount,transmission.torrents.activeTorrentCount=e.arguments.activeTorrentCount,transmission.torrents.pausedTorrentCount=e.arguments.pausedTorrentCount,transmission._onTorrentCountChange()))})},getSession:function(t){this.exec({method:"session-get"},function(e){"success"==e.result&&t&&t(e.arguments)})},addTorrentFromUrl:function(t,e,s,n){t.match(/^[0-9a-f]{40}$/i)&&(t="magnet:?xt=urn:btih:"+t);var r={method:"torrent-add",arguments:{filename:t,paused:!s}};e&&(r.arguments["download-dir"]=e),this.exec(r,function(t){switch(t.result){case"success":n&&(t.arguments["torrent-added"]?n(t.arguments["torrent-added"]):t.arguments["torrent-duplicate"]&&n({status:"duplicate",torrent:t.arguments["torrent-duplicate"]}));break;case"duplicate torrent":default:n&&n(t.result)}})},addTorrentFromFile:function(t,e,s,n,r){var a=new FileReader;a.onload=function(t){var a=t.target.result,o=a.indexOf("base64,");if(-1!=o){var i=a.substring(o+"base64,".length);transmission.exec({method:"torrent-add",arguments:{metainfo:i,"download-dir":e,paused:s}},function(t){switch(t.result){case"success":n&&(null!=t.arguments["torrent-added"]?n(t.arguments["torrent-added"],r):null!=t.arguments["torrent-duplicate"]?n({status:"duplicate",torrent:t.arguments["torrent-duplicate"]},r):n("error"));break;case"duplicate torrent":n&&n("duplicate")}})}},a.readAsDataURL(t)},_onTorrentCountChange:function(){this.torrents.loadSimpleInfo=!1,this.on.torrentCountChange&&this.on.torrentCountChange()},removeTorrent:function(t,e,s){this.exec({method:"torrent-remove",arguments:{ids:t,"delete-local-data":e}},function(t){s&&s(t.result)})},getFreeSpace:function(t,e){this.exec({method:"free-space",arguments:{path:t}},function(t){e&&e(t)})},updateBlocklist:function(t){this.exec({method:"blocklist-update"},function(e){t&&t(e.result)})},renameTorrent:function(t,e,s,n){var r=this.torrents.all[t];if(!r)return!1;this.exec({method:"torrent-rename-path",arguments:{ids:[t],path:e||r.name,name:s}},function(t){n&&n(t)})},closeSession:function(t){this.exec({method:"session-close"},function(e){t&&t(e)})}};