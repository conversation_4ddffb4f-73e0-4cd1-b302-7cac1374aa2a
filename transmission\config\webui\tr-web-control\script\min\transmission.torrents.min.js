transmission.torrents={all:null,puased:null,downloading:null,actively:null,searchResult:null,error:null,warning:null,folders:{},status:{},count:0,totalSize:0,loadSimpleInfo:false,activeTorrentCount:0,pausedTorrentCount:0,fields:{base:"id,name,status,hashString,totalSize,percentDone,addedDate,trackerStats,leftUntilDone,rateDownload,rateUpload,recheckProgress,rateDownload,rateUpload,peersGettingFromUs,peersSendingToUs,uploadRatio,uploadedEver,downloadedEver,downloadDir,error,errorString,doneDate,queuePosition,activityDate",
status:"id,status,percentDone,trackerStats,leftUntilDone,rateDownload,rateUpload,rateDownload,rateUpload,peersGettingFromUs,peersSendingToUs,uploadRatio,uploadedEver,downloadedEver,error,errorString,doneDate,queuePosition,activityDate",config:"downloadLimit,downloadLimited,peer-limit,seedIdleLimit,seedIdleMode,seedRatioLimit,seedRatioMode,uploadLimit,uploadLimited"},datas:{},recently:null,removed:null,isRecentlyActive:false,newIds:[],btItems:[],getallids:function(b,d,c){var a=this.fields.base;if(this.loadSimpleInfo&&
this.all)a=this.fields.status;a=a.split(",");$.isArray(c)&&$.unique($.merge(a,c));c={fields:a};this.isRecentlyActive=false;if(this.all&&d==undefined){c.ids="recently-active";this.isRecentlyActive=true}else if(d)c.ids=d;if(!this.all)this.all={};transmission.exec({method:"torrent-get",arguments:c},function(f){if(f.result=="success"){transmission.torrents.newIds.length=0;transmission.torrents.loadSimpleInfo=true;transmission.torrents.recently=f.arguments.torrents;transmission.torrents.removed=f.arguments.removed;
transmission.torrents.splitid();b&&b(f.arguments.torrents)}else{transmission.torrents.datas=null;b&&b(null)}})},splitid:function(){this.downloading=[];this.puased=[];this.actively=[];this.error=[];this.warning=[];this.btItems=[];transmission.downloadDirs=[];var b=transmission._status;this.status={};transmission.trackers={};this.totalSize=0;this.folders={};this.count=0;var d=new Base64,c;for(c in this.recently){var a=this.recently[c];this.datas[a.id]=a}var f=[];for(c in this.removed){a=this.removed[c];
f.push(a)}for(c in this.datas){a=this.datas[c];if(!a)return;if($.inArray(a.id,f)!=-1&&f.length>0){if(this.all[a.id]){this.all[a.id]=null;delete this.all[a.id]}this.datas[c]=null;delete this.datas[c]}else{this.isRecentlyActive&&!this.all[a.id]&&this.newIds.push(a.id);a=$.extend(this.all[a.id],a);if(a.uploadedEver==0&&a.downloadedEver==0)a.uploadRatio=-1;a.uploadRatio=parseFloat(a.uploadRatio);a.infoIsLoading=false;var e=this.status[a.status];this.addTracker(a);if(!e){this.status[a.status]=[];e=this.status[a.status]}this.totalSize+=
a.totalSize;a.remainingTime=a.rateDownload>0&&a.leftUntilDone>0?Math.floor(a.leftUntilDone/a.rateDownload*1E3):a.rateDownload==0&&a.leftUntilDone==0&&a.totalSize!=0?0:31536E8;e.push(a);a.error!=0&&this.error.push(a);if(a.rateUpload>0||a.rateDownload>0)this.actively.push(a);switch(a.status){case b.stopped:this.puased.push(a);break;case b.download:this.downloading.push(a)}this.all[a.id]=a;$.inArray(a.downloadDir,transmission.downloadDirs)==-1&&transmission.downloadDirs.push(a.downloadDir);if(transmission.options.getFolders)if(a.downloadDir){e=
a.downloadDir.replace(/\\/g,"/").split("/");var h="folders-",i;for(i in e){var g=e[i];if(g!=""){g=d.encode(g);h+=g.replace(/[+|\/|=]/g,"0");(g=this.folders[h])||(g={count:0,torrents:[],size:0,nodeid:h});g.torrents.push(a);g.count++;g.size+=a.totalSize;this.folders[h]=g}}}this.count++}}transmission.downloadDirs=transmission.downloadDirs.sort();this.newIds.length>0&&this.getallids(null,this.newIds)},addTracker:function(b){var d=b.trackerStats,c=[];b.leecherCount=0;b.seederCount=0;if(d.length>0){var a=
[],f;for(f in d){var e=d[f],h=e.lastAnnounceResult.toLowerCase(),i=e.host.getHostName().split(".");$.inArray(i[0],"www,tracker".split(","))!=-1&&i.shift();i=i.join(".");var g="tracker-"+i.replace(/\./g,"-"),j=transmission.trackers[g];if(!j){transmission.trackers[g]={count:0,torrents:[],size:0,connected:true,isBT:d.length>5};j=transmission.trackers[g]}j.name=i;j.nodeid=g;j.host=e.host;if(!e.lastAnnounceSucceeded&&e.announceState!=transmission._trackerStatus.inactive){a.push(e.lastAnnounceResult);if(h==
"could not connect to tracker")j.connected=false}if(j.torrents.indexOf(b)==-1){j.torrents.push(b);j.count++;j.size+=b.totalSize}b.leecherCount+=e.leecherCount;b.seederCount+=e.seederCount;c.indexOf(i)==-1&&c.push(i)}d.length>5&&this.btItems.push(b);if(a.length==d.length){b.warning=a.join(";");if(b.nextAnnounceTime){if(b.nextAnnounceTime>e.nextAnnounceTime)b.nextAnnounceTime=e.nextAnnounceTime}else b.nextAnnounceTime=e.nextAnnounceTime;this.warning.push(b)}if(b.leecherCount<0)b.leecherCount=0;if(b.seederCount<
0)b.seederCount=0;b.leecher=b.leecherCount+" ("+b.peersGettingFromUs+")";b.seeder=b.seederCount+" ("+b.peersSendingToUs+")";b.trackers=c.join(";")}},getPeers:function(b){transmission.exec({method:"torrent-get",arguments:{fields:"peers,peersFrom".split(","),ids:b}},function(d){console.log("data:",d)})},getMoreInfos:function(b,d,c){transmission.exec({method:"torrent-get",arguments:{fields:b.split(","),ids:d}},function(a){if(a.result=="success")c&&c(a.arguments.torrents);else c&&c(null)})},search:function(b,
d){if(!b)return null;if(!d)d=this.all;var c=[];$.each(d,function(a){d[a].name.toLowerCase().indexOf(b.toLowerCase())!=-1&&c.push(d[a])});return this.searchResult=c},getFiles:function(b,d){transmission.exec({method:"torrent-get",arguments:{fields:"files,fileStats".split(","),ids:b}},function(c){if(c.result=="success")d&&d(c.arguments.torrents);else d&&d(null)})},getConfig:function(b,d){this.getMoreInfos(this.fields.config,b,d)},getErrorIds:function(b,d){var c=[],a=new Date;if(d==true)a=a.getTime()/
1E3;for(var f in this.error){var e=this.error[f];if(!($.inArray(e.id,b)!=-1&&b.length>0)){if(d==true)if(a<e.nextAnnounceTime)continue;e.status!=transmission._status.stopped&&c.push(e.id)}}for(f in this.warning){e=this.warning[f];if(!($.inArray(e.id,b)!=-1&&b.length>0)){if(d==true)if(a<e.nextAnnounceTime)continue;c.push(e.id)}}return c},searchAndReplaceTrackers:function(b,d,c){if(b&&d){var a={},f=0,e;for(e in this.all){var h=this.all[e];if(!h)return;var i=h.trackerStats,g;for(g in i)if(i[g].announce==
b){a[g]||(a[g]={ids:[],tracker:d});a[g].ids.push(h.id);f++}}f==0&&c&&c(null,0);for(e in a)transmission.exec({method:"torrent-set",arguments:{ids:a[e].ids,trackerReplace:[parseInt(e),a[e].tracker]}},function(j,k){if(j.result=="success")c&&c(k,f);else c&&c(null)},a[e].ids)}},getMagnetLink:function(b,d){var c="";if(b.constructor.name!="Array")b=[b];if(b.length==0)d&&d(c);else{var a=[],f;for(f in b){f=b[f];if(this.all[f])if(this.all[f].magnetLink)c+=this.all[f].magnetLink+"\n";else a.push(f)}if(a.length==
0)d&&d(c.trim());else transmission.exec({method:"torrent-get",arguments:{fields:["id","magnetLink"],ids:a}},function(e){if(e.result=="success"){for(var h in e.arguments.torrents){h=e.arguments.torrents[h];transmission.torrents.all[h.id].magnetLink=h.magnetLink;c+=h.magnetLink+"\n"}d&&d(c.trim())}})}}};
