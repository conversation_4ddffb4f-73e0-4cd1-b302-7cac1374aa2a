<div class="easyui-layout" data-options="fit:true" style="width:100%;height:100%;">
    <div data-options="region:'center'" style="padding:5px 6px 0px 6px;border:0px;">
        <div id="system-config-tabs" class="easyui-tabs" style="width:100%;height:100%;"
             data-options="fit:true,plain:true">
            <div title="Basic Information" style="padding:5px;padding-right:10px;" class="dialog">
                <table style="width:100%;">
                    <tr>
                        <td width="100%" colspan="2">
                            <span id="system-config-download-dir"></span>
                            <span style="float:right;">(<span id="system-config-download-dir-free-space"></span><span
                                    id="download-dir-free-space"></span>)</span>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2"><select id="download-dir" style="width:100%;"></select></td>
                    </tr>
                    <tr>
                        <td colspan="2"><input id="incomplete-dir-enabled" type="checkbox" style="width:16px;"/><label
                                id="system-config-incomplete-dir-enabled" for="incomplete-dir-enabled"></label></td>
                    </tr>
                    <tr>
                        <td colspan="2"><input id="incomplete-dir" value="" type="text"
                                               enabledof="incomplete-dir-enabled" style="width:100%;"/></td>
                    </tr>
                    <tr>
                        <td colspan="2"><input id="rename-partial-files" type="checkbox" style="width:16px;"/><label
                                id="system-config-rename-partial-files" for="rename-partial-files"></label></td>
                    </tr>
                    <tr>
                        <td colspan="2"><input id="start-added-torrents" type="checkbox" style="width:16px;"/><label
                                id="system-config-start-added-torrents" for="start-added-torrents"></label></td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <hr/>
                        </td>
                    </tr>
                    <tr>
                        <td width="65%"><span id="system-config-cache-size-mb"></span></td>
                        <td width="35%"><input id="cache-size-mb" value="" type="text" class="easyui-numberspinner"
                                               style="width:70px;"/> MB
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <hr/>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2"><input id="script-torrent-done-enabled" type="checkbox"
                                               style="width:16px;"/><label for="script-torrent-done-enabled"
                                                                           system-lang="dialog['system-config']['script-torrent-done-enabled']"></label>
                            <a href="https://github.com/ronggang/transmission-web-control/wiki/About-script-torrent-done-filename"
                               target="_blank">Wiki</a>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2"><input id="script-torrent-done-filename" value="" type="text"
                                               enabledof="script-torrent-done-enabled" style="width:100%;"/></td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <hr/>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <span id="system-config-config-dir"></span>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2"><input id="config-dir" value="" type="text" readonly="readonly"
                                               disabled="disabled" style="width:100%;"/></td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <hr/>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2" align="right">
                            <span id="system-config-language"></span>&nbsp;<select id="system-config-lang" value=""
                                                                                   style="width:200px;"></select>
                        </td>
                    </tr>
                </table>
            </div>
            <div title="Network" style="padding:5px;padding-right:10px;" class="dialog">
                <table style="width:100%;">
                    <tr>
                        <td width="65%">
                            <input id="download-queue-enabled" type="checkbox" style="width:16px;"/><label
                                id="system-config-download-queue-enabled" for="download-queue-enabled"></label>
                        </td>
                        <td width="35%"><input id="download-queue-size" value="" type="text"
                                               class="easyui-numberspinner" enabledof="download-queue-enabled"/></td>
                    </tr>
                    <tr>
                        <td>
                            <input id="seed-queue-enabled" type="checkbox" style="width:16px;"/><label
                                id="system-config-seed-queue-enabled" for="seed-queue-enabled"></label>
                        </td>
                        <td><input id="seed-queue-size" value="" type="text" class="easyui-numberspinner"
                                   enabledof="seed-queue-enabled"/></td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <hr/>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <span id="system-config-encryption"></span>
                        </td>
                        <td>
                            <select id="encryption" class="easyui-combobox" data-options="editable:false"></select>
                        </td>
                    </tr>
                    <tr>
                        <td valign="top">
                            <input id="peer-port-random-on-start" type="checkbox" style="width:16px;"/><label
                                id="system-config-peer-port-random-on-start" for="peer-port-random-on-start"></label>
                        </td>
                        <td>
                            <input id="peer-port" value="" type="text" class="easyui-numberspinner"
                                   style="width:70px;"/> <a id="system-config-test-port" href="javascript:void(0);"
                                                            class="easyui-linkbutton"
                                                            data-options="plain:true,iconCls:'icon-test-port'"></a>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <input id="port-forwarding-enabled" type="checkbox" style="width:16px;"/><label
                                id="system-config-port-forwarding-enabled" for="port-forwarding-enabled"></label>
                        </td>
                        <td>
                            <span id="system-config-port-is-open-true" style="display:none;color:#339900;"></span>
                            <span id="system-config-port-is-open-false" style="display:none;color:red;"></span>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <input id="lpd-enabled" type="checkbox" style="width:16px;"/><label
                                id="system-config-lpd-enabled" for="lpd-enabled"></label>
                        </td>
                        <td>
                            <input id="utp-enabled" type="checkbox" style="width:16px;"/><label
                                id="system-config-utp-enabled" for="utp-enabled"></label>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <input id="dht-enabled" type="checkbox" style="width:16px;"/><label
                                id="system-config-dht-enabled" for="dht-enabled"></label>
                        </td>
                        <td>
                            <input id="pex-enabled" type="checkbox" style="width:16px;"/><label
                                id="system-config-pex-enabled" for="pex-enabled"></label>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <hr/>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <input id="blocklist-enabled" type="checkbox" style="width:16px;"/><label
                                id="system-config-blocklist-enabled" for="blocklist-enabled"></label>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <input id="blocklist-url" type="text" enabledof="blocklist-enabled"/>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <span id="system-config-blocklist-size"
                                  system-lang="dialog['system-config']['blocklist-size']"></span>
                            <button id="system-conifg-button-update-blocklist" class="easyui-linkbutton"
                                    data-options="iconCls:'iconfont tr-icon-reload'"
                                    system-lang="dialog['public']['button-update']"
                                    enabledof="blocklist-enabled"></button>
                        </td>
                    </tr>
                </table>
            </div>
            <div title="Limit" style="padding:5px;padding-right:10px;" class="dialog">
                <table style="width:100%;">
                    <tr>
                        <td width="65%">
                            <input id="speed-limit-down-enabled" type="checkbox" style="width:16px;"/><label
                                id="system-config-speed-limit-down-enabled" for="speed-limit-down-enabled"></label>
                        </td>
                        <td width="35%"><input id="speed-limit-down" value="" type="text" class="easyui-numberspinner"
                                               enabledof="speed-limit-down-enabled"/> KB/s
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <input id="speed-limit-up-enabled" type="checkbox" style="width:16px;"/><label
                                id="system-config-speed-limit-up-enabled" for="speed-limit-up-enabled"></label>
                        </td>
                        <td><input id="speed-limit-up" value="" type="text" class="easyui-numberspinner"
                                   enabledof="speed-limit-up-enabled"/> KB/s
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <hr/>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <span id="system-config-peer-limit-global"></span>
                        </td>
                        <td>
                            <input id="peer-limit-global" value="" type="text" class="easyui-numberspinner"/>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <span id="system-config-peer-limit-per-torrent"></span>
                        </td>
                        <td>
                            <input id="peer-limit-per-torrent" value="" type="text" class="easyui-numberspinner"/>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <hr/>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <input id="seedRatioLimited" type="checkbox" style="width:16px;"/><label
                                id="system-config-seedRatioLimited" for="seedRatioLimited"></label>
                        </td>
                        <td><input id="seedRatioLimit" value="" type="text" class="easyui-numberspinner"
                                   enabledof="seedRatioLimited" data-options="precision:2"/></td>
                    </tr>
                    <tr>
                        <td>
                            <input id="idle-seeding-limit-enabled" type="checkbox" style="width:16px;"/><label
                                id="system-config-idle-seeding-limit-enabled" for="idle-seeding-limit-enabled"></label>
                        </td>
                        <td><input id="idle-seeding-limit" value="" type="text" class="easyui-numberspinner"
                                   enabledof="idle-seeding-limit-enabled"/> <span name="system-config-minutes"></span>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <input id="queue-stalled-enabled" type="checkbox" style="width:16px;"/><label
                                id="system-config-queue-stalled-enabled" for="queue-stalled-enabled"></label>
                        </td>
                        <td><input id="queue-stalled-minutes" value="" type="text" class="easyui-numberspinner"
                                   enabledof="queue-stalled-enabled"/> <span name="system-config-minutes"></span></td>
                    </tr>
                </table>
            </div>
            <div title="Alt speeds" style="padding:5px;" class="dialog">
                <fieldset>
                    <table style="width:100%;">
                        <tr>
                            <td width="65%">
                                <span id="system-config-alt-speed-down"></span>
                            </td>
                            <td width="35%"><input id="alt-speed-down" value="" type="text" class="easyui-numberspinner"
                                                   _enabledof="alt-speed-enabled"/> KB/s
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <label id="system-config-alt-speed-up"></label>
                            </td>
                            <td><input id="alt-speed-up" value="" type="text" class="easyui-numberspinner"
                                       _enabledof="alt-speed-enabled"/> KB/s
                            </td>
                        </tr>
                    </table>
                </fieldset>
                <fieldset>
                    <legend id="">
                        <input id="alt-speed-time-enabled" type="checkbox" style="width:16px;"/><label
                            id="system-config-alt-speed-time-enabled" for="alt-speed-time-enabled"></label>
                    </legend>
                    <table style="width:100%;">
                        <tr>
                            <td>
                                <span id="system-config-alt-speed-time"></span>
                                <input id="alt-speed-time-begin" value="" type="text" class="easyui-timespinner"
                                       style="width:80px;" enabledof="alt-speed-time-enabled"/> -
                                <input id="alt-speed-time-end" value="" type="text" class="easyui-timespinner"
                                       style="width:80px;" enabledof="alt-speed-time-enabled"/>
                            </td>
                        </tr>
                        <tr>
                            <td style="padding:10px 0px 5px 0px;">
                                <input id="alt-speed-time-weekday-1" type="checkbox" style="width:16px;"
                                       enabledof="alt-speed-time-enabled"/><label id="system-config-weekday-1"
                                                                                  for="alt-speed-time-weekday-1"></label>
                                <input id="alt-speed-time-weekday-2" type="checkbox" style="width:16px;"
                                       enabledof="alt-speed-time-enabled"/><label id="system-config-weekday-2"
                                                                                  for="alt-speed-time-weekday-2"></label>
                                <input id="alt-speed-time-weekday-3" type="checkbox" style="width:16px;"
                                       enabledof="alt-speed-time-enabled"/><label id="system-config-weekday-3"
                                                                                  for="alt-speed-time-weekday-3"></label>
                                <input id="alt-speed-time-weekday-4" type="checkbox" style="width:16px;"
                                       enabledof="alt-speed-time-enabled"/><label id="system-config-weekday-4"
                                                                                  for="alt-speed-time-weekday-4"></label>
                                <input id="alt-speed-time-weekday-5" type="checkbox" style="width:16px;"
                                       enabledof="alt-speed-time-enabled"/><label id="system-config-weekday-5"
                                                                                  for="alt-speed-time-weekday-5"></label>
                                <input id="alt-speed-time-weekday-6" type="checkbox" style="width:16px;"
                                       enabledof="alt-speed-time-enabled"/><label id="system-config-weekday-6"
                                                                                  for="alt-speed-time-weekday-6"></label>
                                <input id="alt-speed-time-weekday-0" type="checkbox" style="width:16px;"
                                       enabledof="alt-speed-time-enabled"/><label id="system-config-weekday-0"
                                                                                  for="alt-speed-time-weekday-0"></label>
                            </td>
                        </tr>
                    </table>
                </fieldset>
                <input id="alt-speed-enabled" type="checkbox" style="width:16px;"/><label
                    id="system-config-alt-speed-enabled" for="alt-speed-enabled"></label>
            </div>
            <div title="Folders Dictionary" style="padding:5px;padding-right:10px;" class="dialog">
                <table style="width:100%;height:100%;">
                    <tr>
                        <td width="100%" height="100%">
                            <textarea id="local-storage-dictionary-folders" style="width:100%;height:100%;"></textarea>
                        </td>
                    </tr>
                </table>
            </div>
            <div title="More" style="padding:10px;" class="dialog">
                <table style="width:100%;">
                    <tr>
                        <td width="80%">
                            <label for="show-bt-servers"
                                   system-lang="dialog['system-config']['show-bt-servers']"></label>
                        </td>
                        <td>
                            <input id="show-bt-servers" class="easyui-switchbutton" type="checkbox" config-type="1"/>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label for="hide-subfolders"
                                   system-lang="dialog['system-config']['hide-subfolders']"></label>
                        </td>
                        <td>
                            <input id="hide-subfolders" class="easyui-switchbutton" type="checkbox" config-type="1"/>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label for="simpleCheckMode"
                                   system-lang="dialog['system-config']['simple-check-mode']"></label>
                        </td>
                        <td>
                            <input id="simpleCheckMode" class="easyui-switchbutton" type="checkbox" config-type="1"/>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <hr/>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2"><span system-lang="dialog['system-config']['nav-contents']"></span></td>
                    </tr>
                    <tr>
                        <td>
                            <label system-lang="tree['servers']"></label>
                        </td>
                        <td>
                            <input id="nav-servers" class="easyui-switchbutton" type="checkbox" config-type="1"/>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label system-lang="tree['folders']"></label>
                        </td>
                        <td>
                            <input id="nav-folders" class="easyui-switchbutton" type="checkbox" config-type="1"/>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label system-lang="tree['statistics']['title']"></label>
                        </td>
                        <td>
                            <input id="nav-statistics" class="easyui-switchbutton" type="checkbox" config-type="1"/>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <label system-lang="tree['labels']"></label>
                        </td>
                        <td>
                            <input id="nav-labels" class="easyui-switchbutton" type="checkbox" config-type="1"/>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <label system-lang="dialog['system-config']['ipinfo']"></label>
                            <a id="system-config-test-port" href="http://ipinfo.io" target="_blank"
                                                            class="easyui-linkbutton"
                                                            data-options="plain:true,iconCls:'icon-about'"></a>
                        </td>
                    </tr>
                    <tr>
                        <td colspan="2">
                            <input id="ipInfoToken" value="" type="text" style="width:100%;"/>
                        </td>
                    </tr>
                </table>
            </div>
            <div title="User Labels" style="padding:5px;" class="dialog">
                <div id="divLabels" style="width:100%;height: 100%;"></div>
                <div id="divLabelsToolbar" style="height: auto;">
                    <a id="btn-UserLabel-addNew" href="javascript:void(0)" class="easyui-linkbutton"
                       data-options="iconCls:'iconfont tr-icon-plus',plain:true"
                       system-lang="dialog['public']['button-addnew']"></a>
                    <a id="btn-UserLabel-remove" href="javascript:void(0)" class="easyui-linkbutton"
                       style="color:crimson;" data-options="iconCls:'iconfont tr-icon-delete',plain:true"
                       system-lang="dialog['public']['button-delete']"></a>
                    <span class="button-split">|</span>
                    <a id="btn-UserLabel-import" href="javascript:void(0)" class="easyui-linkbutton"
                       data-options="iconCls:'iconfont tr-icon-download',plain:true"
                       system-lang="dialog['public']['button-import']"></a>
                    <a id="btn-UserLabel-export" href="javascript:void(0)" class="easyui-linkbutton"
                       data-options="iconCls:'iconfont tr-icon-upload',plain:true"
                       system-lang="dialog['public']['button-export']"></a>
                </div>
            </div>
        </div>
    </div>
    <div data-options="region:'south',border:false" style="text-align:right;padding:6px;">
        <a id="system-config-button-reload" href="javascript:void(0)" class="easyui-linkbutton"
           data-options="plain:true,iconCls:'icon-reload'" style="float:left;display:none;"></a>
        <button id="restore-default-settings" style="float:left;"
                system-lang="dialog['system-config']['restore-default-settings']"
                data-options="iconCls:'iconfont tr-icon-restore',plain:true" class="easyui-linkbutton"></button>
        <span style="float:left;margin-top: 4px;height: auto;" class="button-split">|</span>
        <button id="btn-import-config" style="float:left;" system-lang="dialog['public']['button-import']"
                system-tip-lang="dialog['system-config']['import-config']"
                data-options="iconCls:'iconfont tr-icon-download',plain:true" class="easyui-linkbutton"></button>
        <button id="btn-export-config" style="float:left;" system-lang="dialog['public']['button-export']"
                system-tip-lang="dialog['system-config']['export-config']"
                data-options="iconCls:'iconfont tr-icon-upload',plain:true" class="easyui-linkbutton"></button>
        <span id="system-config-nochange" style="display:none;"></span>
        <span id="system-config-saved" style="display:none;"></span>
        <a id="system-config-button-save" class="easyui-linkbutton" data-options="iconCls:'icon-save',plain:true"
           href="javascript:void(0);">Ok</a>
        <a id="system-config-button-cancel" class="easyui-linkbutton" data-options="iconCls:'icon-cancel',plain:true"
           href="javascript:void(0);">Cancel</a>
    </div>
</div>
<script type="text/javascript">
  (function (thisDialog) {
    var page = {
      selectedIndex: -1,
      init: function () {
        this.initLangText();
        this.initTabs();
        this.initLanguageCombo();
        this.initConfigValue();
        this.initEvents();
        this.initUserLabels();
      },
      // 初始化选项卡
      initTabs: function () {
        var tabs = thisDialog.find('#system-config-tabs');
        tabs.tabs();
        var base = tabs.tabs('getTab', 0);

        tabs.tabs('update', {
          tab: tabs.tabs('getTab', 0),
          options: {
            title: system.lang.dialog['system-config'].tabs.base
          }
        });
        tabs.tabs('update', {
          tab: tabs.tabs('getTab', 1),
          options: {
            title: system.lang.dialog['system-config'].tabs.network
          }
        });
        tabs.tabs('update', {
          tab: tabs.tabs('getTab', 2),
          options: {
            title: system.lang.dialog['system-config'].tabs.limit
          }
        });
        tabs.tabs('update', {
          tab: tabs.tabs('getTab', 3),
          options: {
            title: system.lang.dialog['system-config'].tabs['alt-speed']
          }
        });
        tabs.tabs('update', {
          tab: tabs.tabs('getTab', 4),
          options: {
            title: system.lang.dialog['system-config'].tabs['dictionary-folders']
          }
        });
        tabs.tabs('update', {
          tab: tabs.tabs('getTab', 5),
          options: {
            title: system.lang.dialog['system-config'].tabs['more']
          }
        });
        tabs.tabs('update', {
          tab: tabs.tabs('getTab', 6),
          options: {
            title: system.lang.dialog['system-config'].tabs['labels']
          }
        });
      },
      // Set the language
      initLanguageCombo: function () {
        $.each(system.languages, function (key, value) {
          $('<option/>').text(value).val(key).attr('selected', (key == system.lang.name ? true : false)).appendTo(thisDialog.find('#system-config-lang'));
        });
        $('#system-config-lang', thisDialog).combobox({
          editable: false,
          panelHeight: 'auto'
        });
      },
      // 初始化界面文字
      initLangText: function () {
        var title = 'button-save,button-cancel,button-reload'.split(',');
        $.each(title, function (i, item) {
          thisDialog.find('#system-config-' + item).html(system.lang.dialog.public[item]);
        });

        system.resetLangText(thisDialog);
        thisDialog.find('#system-config-blocklist-size').html(system.lang.dialog['system-config']['blocklist-size'].replace('%n', system.serverConfig['blocklist-size']));

        title = ('config-dir,download-dir,incomplete-dir-enabled,cache-size-mb,rename-partial-files,download-dir-free-space,start-added-torrents' +
          ',download-queue-enabled,seed-queue-enabled,encryption,peer-port-random-on-start,speed-limit-down-enabled,speed-limit-up-enabled' +
          ',alt-speed-enabled,alt-speed-down,alt-speed-up,test-port,port-forwarding-enabled,utp-enabled,dht-enabled,lpd-enabled,pex-enabled' +
          ',peer-limit-global,peer-limit-per-torrent,blocklist-enabled,seedRatioLimited,queue-stalled-enabled,idle-seeding-limit-enabled' +
          ',alt-speed-time-enabled,alt-speed-time,nochange,port-is-open-true,port-is-open-false,language'
        ).split(',');
        $.each(title, function (i, item) {
          thisDialog.find('#system-config-' + item).html(system.lang.dialog['system-config'][item]);
        });

        thisDialog.find('span[name=\'system-config-minutes\']').html(system.lang.dialog['system-config']['minutes']);
      },
      // 初始化参数值
      initConfigValue: function () {
        // Replace system settings
        var title = ('incomplete-dir-enabled,rename-partial-files,start-added-torrents,download-queue-enabled,seed-queue-enabled' +
          ',peer-port-random-on-start,speed-limit-down-enabled,speed-limit-up-enabled,alt-speed-enabled,idle-seeding-limit-enabled' +
          ',port-forwarding-enabled,utp-enabled,dht-enabled,lpd-enabled,pex-enabled,blocklist-enabled,seedRatioLimited,queue-stalled-enabled' +
          ',alt-speed-time-enabled,script-torrent-done-enabled'
        ).split(',');
        $.each(title, function (i, item) {
          thisDialog.find('#' + item).prop('checked', system.serverConfig[item])
            .click(function () {
              thisDialog.find('*[enabledof=\'' + this.id + '\']').prop('disabled', !this.checked);
              if (this.checked) {
                thisDialog.find('.easyui-numberspinner[enabledof=\'' + this.id + '\']').numberspinner('enable');
                thisDialog.find('.easyui-timespinner[enabledof=\'' + this.id + '\']').timespinner('enable');
                thisDialog.find('.easyui-linkbutton[enabledof=\'' + this.id + '\']').linkbutton('enable');
              } else {
                thisDialog.find('.easyui-numberspinner[enabledof=\'' + this.id + '\']').numberspinner('disable');
                thisDialog.find('.easyui-timespinner[enabledof=\'' + this.id + '\']').timespinner('disable');
                thisDialog.find('.easyui-linkbutton[enabledof=\'' + this.id + '\']').linkbutton('disable');
              }
            });
        });

        // Set content
        thisDialog.find('#download-dir').val(system.downloadDir);
        $.each(transmission.downloadDirs, function (i, item) {
          $('<option/>').text(item).val(item).attr('selected', (item == system.downloadDir ? true : false)).appendTo(thisDialog.find('#download-dir'));
        });
        if (transmission.downloadDirs.length == 0) {
          $('<option/>').text(system.downloadDir).val(system.downloadDir).attr('selected', true).appendTo(thisDialog.find('#download-dir'));
        }
        thisDialog.find('#download-dir').combobox();

        thisDialog.find('#config-dir').val(system.serverConfig['config-dir']);
        thisDialog.find('#incomplete-dir').val(system.serverConfig['incomplete-dir']);
        thisDialog.find('#incomplete-dir').prop('disabled', !system.serverConfig['incomplete-dir-enabled']);
        thisDialog.find('#cache-size-mb').val(system.serverConfig['cache-size-mb']);
        thisDialog.find('#download-queue-size').val(system.serverConfig['download-queue-size']).prop('disabled', !system.serverConfig['download-queue-enabled']);
        thisDialog.find('#seed-queue-size').val(system.serverConfig['seed-queue-size']).prop('disabled', !system.serverConfig['seed-queue-enabled']);
        thisDialog.find('#peer-port').val(system.serverConfig['peer-port']).prop('disabled', system.serverConfig['peer-port-random-on-start']);

        thisDialog.find('#speed-limit-down').val(system.serverConfig['speed-limit-down']).prop('disabled', !system.serverConfig['speed-limit-down-enabled']);
        thisDialog.find('#speed-limit-up').val(system.serverConfig['speed-limit-up']).prop('disabled', !system.serverConfig['speed-limit-up-enabled']);

        thisDialog.find('#alt-speed-down').val(system.serverConfig['alt-speed-down']);
        thisDialog.find('#alt-speed-up').val(system.serverConfig['alt-speed-up']);

        thisDialog.find('#alt-speed-time-begin').val(getHoursFromMinutes(system.serverConfig['alt-speed-time-begin'])).prop('disabled', !system.serverConfig['alt-speed-time-enabled']);
        thisDialog.find('#alt-speed-time-end').val(getHoursFromMinutes(system.serverConfig['alt-speed-time-end'])).prop('disabled', !system.serverConfig['alt-speed-time-enabled']);

        var days = ('0000000' + system.serverConfig['alt-speed-time-day'].toString(2)).right(7).split('').reverse();
        var weekday = system.lang.dialog['system-config']['weekday'];
        // Set schedule
        for (var day in weekday) {
          thisDialog.find('#alt-speed-time-weekday-' + day).prop('checked', (days[day] == 1 ? true : false)).prop('disabled', !system.serverConfig['alt-speed-time-enabled']);
          thisDialog.find('#system-config-weekday-' + day).text(weekday[day]);
        }

        thisDialog.find('#peer-limit-global').val(system.serverConfig['peer-limit-global']);
        thisDialog.find('#peer-limit-per-torrent').val(system.serverConfig['peer-limit-per-torrent']);

        thisDialog.find('#blocklist-url').val(system.serverConfig['blocklist-url']).prop('disabled', !system.serverConfig['blocklist-enabled']);
        thisDialog.find('#system-conifg-button-update-blocklist').prop('disabled', !system.serverConfig['blocklist-enabled']);

        thisDialog.find('#seedRatioLimit').val(system.serverConfig['seedRatioLimit']).prop('disabled', !system.serverConfig['seedRatioLimited']);
        thisDialog.find('#queue-stalled-minutes').val(system.serverConfig['queue-stalled-minutes']).prop('disabled', !system.serverConfig['queue-stalled-enabled']);
        thisDialog.find('#idle-seeding-limit').val(system.serverConfig['idle-seeding-limit']).prop('disabled', !system.serverConfig['idle-seeding-limit-enabled']);

        thisDialog.find('#script-torrent-done-filename').val(system.serverConfig['script-torrent-done-filename']).prop('disabled', !system.serverConfig['script-torrent-done-enabled']);

        var etype = system.lang.dialog['system-config']['encryption-type'];
        for (var key in etype) {
          $('<option/>').val(key).attr('selected', (key == system.serverConfig['encryption'] ? true : false)).text(etype[key]).appendTo(thisDialog.find('#encryption'));
        }

        var tmp = system.serverConfig['download-dir-free-space'];
        if (tmp == -1) {
          tmp = system.lang.public['text-unknown'];
        } else {
          tmp = formatSize(tmp);
        }
        thisDialog.find('#download-dir-free-space').text(tmp);

        // Local dictionary folder list (not including transmission.downloadDirs)
        if (system.dictionary.folders == null || system.dictionary.folders == '') {
          thisDialog.find('#local-storage-dictionary-folders').val('');
        } else {
          thisDialog.find('#local-storage-dictionary-folders').val(system.dictionary.folders);
        }

        //thisDialog.find("#local-storage-dictionary-folders").val(transmission.downloadDirs);

        thisDialog.find('#show-bt-servers').switchbutton({
          checked: system.config.showBTServers
        });
        thisDialog.find('#hide-subfolders').switchbutton({
          checked: system.config.hideSubfolders
        });
        thisDialog.find('#simpleCheckMode').switchbutton({
          checked: system.config.simpleCheckMode
        });

        thisDialog.find('#ipInfoToken').val(system.config.ipInfoToken);

        for (var key in system.config.nav) {
          var value = system.config.nav[key];
          thisDialog.find('#nav-' + key).switchbutton({
            checked: value
          });
        }
      },
      // 初始化相关按钮事件
      initEvents: function () {
        // Cancel
        thisDialog.find('#system-config-button-cancel').click(function () {
          thisDialog.dialog('close');
        });
        /**/
        // 测试端口
        thisDialog.find('#system-config-test-port').click(function () {
          var button = $(this);
          button.linkbutton({
            text: system.lang.dialog['system-config'].testing,
            disabled: true
          });
          // 如果端口有变更，则先保存
          if (parseInt(thisDialog.find('#peer-port').val()) != system.serverConfig['peer-port']) {
            transmission.exec({
              method: 'session-set',
              arguments: {
                'peer-port': true
              }
            }, function (data) {
              if (data.result == 'success') {
                transmission.exec({
                  method: 'port-test'
                }, function (data) {
                  if (data.result == 'success') {
                    if (data.arguments['port-is-open']) {
                      thisDialog.find('#system-config-port-is-open-true').fadeInAndOut();
                    } else {
                      thisDialog.find('#system-config-port-is-open-false').fadeInAndOut();
                    }
                  }
                  button.linkbutton({
                    text: system.lang.dialog['system-config']['test-port'],
                    disabled: false
                  });
                });
              }
            });
          } else {
            transmission.exec({
              method: 'port-test'
            }, function (data) {
              if (data.result == 'success') {
                if (data.arguments['port-is-open']) {
                  thisDialog.find('#system-config-port-is-open-true').fadeInAndOut();
                } else {
                  thisDialog.find('#system-config-port-is-open-false').fadeInAndOut();
                }
                button.linkbutton({
                  text: system.lang.dialog['system-config']['test-port'],
                  disabled: false
                });
              }
            });
          }
        });

        // 保存参数
        thisDialog.find('#system-config-button-save').click(function () {
          var inputs = thisDialog.find('input');
          var config = {};
          var days = new Array();
          var value = null;
          var reloadPage = false;
          // 提交标签列表的变更
          if (page.endEditing()) {
            page.userLabelsTable.datagrid('acceptChanges');
          }
          for (var i = 0; i < inputs.length; i++) {
            var input = inputs[i];
            var configType = input.getAttribute('config-type') || 0;
            value = null;
            // 0 表示服务器配置，默认为0
            if (configType == 0 && input.id != undefined && input.id != '') {
              switch (input.type) {
                case 'checkbox':
                  switch (input.id) {
                    // 星期
                    case 'alt-speed-time-weekday-0':
                    case 'alt-speed-time-weekday-1':
                    case 'alt-speed-time-weekday-2':
                    case 'alt-speed-time-weekday-3':
                    case 'alt-speed-time-weekday-4':
                    case 'alt-speed-time-weekday-5':
                    case 'alt-speed-time-weekday-6':
                      days.push(input.checked ? 1 : 0);
                      break;
                    default:
                      value = input.checked;
                      break;
                  }
                  break;
                default:
                  switch (input.id) {
                    // 限制时间
                    case 'alt-speed-time-begin':
                    case 'alt-speed-time-end':
                      value = getMinutesFromHours(input.value);
                      break;

                    default:
                      value = ($.isNumeric(input.value) ? parseFloat(input.value) : input.value);
                      break;
                  }
                  break;
              }

              if (value != system.serverConfig[input.id] && value != null) {
                config[input.id] = value;
              }
            }
          }
          //console.log("days:",days);
          var sunday = days.pop();
          //console.log("days1:",days);
          days.unshift(sunday);
          value = parseInt(days.reverse().join(''), 2);
          if (value != system.serverConfig['alt-speed-time-day'] && value != null) {
            config['alt-speed-time-day'] = value;
          }
          value = thisDialog.find('#encryption').combobox('getValue');
          if (value != system.serverConfig['encryption'] && value != null) {
            config['encryption'] = value;
          }

          value = thisDialog.find('#download-dir').combobox('getValue');
          if (value != system.serverConfig['download-dir'] && value != null) {
            config['download-dir'] = value;
          }

          system.config.showBTServers = thisDialog.find('#show-bt-servers').prop('checked');
          system.config.hideSubfolders = thisDialog.find('#hide-subfolders').prop('checked');
          system.config.simpleCheckMode = thisDialog.find('#simpleCheckMode').prop('checked');
          system.config.ipInfoToken = thisDialog.find('#ipInfoToken').val();

          var _temp = JSON.stringify(system.config.nav);
          for (var key in system.config.nav) {
            system.config.nav[key] = thisDialog.find('#nav-' + key).prop('checked');
          }
          if (_temp != JSON.stringify(system.config.nav)) {
            reloadPage = true;
          }

          var cuslang = thisDialog.find('#system-config-lang').val();

          // If the parameters are changed, save
          if (!$.isEmptyObject(config)) {
            $(this).linkbutton({
              text: system.lang.dialog['system-config'].saving,
              disabled: true
            });
            // Start setting parameters
            transmission.exec({
              method: 'session-set',
              arguments: config
            }, function (data) {
              if (data.result == 'success') {
                // Once the settings are successful, regain the current parameters
                transmission.exec({
                  method: 'session-get'
                }, function (data) {
                  if (data.result == 'success') {
                    system.serverConfig = data.arguments;
                    $('#status_version').html(system.lang.statusbar.version + data.arguments['version']);
                    if (data.arguments['alt-speed-enabled'] == true) {
                      system.panel.toolbar.find('#toolbar_alt_speed').linkbutton({
                        iconCls: 'icon-alt-speed-true'
                      });
                      $('#status_alt_speed').show();
                    } else {
                      system.panel.toolbar.find('#toolbar_alt_speed').linkbutton({
                        iconCls: 'icon-alt-speed-false'
                      });
                      $('#status_alt_speed').hide();
                    }

                    system.downloadDir = data.arguments['download-dir'];
                    saveLocalData();

                    if (cuslang != system.lang.name) {
                      system.changeLanguages(cuslang);
                    }
                  }
                });
              }
            });
            return;
          }
          // If the language changes, reload the page
          else if (cuslang != system.lang.name) {
            system.changeLanguages(cuslang);
          } else {
            if (system.dictionary.folders == thisDialog.find('#local-storage-dictionary-folders').val()) {
              thisDialog.find('#system-config-nochange').fadeInAndOut();
              return;
            }
          }

          saveLocalData();

          // Save the local data
          function saveLocalData () {
            if (thisDialog.find('#local-storage-dictionary-folders').val().replace(/^\s*[\r\n]/gm, '').length == 0) {
              system.dictionary.folders = '';
            } else {
              system.dictionary.folders = thisDialog.find('#local-storage-dictionary-folders').val().replace(/^\s*[\r\n]/gm, '');
            }
            system.saveConfig();
            if (reloadPage) {
              location.href = location.href;
            } else {
              thisDialog.dialog('close');
            }
          }
        });

        // Update blacklist
        thisDialog.find('#system-conifg-button-update-blocklist').click(function () {
          var button = $(this);
          button.prop('disabled', true);
          transmission.updateBlocklist(function (data) {
            if (data.result == 'success') {
              thisDialog.find('#system-config-blocklist-size').html(system.lang.dialog['system-config']['blocklist-size'].replace('%n', data.arguments['blocklist-size']));
            }
            ;
            button.prop('disabled', false);
          });
        });

        // 恢复界面默认设置
        $('#restore-default-settings', thisDialog).click(function () {
          if (confirm(system.lang['public']['text-confirm']) == false) {
            return;
          }
          system.config = {};
          system.userConfig = {};
          system.saveConfig();
          system.saveUserConfig();

          location.href = location.href;
        });
      },
      // 初始化用户标签列表
      initUserLabels: function () {
        this.userLabelsTable = $('<table/>').appendTo($('#divLabels', thisDialog));
        var headContextMenu = null;
        var selectedIndex = -1;

        // 添加
        $('#btn-UserLabel-addNew', thisDialog).on('click', function () {
          if (page.endEditing()) {
            page.userLabelsTable.datagrid('appendRow', {
              'ck': false,
              'color': '#000000'
            });
            page.editIndex = page.userLabelsTable.datagrid('getRows').length - 1;
            page.userLabelsTable.datagrid('selectRow', page.editIndex).datagrid('beginEdit', page.editIndex);
          }
        });

        // 删除
        $('#btn-UserLabel-remove', thisDialog).on('click', function () {
          var checkedRows = page.userLabelsTable.datagrid('getChecked');
          if (checkedRows.length == 0) {
            return;
          }
          if (!confirm(system.lang['public']['text-confirm'])) {
            return;
          }

          page.userLabelsTable.datagrid('cancelEdit', page.editIndex);
          var removed = [];
          for (var index = checkedRows.length - 1; index >= 0; index--) {
            var item = checkedRows[index];
            var rowIndex = page.userLabelsTable.datagrid('getRowIndex', item);
            console.log(rowIndex);
            removed.push(rowIndex);
            page.userLabelsTable.datagrid('deleteRow', rowIndex);
            system.config.labels.splice(rowIndex, 1);
          }

          // 清除已打过标签的种子
          for (var key in system.config.labelMaps) {
            if (system.config.labelMaps.hasOwnProperty(key)) {
              var items = system.config.labelMaps[key];
              for (var index = items.length - 1; index >= 0; index--) {
                if ($.inArray(items[index], removed) != -1) {
                  items.splice(index, 1);
                }
              }
            }
          }

          system.saveConfig();
          system.control.torrentlist.refresh();
          system.resetNavLabels(true);
          page.editIndex = undefined;
        });

        // 导出
        $('#btn-UserLabel-export', thisDialog).on('click', function () {
          var data = {
            labels: system.config.labels,
            maps: system.config.labelMaps
          };
          var exportData = JSON.parse(JSON.stringify(data));

          saveFileAs('tr-web-control-labels.json', JSON.stringify(exportData));
        });

        // 导入
        $('#btn-UserLabel-import', thisDialog).on('click', function () {
          loadFileContent('json', function (content) {
            var item = content;
            if (typeof (content) === 'string') {
              try {
                item = JSON.parse(content);
              } catch (error) {
                alert(system.lang['public']['text-json-file-parsing-failed']);
                return;
              }
            }

            if (item.labels && item.maps) {
              if (confirm(system.lang.dialog['system-config']['labels-manage']['import-confirm'])) {
                system.config.labels = item.labels;
                system.config.labelMaps = item.maps;
                system.saveConfig();
                reloadData();
                system.control.torrentlist.refresh();
                system.resetNavLabels(true);
              }
            }
          });
        });

        function reloadData () {
          page.userLabelsTable.empty();
          $.get(system.rootPath + 'template/config-user-labels-fields.json?time=' + (new Date()), function (data) {
            var fields = data.fields;
            var _fields = {};
            for (var i = 0; i < fields.length; i++) {
              var item = fields[i];
              _fields[item.field] = item;
            }

            for (var key in fields) {
              var item = fields[key];
              var _field = _fields[item.field];
              if (_field && _field['formatter']) {
                item['formatter'] = _field['formatter'];
              }

              item.title = system.lang.dialog['system-config']['labels-manage'][item.field] || item.field;
              system.setFieldFormat(item);
            }

            page.userLabelsTable.datagrid({
              autoRowHeight: true,
              rownumbers: true,
              remoteSort: false,
              checkOnSelect: false,
              singleSelect: true,
              selectOnCheck: false,
              toolbar: $('#divLabelsToolbar', thisDialog),
              fit: true,
              striped: true,
              columns: [fields],
              data: system.config.labels,
              onSelect: function (rowIndex, rowData) {
                if (page.selectedIndex != -1) {
                  // page.userLabelsTable.datagrid("unselectRow", page.selectedIndex);
                }
                page.selectedIndex = rowIndex;
              },
              onUnselect: function (rowIndex, rowData) {
                page.selectedIndex = -1;
              },
              onClickCell: function (index, field) {
                if (page.editIndex != index) {
                  if (page.endEditing()) {
                    page.userLabelsTable.datagrid('selectRow', index).datagrid('beginEdit', index);
                    var ed = page.userLabelsTable.datagrid('getEditor', { index: index, field: field });
                    if (ed) {
                      ($(ed.target).data('textbox') ? $(ed.target).textbox('textbox') : $(ed.target)).focus();
                    }
                    page.editIndex = index;
                  } else {
                    setTimeout(function () {
                      page.userLabelsTable.datagrid('selectRow', page.editIndex);
                    }, 0);
                  }
                }
              },
              onEndEdit: function (index, row, changes) {
                if (row.name) {
                  if (row.ck !== undefined) {
                    delete row.ck;
                  }
                  system.config.labels[index] = row;
                  system.saveConfig();
                  system.control.torrentlist.refresh();
                  system.resetNavLabels(true);
                }
              }
            });
          }, 'json');
        };

        reloadData();

        $('#btn-export-config', thisDialog).on('click.cus', function () {
          page.exportConfig(this);
        });

        $('#btn-import-config', thisDialog).on('click', function () {
          page.importConfig(this);
        });
      },
      endEditing: function () {
        if (page.editIndex == undefined) {return true;}
        if (page.userLabelsTable.datagrid('validateRow', page.editIndex)) {
          page.userLabelsTable.datagrid('endEdit', page.editIndex);
          page.editIndex = undefined;
          return true;
        } else {
          return false;
        }
      },
      importConfig: function (button) {
        loadFileContent('json', function (content) {
          var item = content;
          if (typeof (content) === 'string') {
            try {
              item = JSON.parse(content);
            } catch (error) {
              alert(system.lang['public']['text-json-file-parsing-failed']);
              return;
            }
          }

          if (item.system || item.user || item.server || item.dictionary) {
            system.openDialogFromTemplate({
              id: 'dialog-import-config',
              options: {
                title: system.lang.dialog['import-config'].title,
                width: 500,
                height: 120
              },
              datas: {
                config: item
              },
              type: 1,
              source: $(button),
              onClose: function () {
                $(button).on('click', function () {
                  page.importConfig(this);
                });
              }
            });
          } else {
            alert(system.lang.dialog['import-config']['invalid-file']);
            // thisDialog.find("#system-config-nochange").html(system.lang.dialog["import-config"]["invalid-file"]).fadeInAndOut();
          }
        });
      },
      /**
       * 导出当前配置到文件
       */
      exportConfig: function (button) {
        system.openDialogFromTemplate({
          id: 'dialog-export-config',
          options: {
            title: system.lang.dialog['export-config'].title,
            width: 500,
            height: 120
          },
          type: 1,
          source: $(button),
          onClose: function () {
            $(button).on('click', function () {
              page.exportConfig(this);
            });
          }
        });
      }
    };
    page.init();
  })($('#dialog-system-config'));
</script>
