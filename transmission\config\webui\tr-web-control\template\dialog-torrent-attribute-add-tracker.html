<div class="easyui-layout" data-options="fit:true" style="width:100%;height:100%;">
	<div data-options="region:'center'" style="padding:3px;border:0px;">
		<div id="" class="dialog" style="width:100%;padding:0px;">
			<span id="dialog-torrent-attribute-add-tracker-tip" class="tip"></span>
			<textarea id="txtTrackers" style="width:99%;height:120px;"></textarea>
		</div>
	</div>
	<div data-options="region:'south',border:false" style="text-align:right;padding:6px;">
		<span id="text-nochange" style="display:none;"></span>
		<a id="torrent-attribute-add-tracker-button-search-best-ip" class="easyui-linkbutton" data-options="iconCls:'icon-search',plain:true" href="javascript:void(0);">Best Trackers</a>
		<a id="torrent-attribute-add-tracker-button-search-all-ip" class="easyui-linkbutton" data-options="iconCls:'icon-search',plain:true" href="javascript:void(0);">All Trackers</a>
		<a id="torrent-attribute-add-tracker-button-ok" class="easyui-linkbutton" data-options="iconCls:'icon-ok',plain:true" href="javascript:void(0);">Ok</a>
		<a id="torrent-attribute-add-tracker-button-cancel" class="easyui-linkbutton" data-options="iconCls:'icon-cancel',plain:true"
		   href="javascript:void(0);">Cancel</a>
	</div>
</div>
<script type="text/javascript">
	(function (thisDialog) {
		var title = "tip".split(",");
		var torrent = transmission.torrents.all[system.currentTorrentId];

		$.each(title, function (i, item) {
			thisDialog.find("#dialog-torrent-attribute-add-tracker-" + item).html(system.lang.dialog[
				"torrent-attribute-add-tracker"][item]);
		});

		thisDialog.find(".title").css({
			background: "#e6e6e6"
		});

		title = "button-search-best-ip,button-search-all-ip,button-ok,button-cancel".split(",");
		$.each(title, function (i, item) {
			thisDialog.find("#torrent-attribute-add-tracker-" + item).html(system.lang.dialog["public"][item]);
			thisDialog.find("#torrent-attribute-add-tracker-" + item).html(system.lang.dialog[
				"torrent-attribute-add-tracker"][item]);
		});

		thisDialog.find("#text-nochange").html(system.lang["public"]["text-nochange"]);

		// 确认
		thisDialog.find("#torrent-attribute-add-tracker-button-ok").click(function () {
			var urls = thisDialog.find("#txtTrackers").val();
			var button = $(this);

			if (urls != "") {
				var newTrackers = [];
				var items = urls.split("\n");
				$.each(items, function(index, value) {
					if (isURL(value) && newTrackers.indexOf(value)==-1) {
						newTrackers.push(value);
					}
				});
				if (newTrackers.length==0) {
					thisDialog.find("#text-nochange").html(system.lang["public"]["text-nochange"]).fadeInAndOut();
					return;
				}
				var icon = button.linkbutton("options").iconCls;
				button.linkbutton({
					disabled: true,
					iconCls: "icon-loading"
				});
				transmission.exec({
					method: "torrent-set",
					arguments: {
						ids: system.currentTorrentId,
						trackerAdd: newTrackers
					}
				}, function (data) {
					button.linkbutton({
						iconCls: icon,
						disabled: false
					});

					if (data.result == "success") {
						system.getTorrentInfos(system.currentTorrentId);
						thisDialog.dialog("close");
					}
				});
			} else {
				thisDialog.find("#text-nochange").html(system.lang["public"]["text-nochange"]).fadeInAndOut();
			}

		});

        thisDialog.find("#torrent-attribute-add-tracker-button-search-best-ip").click(function () {
            var trackersURL = "https://raw.githubusercontent.com/ngosang/trackerslist/master/trackers_best_ip.txt"
            $.get(trackersURL,function(data){
                $("#txtTrackers").html(data.replace(/[\r\n]+/g,"\n"));
            });
        });

        thisDialog.find("#torrent-attribute-add-tracker-button-search-all-ip").click(function () {
            var trackersURL="https://raw.githubusercontent.com/ngosang/trackerslist/master/trackers_all_ip.txt"
            $.get(trackersURL,function(data){
                $("#txtTrackers").html(data.replace(/[\r\n]+/g,"\n"));
            });
        });
        
		thisDialog.find("#torrent-attribute-add-tracker-button-cancel").click(function () {
			thisDialog.dialog("close");
		});

		function isURL(v) {
			return /^[a-zA-z]+:\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&amp;:/~\+#]*[\w\-\@?^=%&amp;/~\+#])?$/.test(v);
		}
	})($("#dialog-torrent-attribute-add-tracker"));
</script>